import { ADD_RESPONSE, ADD_SURVEY, ADD_CURRENT_NESTED_QUESTION } from '../actions/SurveyActionTypes';
import { cloneDeep } from 'lodash/lang';
import { findIndex } from 'lodash/array';

const InitialState = {
    Questions: [],
    Responses: [],
    CurrentNestedQuestion: {}
}

const RemoveExistingNestedResponse = ({
    Responses,
    ParentQuestionId
}) => {
    if (ParentQuestionId) {
        let FilteredResponses = Responses.filter(x => x.ParentQuestionId !== ParentQuestionId)
        let DeletedResponse = Responses.filter(x => x.ParentQuestionId === ParentQuestionId)
        return RemoveExistingNestedResponse({
            Responses: FilteredResponses || [],
            ParentQuestionId: DeletedResponse && DeletedResponse.length > 0 && DeletedResponse[0].QuestionID || null
        });
    } else {
        return Responses;
    }

}

const UpdateResponses = ({
    Responses,
    Payload,
    QuestionID,
    OptionID,
    Type,
    Checked
}) => {
    let index = -1;
    const { ParentQuestionId } = Payload;
    let UpdatedResponses = Responses;
    if (Type === 'single select') {
        UpdatedResponses = RemoveExistingNestedResponse({
            Responses,
            ParentQuestionId
        });
    }

    if (Type == 'multi-select') {
        if (UpdatedResponses && UpdatedResponses.length > 0) {
            index = UpdatedResponses && UpdatedResponses.findIndex(x => x.QuestionID === QuestionID && x.OptionID === OptionID);
        }

        if (index === -1) {
            return [...UpdatedResponses, Payload];
        }

        if (Checked) {
            return [...UpdatedResponses.slice(0, index), Payload, ...UpdatedResponses.slice(index + 1)];
        } else {
            return [...UpdatedResponses.slice(0, index), ...UpdatedResponses.slice(index + 1)];
        }

    } else {

        if (UpdatedResponses && UpdatedResponses.length > 0) {
            index = UpdatedResponses.findIndex(x => x.QuestionID === QuestionID)
        }

        // if no other response is present with QuestionID then it is appended
        if (index === -1) {
            return [...UpdatedResponses, Payload];
        }

        // else the deleted response will kill the child responses recursively
        let deletedResParQuestionId = null;
        if (UpdatedResponses && UpdatedResponses[index]) {
            deletedResParQuestionId = UpdatedResponses[index].QuestionID || null;
        }

        const DeletedChildResponses = RemoveExistingNestedResponse({
            ParentQuestionId: deletedResParQuestionId,
            Responses: UpdatedResponses
        });

        index = -1;
        if (DeletedChildResponses && DeletedChildResponses.length > 0) {
            index = DeletedChildResponses.findIndex(x => x.QuestionID === QuestionID)
        }
        if (index === -1) {
            return [...DeletedChildResponses, Payload];
        }
        return [...DeletedChildResponses.slice(0, index), Payload, ...DeletedChildResponses.slice(index + 1)];

    }
}

function SurveyReducer(state = InitialState, action = '') {

    switch (action.type) {

        case ADD_SURVEY: {
            const Payload = action.payload;
            return { ...state, Questions: Payload }
        }

        case ADD_RESPONSE: {
            const Payload = action.payload;
            const { QuestionID, OptionID, Checked, Type } = Payload;
            const { Responses } = state;
            const UpdatedResponse = UpdateResponses({
                Responses,
                Payload,
                QuestionID,
                OptionID,
                Type,
                Checked
            });
            return { ...state, Responses: UpdatedResponse }
        }

        case ADD_CURRENT_NESTED_QUESTION: {
            const Payload = action.payload;
            return { ...state, CurrentNestedQuestion: Payload }
        }

        default:
            return { ...state };
    }
}

export default SurveyReducer;