@import './variables.scss';

.rule-flex-container {
    display: flex;
    flex-wrap: wrap;
    margin-top: 50px;
    margin-left: 50px;

    .decision-box {
        border-bottom: 2px solid #ccc;
        margin-bottom: 30px;
        padding-bottom: 20px;
        width: 100%;
    }

    .tool-flex {
        display: flex;
        justify-content: flex-end;
        width: 100%;

        div {
            width: 40px;
        }
    }
}

.add-rulecase-wrapper {
    border-bottom: 2px dashed $form-label-color;
    border-top: 2px dashed $form-label-color;
    margin-top: 20px;
    padding: 20px 0;

    .selected-node-details-tree {
        display: flex;
        justify-content: space-between;

        .node-name {
            display: flex;
            h5 {
                color: #1ABB9C;          
                font-size: 16px;
                margin-left: -10px;
            }
            label {
                font-size: 16px;
            }
        }

        .node-level {
            display: flex;
            h5 {
                color: #1ABB9C;               
                font-size: 16px;
                margin-left: -10px;
            }
            label {
                font-size: 16px;
            }
        }
    }
}

.add-decision-step {
    display: flex;
    color: $form-label-color;
    font-family: $form-font-family;
    flex-wrap: wrap;
    justify-content: space-between;

    .step1 {
        margin-right: 10%;
    }

    .step2 {
        margin-right: 10%;
    }

    div {
        margin-bottom: 10px;
    }
}

.add-field-panel {
    display: flex;
    flex-direction: row;
    font-family: $form-font-family;
    justify-content: space-between;
    margin-top: 20px;

    div {
        flex-grow: 1;
        width: 100%;
    }

    .btn-container {
        margin-top: 27px;
    }

    &.half-width {
        width: 50%;
    }

    .multiselect-dropdown-fact {
        background-color: #fff;
        // border: 1px solid $form-border-color;
        border-radius: 4px;
        font-size: 16px;
        color: $form-field-color;
        height: 40px;
        margin-top: 8px;
        width: 95%;
    }

    .fact-error-actions {
        display: flex;
        flex-direction: column;
        justify-content: center;
        p {
            color: red;
            font-size: 14px;
            margin-left: 10px;
        }    
    }

    .select-search-dropdown {
        margin-top: 13px;
    }
     
}

 