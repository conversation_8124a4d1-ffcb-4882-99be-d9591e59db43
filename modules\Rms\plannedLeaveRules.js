
const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
var fs = require('fs');
let express = require('express');
var path = require('path');
const uuid = require('uuid');
const axios = require('axios');
const conf = require("../../env_config");
const { Base64Encode } = require('../../auth');
const CryptoJS = require("crypto-js");
const moment = require("moment");
const cache = require('memory-cache');
const _ = require('underscore');


const app = express();

const { Engine } = require('json-rules-engine')


/**
 * Setup a new engine
 */
let engine = new Engine()

// define a rule for detecting the player has exceeded foul limits.  Foul out any player who:
// (has committed 5 fouls AND game is 40 minutes) OR (has committed 6 fouls AND game is 48 minutes)

exports.plannedLeave = async function (req, res) {
  // engine.addRule({
  //   conditions: {
  //     any: [{
  //       all: [{
  //         fact: "leaveCategoryId",
  //         description: "For leaveCategoryId 2 (Earned leave, Casual Leave)",
  //         operator: "equal",
  //         // value: "today",
  //         value: 2,
  //       },
  //       {
  //         fact: "no_past_date",
  //         description: "Date should not be in the past",
  //         operator: "greaterThanInclusive",
  //         // value: "today",
  //         value: new Date().toISOString().split("T")[0],
  //       },
  //       {
  //         fact: "no_past_date",
  //         description: "Date should not be more than 45 days from today",
  //         operator: "lessThanInclusive",
  //         value: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString().split("T")[0],
  //       },
  //       {
  //         fact: "shrinkage",
  //         description: "maxShrinkage",
  //         operator: "lessThan",
  //         // value: "today",
  //         value: 5,
  //       },
  //       ],
  //     },
  //     {
  //       all: [{
  //         fact: "leaveCategoryId",
  //         description: "For leaveCategoryId 4 (work from home, Forgot to Punch)",
  //         operator: "equal",
  //         value: 4,
  //         // value: 'microsoft',
  //         // path: '$.status'
  //       }]
  //     },
  //     {
  //       all: [{
  //         fact: "leaveCategoryId",
  //         description: "For leaveCategoryId 2 (Sick Leave)",
  //         operator: "equal",
  //         // value: "today",
  //         value: 3,
  //       }]
  //     },
  //     ]
  //   },

  //   event: {  // define the event to fire when the conditions evaluate truthy
  //     type: "date-validation",
  //     params: {
  //       message: "Date is valid.",
  //     },
  //   }
  // })

  // Rule for Earned Leave, Casual Leave
   engine.addRule({
    conditions: {
      all: [
        {
          fact: "leaveCategoryId",
          operator: "equal",
          value: 2, // Earned Leave, Casual Leave
        },
        {
          fact: "no_past_date",
          operator: "greaterThanInclusive",
          value: new Date().toISOString().split("T")[0],
        },
        {
          fact: "no_past_date",
          operator: "lessThanInclusive", 
          value: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString().split("T")[0],
        },
        {
          fact: "shrinkageInfo",
          operator: "equal",
          value: [0,1],
          path: '$.status'
        },
      ],
    },
    event: {
      type: "earned-casual-leave-validation",
      params: {
        message: "Earned or casual leave application is valid.",
        maxShrinkage: 5,
      },
    },
  });

  // Rule for Work From Home, Forgot to Punch
  engine.addRule({
    conditions: {
      all: [
        {
          fact: "leaveCategoryId",
          operator: "equal",
          value: 4, // Work From Home, Forgot to Punch
        },
      ],
    },
    event: {
      type: "work-from-home-validation",
      params: {
        message: "Work from home leave application is valid.",
      },
    },
  });

  // Rule for Sick Leave
  engine.addRule({
    conditions: {
      all: [
        {
          fact: "leaveCategoryId",
          operator: "equal",
          value: 3, // Sick Leave
        },
      ],
    },
    event: {
      type: "sick-leave-validation",
      params: {
        message: "Sick leave application is valid.",
      },
    },
  });


  // [Rms].[GetSlotOnPlannedLeaves]

  // engine.addFact('staticData', (params, almanac) => {
  //   console.log("inside facts")
  //   // almanac.factValue()
  //   return { status: 4 }; // A simple static fact
  // });

  // engine.addFact('staticData', function (params, almanac) {
  //   console.log('loading account information...')
  //   return almanac.factValue('accountId')
  //     .then((accountId) => {
  //       return apiClient.getAccountInformation(accountId)
  //     })
  // })

  // engine.addFact("userDetails", async () => {
  //   const response = await axios.get("https://api.example.com/user/123");
  //   return {
  //     role: response.data.role,
  //     isActive: response.data.isActive,
  //   };
  // });
  
  engine.addFact('shrinkageInfo', function (params, almanac) {
    console.log('loading account information...')
    return almanac.factValue('shrinkage')
      .then(async() => {
        // return apiClient.getAccountInformation(accountId)
        try {
          let params = [];
          params.push({ key: "UserId", value: req.user.userId });
          // params.push({ key: "LeaveCategoryId", value: req.body.leaveCategoryId });
          params.push({ key: "ApplicationDate", value: req.body.leaveDate });

          let response = await sqlHelper.sqlProcedure("L", "[Rms].[GetSlotOnPlannedLeaves]", params);

          return response?.recordsets[0][0];
         
        }
        catch (e) {
          console.log(e);
          return res.send({
            status: 500,
            message: e
          });
        }
      })
  })
  


  /**
 * Define facts the engine will use to evaluate the conditions above.
 * Facts may also be loaded asynchronously at runtime; see the advanced example below
 */
  let facts = {
    // no_past_date: '2024-11-28',
    no_past_date: req.body.leaveDate,
    shrinkage: 0,
    leaveCategoryId: req.body.leaveCategoryId
  }


  // Run the engine to evaluate
  engine
    .run(facts)
    .then(async (results) => {
      // if (results.events.length > 0) {
      if (true) {
        // console.log(results.events[0].params.message); // Triggered rule message
        // console.log(facts);
        // if (facts.leaveCategoryId == 4) {
        try {
          let params = [];
          params.push({ key: "UserId", value: req.user.userId });    
          params.push({ key: "LeaveCategoryId", value: req.body.leaveCategoryId });
          params.push({ key: "LeaveType", value: req.body.leaveType });
          params.push({ key: "LeaveDate", value: req.body.leaveDate });
          let response = []
          response = await sqlHelper.sqlProcedure("L", "[Rms].[InsertAdvisorLeaves]", params);

          if (response?.recordsets) {
            return res.send({
              status: 200,
              message: 'success',
              results: response?.recordsets
            });
          }
          else {
            return res.status(500).json({
              status: 500
            });
          }
        }
        catch (e) {
          console.log(e);
          return res.send({
            status: 500,
            message: e
          });
        }
        // }
      } else {
        console.log("Date out of Range.");
      }
    })

}
