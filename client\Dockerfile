FROM node:18.17.1-alpine as build-step
WORKDIR /client
COPY package*.json /client/
RUN npm install
#COPY . /client
#COPY . .
#RUN npm install
COPY ./ /client/
RUN npm run build

# production environment
FROM nginx:1.25-alpine
COPY --from=build-step /client/build/ /usr/share/nginx/html
RUN rm /etc/nginx/conf.d/default.conf
#COPY ./nginx.conf /etc/nginx/conf.d
COPY --from=build-step /client/nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
