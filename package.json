{"name": "PBRms", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "local:qa": "set NODE_ENV=qa&& node app.js", "local:prod": "set NODE_ENV=prod&& node app.js"}, "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"athena-express": "^7.1.5", "aws-sdk": "^2.1445.0", "axios": "^1.4.0", "bcrypt": "^5.1.1", "compression": "^1.7.4", "content-security-policy": "^0.3.4", "cors": "^2.8.5", "crypto-js": "^4.1.1", "dont-sniff-mimetype": "^1.1.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-fileupload": "^1.4.0", "feature-policy": "^0.6.0", "hive-driver": "^0.2.0", "joi": "^17.9.2", "json-rules-engine": "^7.2.0", "jsontoxml": "^1.0.1", "memory-cache": "^0.2.0", "moment": "^2.29.4", "mongodb": "^3.1.13", "mssql": "^9.1.1", "mysql": "^2.18.1", "newrelic": "^11.18.0", "nodemailer": "^6.9.4", "read-excel-file": "^5.6.1", "referrer-policy": "^1.2.0", "request": "^2.88.2", "strict-transport-security": "^0.3.0", "underscore": "^1.13.6", "uuid": "^9.0.0", "x-xss-protection": "^2.0.0"}}