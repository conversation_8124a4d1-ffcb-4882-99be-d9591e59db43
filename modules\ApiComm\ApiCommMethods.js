
const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
var fs = require('fs');
let express = require('express');
var path = require('path');
const uuid = require('uuid');
const axios = require('axios');
const conf = require("../../env_config");



exports.Ticketing = function (req, res) {
    const url = conf.BMSTicket + "api/TicketSystem/GetMyTicketList"
    const body = {
        StartDate: req.query.startdate,
        EndDate: req.query.enddate,
        Type: 1,
        UserID: req.query.userid,
        UserName: req.query.UserName,
        EmployeeID: req.query.EmployeeID
    }
    const headers = {
        "content-type": conf.BMSContent, "token": conf.BMSToken,
        "app": conf.BMSApp
    }

    console.log(url);
    console.log(body);
    console.log(headers);

    try {
        axios.post(url, body, { headers: headers }).then(response => {
            try {
                if (response.status === 200) {
                    res.send({
                        status: 200,
                        data: response.data.Data
                    });
                }
                else {
                    res.send({
                        status: 500,
                        data: response
                    });
                }
            }
            catch (e) {

                res.send({
                    status: 500,
                    message: e
                });
            }

        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
}