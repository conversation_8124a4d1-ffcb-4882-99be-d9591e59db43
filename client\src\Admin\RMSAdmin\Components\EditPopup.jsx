import React
, { useContext, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { GetCommonData, InsertData, UpdateData, DeleteData } from "../../store/actions/CommonAction";
import { PopupContext } from '../Context/ConfigContext';
import Box from '@mui/material/Box';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

const EditPopup = (props) => {


    let type1 = props.type;

    let data = props.data;
    let name = props.name;
    let value = props.value;

    // let key= props.identifier;

    let popup = useContext(PopupContext);

    const [Value, setValue] = useState(value);
    const [selectedDate, setSelectedDate] = useState(null);
    const [type, setType] = useState(type1);

    const handleValueChange = (e, type) => {

        setValue(e.target.value);
    }

    const handleClose = () => {
        popup.setPopup();
    }


    const handleSave = (e, type) => {
        if (type == 'date') {
            popup.handleSave(data, selectedDate);
            popup.setPopup();
            return;
        }
        popup.handleSave(data, Value);
        popup.setPopup();
    }

    const handleDateChange = (date) => {
        debugger
        
        // console.log("in format ", moment(date.$d).format('YYYY-MM-DD'));
       
        setSelectedDate(date);
       
    }


    return (
        <Box>
            <Dialog open={popup.open} onClose={handleClose} aria-labelledby="form-dialog-title">
                <DialogTitle id="form-dialog-title">Configuration Data</DialogTitle>

                <DialogContent>
                    <DialogContentText>Customized Popup</DialogContentText>

                    <br />
                    {type === 'date' &&

                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                            <DatePicker
                                
                                // disableFuture
                                // openTo="year"
                                // format="yyyy-MM-dd"
                                label="YYYY-MM-DD"
                                value={selectedDate}
                                onChange={handleDateChange}
                                renderInput={(params) => <TextField {...params} />}
                            />
                        </LocalizationProvider>

                    }

                    {type !== 'date' &&
                        <TextField
                            autoFocus
                            id={name}
                            type={type}
                            margin="dense"
                            label={name}
                            onChange={(e) => handleValueChange(e, type)}
                            value={Value}
                        />
                    }
                </DialogContent>

                <DialogActions>
                    <Button variant="outlined" color="secondary" onClick={handleClose}>
                        Cancel
                    </Button>

                    <Button onClick={(e) => handleSave(e, type)} color="primary">
                        SAVE
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        InsertData,
        UpdateData,
        DeleteData
    }
)(EditPopup);