const tblList = {
    //Tables
    'Users': 'PBT.UserMaster',
    'UserMenu': 'CRM.UserMenuMap',
    'SurveyResponse': '[PBT].[SurveyResponse]',
    'SurveyMaster': '[PBT].[SurveyMaster]',
    'SurveyNestedQuestionMaster': '[PBT].[SurveyNestedQuestionMaster]',
    'SurveyQuestionMaster': '[PBT].[SurveyQuestionMaster]',
    'SurveyAgentMapping': '[PBT].[SurveyAgentMapping]',
    'ApplicationMaster' : '[PBT].[ApplicationMaster]',
    'FieldMaster' : '[PBT].[FieldMaster]',
    'SurveyOptionMaster': '[PBT].[SurveyOptionMaster]',
    'QuizMaster': '[PBT].[QuizMaster]',
    'QuizQuestionMaster': '[PBT].[QuizQuestionMaster]',
    'QuizFieldMaster' : '[PBT].[QuizFieldMaster]',
    'QuizOptionMaster': '[PBT].[QuizOptionMaster]',
    'QuizResponse': '[PBT].[QuizResponse]',
    'QuizNestedQuestionMaster': '[PBT].[QuizNestedQuestionMaster]',
    'Products': 'dbo.Products',
    'QuizAgentMapping' : '[PBT].[QuizAgentMapping]',
    'CourseMaster': '[PBT].[CourseMaster]',
    'CourseContentMaster': '[PBT].[CourseContentMaster]',
    'CourseContentTypeMaster': '[PBT].[CourseContentTypeMaster]',
    'ContestApplicationMaster': '[ENC].[ContestApplicationMaster]',
    'CourseQuizMaster' :'[PBT].[CourseQuizMaster]',
    'CourseQuizQuestionMaster' :'[PBT].[CourseQuizQuestionMaster]',
    'CourseQuizOptionMaster' :'[PBT].[CourseQuizOptionMaster]',
    'CourseQuizNestedQuestionMaster' :'[PBT].[CourseQuizNestedQuestionMaster]',
    'CourseQuizFieldMaster' :'[PBT].[CourseQuizFieldMaster]',
    'CourseAgentMapping' :'[PBT].[CourseAgentMapping]',
    'ChapterMaster':'[PBT].[CourseChapterMaster]',
    'CourseApplicationMaster': '[ENC].[CourseApplicationMaster]',
    'CategoryMaster':'[PBT].[CourseCategoryMaster]',
    'CourseCategoryProductMapping':'[PBT].[CourseCategoryProductMapping]',



    'LeaveTypemaster':'[RMS].[LeaveTypemaster]',
    'RosterMaster' : '[RMS].[RosterMaster]',
    'HolidayMaster' : '[RMS].[HolidayMaster]',
    'Configuration':'[RMS].[Configuration]',
    'GetSupervisorsByProcess': '[Rms].[GetSupervisorsByProcess]',
    'ProcessCategoryMaster':'[Rms].[ProcessCategoryMaster]',
    
    //Tables PBRMS
    // 'InsertLeaveRequest': '[RMS].[InsertLeaveRequest]',
    // 'GetLeaves' : '[RMS].[GetLeaves]',
    'RosterShrinkageMaster':'[RMS].[RosterShrinkageMaster]',
    'ProcessMaster':'[RMS].[ProcessMaster]',
    // 'GetLeaveStatus' : '[Rms].[GetLeaveStatus]',
    'CheackLeaveAvailability':'[Rms].[CheackLeaveAvailability]',
    'GetRosterDates':'[Rms].[GetRosterDates]',
    // 'CheckJagUser' : '[Rms].[CheckJagUser]',
    'ErrorMessage' : '[RMS].[ErrorMessage]',
    'ShrinkageEditCheck':'[RMS].[ShrinkageEditCheck]',
    // 'GetLeaveTakenAndSlotAvailibility' : '[RMS].[GetLeaveTakenAndSlotAvailibility]',
    'GetSupervisorsByProcessAndProduct': '[RMS].[GetSupervisorsByProcessAndProduct]',
    'TransferRequest' : '[RMS].[TransferRequest]',
    'HRMS_State' : '[RMS].[HRMS_StateMaster]',

    //Stored Procedures PB Training
    'CheckSurveyAgent': '[PBT].[CheckSurveyAgent]',
    'CheckQuizAgent': '[PBT].[CheckQuizAgent]',
    'UserQuizInfo': '[PBT].[UserQuizInfo]',
    'GetQuizReview' :  '[PBT].[GetQuizReview]',
    'InsertSkipData' : '[PBT].[InsertSkipData]',
    'GetQuizScore' : '[PBT].[GetQuizScore]',
    'InsertUpdateCategoryProductMapping':'[PBT].[InsertUpdateCategoryProductMapping]',
    'UpdateCourseMaster':'[PBT].[UpdateCourseMaster]',
    'GetChapterContentByCourse':'[PBT].[GetChapterContentByCourse]',
    'GetCourseQuizReview':'[PBT].[GetCourseQuizReview]',
    'GetCourseQuizScore':'[PBT].[GetCourseQuizScore]',
    // PB Contest
    'CheckContestAgent': '[ENC].[CheckContestAgent]',

    //Stored Procedures PB Training
    'InsertSurveyAgentMappingAll' : '[MTX].[InsertSurveyAgentMappingAll]',
    'GetAgentProcessOnProduct':'[MTX].[GetAgentProcessOnProduct]',
    'InsertSurveyAgentsProcess' : '[MTX].[InsertSurveyAgentsProcess]',
    'InsertQuizAgentsProcess' : '[MTX].[InsertQuizAgentsProcess]',
    
    //Stored Procedures IN PBRMS
    'GetProcessRosterDefaultShrinkage':'[RMS].[GetProcessRosterDefaultShrinkage]',
    'FetchRosterProducts':'[RMS].[FetchRosterProducts]',
    'GetProcessOnStateAndProduct':'[Rms].[GetProcessOnStateAndProduct]', 
    'GetSupervisorsOnState':'[Rms].[GetSupervisorsOnState]',
    'GetStates':'[Rms].[GetStates_v2]',
    'GetRecommendEmployeeData' : '[Rms].[GetRecommendEmployeeData]',
    'GetRotaWeeklySheet_v2' : '[Rms].[GetRotaWeeklySheet_v2]',
    // 'TLRosterStatusData':'[RMS].[TLRosterStatusData]',
    'CheckTLEmployeeId' : '[Rms].[CheckTLEmployeeId]',
    'GetTLRosterStatusDataMis' : '[Rms].[GetTLRosterStatusDataMis]',
    'GetShrinkageOnApplicationDate' : '[Rms].[GetShrinkageOnApplicationDate]',
    'LeaveStatus' : '[RMS].[GetLeaveStatus]',

    // Mongo Collections
    'History': 'UpdateHistory',
    'livechat_department': 'rocketchat_livechat_department',
    'livechat_department_agents': 'rocketchat_livechat_department_agents',
    'JagStoryBoard': 'JagStoryBoard',
    'roles':'rocketchat_roles',
    'DashboardLogs': 'MatrixDashboardLogs',
    'Settings' : 'Settings',
    'ContestAgents' : 'LotteryContestAgents',
    
};


const tblListWithParams = {
    'GetAgentLeavesByRoster':  {
        proc: '[RMS].[GetAgentLeavesByRoster_v6]',
        params: [
             {element: 'ManagerIds', from: ''},
             {element: 'RoleId', from: ''},
             {element: 'UserId', from: 'user', userKey: 'userId'},
             {element: 'RosterId', from: ''},
             {element: 'ProcessId', from: ''},
             {element: 'StateId', from: ''},
             {element: 'ProductId', from: ''},
             {element: 'CategoryId', from: ''},
             {element: 'RosterStartingDate', from: ''},
             {element: 'RosterEndingDate', from: ''}

        ]
    },
    'GetLeaves':  {
        proc: '[RMS].[GetLeaves_v2]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'},
        ]
    },
    'GetLeaveTakenAndSlotAvailibility_v2':  {
        proc: '[RMS].[GetLeaveTakenAndSlotAvailibility_v2]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'},
        ]
    },
    'InsertLeaveRequest':  {
        proc: '[RMS].[InsertLeaveRequest]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'},
            {element: 'LeaveType', from: ''},
            {element: 'LeaveDate', from: ''},
            {element: 'RequestLeave', from: ''},
            {element: 'AvailableDates', from: ''},
            {element: 'AppliedBy', from: 'user', userKey: 'userId'},
        ]
    },
    'CheckJagUser':  {
        proc: '[RMS].[CheckJagUser]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'}
            // {element: 'CurrentDate', from: ''}
        ]
    },
    'GetLeaveStatus':  {
        proc: '[RMS].[GetLeaveStatus]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'},
        ]
    },
    'RequestForLeave':  {
        proc: '[RMS].[RequestForLeave]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'},
        ]
    },
    'GetHolidays': {
        proc: '[RMS].[GetHolidays_v2]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'},
            {element: 'RosterId', from: ''},

        ]
    },
    'GetTLRosterStatusData': {
        proc: '[RMS].[GetTLRosterStatusData]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'},
        ]
    },
    // 'GetTLRosterStatusDataMis': {
    //     proc: '[RMS].[GetTLRosterStatusDataMis]',
    //     params: [
    //         {element: 'UserId', from: 'user', userKey: 'userId'},
    //         {element: 'ProductId', from: ''},
    //         {element: 'StateId', from: ''},
    //     ]
    // },
    'GetPermissions': {
        proc: '[Persona].[GetPermissions]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'},
            // {element: 'Menuid', from: ''},
        ]
    },
    'GetAgentRequestedLeave':  {
        proc: '[RMS].[GetAgentRequestedLeave]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'},
            // {element: 'RosterId', from: ''},
            // {element: 'StartDate', from: ''},
            // {element: 'EndDate', from: ''},
            // {element: 'ProcessId', from: ''},
            // {element: 'ManagerId', from: ''},
            // {element: 'SelectedStatus', from: ''}
        ]
    },
    'GetTotalShrinkage':  {
        proc: '[RMS].[GetTotalShrinkage]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'},

        ]
    },
    'GetMultiUsersManagerDetails':  {
        proc: '[RMS].[GetMultiUsersManagerDetails]',
        params: [
            {element: 'ApproverId', from: 'user', userKey: 'userId'},
            {element: 'RosterId', from: ''},
            {element: 'UserIds', from: ''},
            {element: 'ManagerId', from: ''}
        ]
    },
    'GetSingleUsersManagerDetails':  {
        proc: '[RMS].[GetSingleUsersManagerDetails]',
        params: [
            {element: 'ApproverId', from: 'user', userKey: 'userId'},
            // {element: 'RosterId', from: ''},
            {element: 'UserIds', from: ''},
            {element: 'ManagerId', from: ''}
        ]
    },
}

module.exports = {
    ...tblList,
    ...tblListWithParams
};