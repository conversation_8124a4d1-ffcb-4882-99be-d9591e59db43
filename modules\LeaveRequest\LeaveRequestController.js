const tblList = require("../constants");
const methods = require("./LeaveRequestMethods");
let fs = require('fs');
const path = require("path");
const moment = require("moment");


async function leaveAppliedByManager(req, res) {
    try {
        await methods.leaveAppliedByManager(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
}


module.exports = {
    
    leaveAppliedByManager: leaveAppliedByManager,
   
};