const sql = require("mssql");
const conf = require("../env_config");

async function sqlquery(connectionstring, sqlquery, userInput) {
  const AWSSecretConfig = global.SecretConfig;
  // console.log('Inside sqlquery pbcroma');
  let dbConfig = AWSSecretConfig.RSQL_URL;
  if (connectionstring == "L") {
    dbConfig = AWSSecretConfig.SQL_URL;
  } else if (connectionstring == "A") {
    dbConfig = AWSSecretConfig.ANALYTICSLIFE_SQL_URL;
  }
  
  const pool = new sql.ConnectionPool(dbConfig);
  pool.on('error', err => {
    // ... error handler 
    console.log('sql pool error db.js', err);
  });

  try {
    await pool.connect();
    let result = await pool.request();
    // console.log("SQL Query Start time: ", new Date() );
    for (let key in userInput) {      
      result = await result.input(userInput[key].key, userInput[key].value);
    };
    result = await result.query(sqlquery);
    // console.log("SQL Query End time: ", new Date() );
    return result;
  } catch (err) {
    // stringify err to easily grab just the message
    let e = JSON.stringify(err, ["message", "arguments", "type", "name"]);
    console.log(err, sqlquery, userInput);
    return [{ error: JSON.parse(e).message }];
  } finally {
    pool.close(); //closing connection after request is finished.
  }

}


async function sqlProcedure(connectionstring, sqlquery, userInput) {
  const AWSSecretConfig = global.SecretConfig;
  // console.log('Inside sqlquery pbcroma');
  let dbConfig = AWSSecretConfig.RSQL_URL
  if (connectionstring == "L") {
    dbConfig = AWSSecretConfig.SQL_URL;
  } else if (connectionstring == "A") {
    dbConfig = AWSSecretConfig.ANALYTICSLIFE_SQL_URL;
  }
  const pool = new sql.ConnectionPool(dbConfig);
  pool.on('error', err => {
    // ... error handler 
    console.log('sql pool error db.js', err);
  });

  try {
    await pool.connect();
    let result = await pool.request();

    for (let key in userInput) {
      //console.log(userInput);
      // if ( Array.isArray(userInput[key]) ) {  
      //   // input(field_name, dataType, value)
      //   result = await result.input(key, "@"+userInput[key].key, userInput[key].value);
      // } else { 
      // input(field_name, value)
      //result.parameters()
      // console.log(key)

      if (userInput[key].type == "out") {
        //console.log("Inside out")
        result = await result.output(userInput[key].key, userInput[key].value);
      }
      else {
        result = await result.input(userInput[key].key, userInput[key].value);
      }
      //};
    };
    // console.log("SQL Query Start time: ", new Date() );
    result = await result.execute(sqlquery);
    // console.log("SQL Query End time: ", new Date() );
    
    return result;
  } catch (err) {
    // stringify err to easily grab just the message
    console.log(err, sqlquery, userInput);
    let e = JSON.stringify(err, ["message", "arguments", "type", "name"]);
    return [{ error: JSON.parse(e).message }];
  } finally {
    pool.close(); //closing connection after request is finished.
  }

}

module.exports = {
  sqlquery: sqlquery,
  sqlProcedure: sqlProcedure
};
