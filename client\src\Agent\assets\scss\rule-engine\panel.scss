@use "sass:map";
@import './variables.scss';

.panel-wrapper {
    border: 1px solid #ccc;
    border-radius: 12px;
    margin-top: 9px;
    padding: 3px 20px 20px;
}

.panel-box-wrapper {    
    background-color: #fff;
    border: none;
    box-shadow: 0 1px 15px #0000000a, 0 1px 6px #0000000a;
    border-radius: 8px;
    display: flex;
    font-family: $tab-font-family;
    font-size: 14px;
    flex-wrap: wrap;
    margin-top: 13px;
    padding: 10px;
    align-items: center;
    color: #253858;

    &.string-type {
        // border-left: 5px solid map-get($attribute-type, "string");
    }

    &.number-type {
        // border-left: 5px solid map-get($attribute-type, "number");
    }

    &.date-type {
        // border-left: 5px solid map-get($attribute-type, "date");
    }

    &.object-type {
        // border-left: 5px solid map-get($attribute-type, "object");
    }

    &.boolean-type {
        // border-left: 5px solid map-get($attribute-type, "boolean");
    }

    &.array-type {
        // border-left: 5px solid map-get($attribute-type, "array");
    }

    div {
        margin: 0 20px;
        width: 10%;
     }
 
    .index {
         width: 1%;
    }
 
    .name {
         width: 25%;
    }
 
    .type {
         width: 15%;
    }
    
    .type-badge {
        background-color: #4556ac;
        border-radius: 12px;
        color: white;
        font-size: 12px;
        padding: 2px 6px;
        
    }

    .menu {
        margin-left: 20px;
        width: auto;
   }
 
    a {
        color: $link-color;
        cursor: pointer;
        font-family: $tab-font-family;
        font-size: 14px;
        margin-right: 30px;
        text-decoration: none;;
    }
 
    a:hover {
        text-decoration: underline;
    }
}

.title-panel {
    border: 1px solid #ccc;
    border-radius: 5px;
    box-shadow: 0px 0px 6px 3px #eee;
    font-family: $title-font-family;
    // height: 300px;
    padding: 20px;
    width: 50%;

    .title {
        display: flex;
    
        h3 {
            color: $page-title-color;
            font-size: 1.25rem;
            font-weight: 500;
            margin-top: 10px;
            margin-left: 10px;
        }
    }
}

.banner {
    border-radius: 5px;
    display: flex;
    font-family: $form-font-family;
    justify-content: space-between;
    padding: 20px 20px;
}

.submit-panel {
    background-color: $panel-bg-color;
    border: 2px dashed $succees;
    color: $form-field-color;
}

.warning-panel {
    background-color: $panel-bg-color;
    border: 2px dashed $warning-dark;
    color: $form-field-color;
}

.banner-container {
    margin-left: 15px;
    margin-top: 20px;
}