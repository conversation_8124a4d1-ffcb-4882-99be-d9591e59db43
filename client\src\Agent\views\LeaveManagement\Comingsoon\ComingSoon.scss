* {
    margin: 0px;
    padding: 0px;
}

.theme-light {
    background-color: #fff;
}
.comingSoon{
    background-image: url("../../../../../public/Lms/ComingSoonBG.jpeg"); 
    background-position: center;
    background-size: cover;
    width: 100%;
    height: 100vh;
    h1{
        position: absolute;
        color: #fff;
        left: 0px;
        -webkit-backdrop-filter: blur(5px);
        backdrop-filter: blur(2px);
        right: 0px;
        bottom: 0px;
        background-color: #000000bf;
        top: 0px;
        margin: auto;
        font-family: 'Roboto';
        width: 100%;
        display: flex;
        font-size: 105px;
        font-weight: bold;
        text-align: center;
        align-items: center;
        justify-content: center;
        font-style: italic;
        text-shadow: 1px 8px #000000;
    }
}

@media screen and (max-width: 767px){
    .comingSoon{
        h1{
            font-size: 48px;
        }
    }
}