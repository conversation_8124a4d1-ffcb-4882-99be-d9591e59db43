
const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
var fs = require('fs');
let express = require('express');
var path = require('path');
const uuid = require('uuid');
const axios = require('axios');
const conf = require("../../env_config");



exports.UpdateFOSCityAssignment = async function (req, res) {
    const url = conf.MATRIXCOREAPI + "/fos/api/FOS/UpdateFosCity"
    const { AgentId, Data} = req.body
  
    const headers = {
        "AgentId": AgentId,
        "Source": "dashboard",
        "Content-Type": "application/json",
        "authkey": conf.FOSAUTHKEY,
        "clientkey": conf.FOSCLIENTKEY

    }
    console.log("UpdateFOSCityAssignment", url);

    let response = await axios.post(url, Data ,{headers:headers});
    console.log("UpdateFOSCityAssignment", response);
    try {

        res.send(response.data);

    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
}


