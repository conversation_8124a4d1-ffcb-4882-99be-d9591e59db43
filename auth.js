const axios = require("axios");
const cache = require('memory-cache');
const CryptoJS = require("crypto-js");
const conf = require("./env_config");
const { GetParsedConfigFromCache, GetParsedConfigLocal } = require('./modules/common/CommonMethods');

// async function checkSettings() {

//   let cachedSetting = cache.get('Settings');
//   if (cachedSetting) {
//     return cachedSetting;
//   } else {
//     let data = await matrixdb.collection('Settings').findOne({ 'application': 'matrixdashboard' });
//     cache.put('Settings', data, (60 * 60 * 1000));
//     return data;
//   }
// }

function parseCookies(request) {
  var cookies = {};
  request.headers && request.headers.cookie && request.headers.cookie.split(';').forEach(function (cookie) {
    var parts = cookie.match(/(.*?)=(.*)$/);
    cookies[parts[1].trim()] = (parts[2] || '').trim();
  });
  return cookies;
}

// async function fetchAllowURLS() {
//   let cachedAllowURLs = cache.get('AllowURLs');
//   if (cachedAllowURLs) {
//     return cachedAllowURLs;
//   } else {
//     let data = await matrixdb.collection('AllowURL').find({}).toArray();
//     cache.put('AllowURLs', data, (60 * 60 * 1000));
//     return data;
//   }
// }

function createLog(agentId, Method, Channel, query, ResponseText, err, IP) {
  try {
    let logData = {};
    let responseTime = new Date().toISOString();

    logData.TrackingID = agentId
    logData.Exception = err.toString();
    logData.Method = Method;
    logData.Application = "MatrixDashboard";
    logData.Channel = Channel;
    logData.RequestText = JSON.stringify(query);
    logData.ResponseText = JSON.stringify(ResponseText)
    logData.Requesttime = responseTime;
    logData.Responsetime = responseTime;
    logData.CreatedOn = responseTime;
    logData.CreatedBy = "MatrixDashboard";
    logData.IP = IP;

    loggerdb.collection('Log_Collection').insertOne(logData);
  }
  catch (e) {
    // console.log(e);
  }
}

function Base64Decoding(value) {
  if(value) {
    const decoded = CryptoJS.enc.Base64.parse(value);
    const details = decoded.toString(CryptoJS.enc.Utf8);
    return details;
  } else {
    return "{}";
  }
  
}

function Base64Encode(value) {
  if(value) {
    const encoded = CryptoJS.enc.Utf8.parse(value);
    const details = CryptoJS.enc.Base64.stringify(encoded);
    return details;
  } else {
    return "{}";
  }
}

async function ValidateToken({ url, body, name }) {
  try {
    const { userId, employeeId, token } = body;
    if (!employeeId || !token) {
      // console.log("ValidateToken", 1);
      return false;
    }

    let cachedToken = cache.get(employeeId);
    let result = false, response = null;

    if (name === 'mtx') {
      let data = {
        userid: userId,
        token: token
      };

      if (!cachedToken) {
        response = await axios.post(url, data);
        if (response && response.data) {
          result = true;
          cache.put(employeeId, data, (8 * 60 * 60 * 1000));
        }
      } else {
        if (!(body.token) || !(cachedToken.token)) {
          result = false;
        } else if (body.token !== cachedToken.token) {
          response = await axios.post(url, data);
          if (response && response.data) {
            result = true;
            cache.put(employeeId, data, (8 * 60 * 60 * 1000));
          }
        } else {
          result = true;
        }
      }

    } else if (name === 'bms') {
      let data = {
        UserId: userId,
        Token: token
      };

      if (!cachedToken) {
        response = await axios.post(url, data);
        if (response && response.IsLoggedIn) {
          result = true;
          cache.put(employeeId, data, (8 * 60 * 60 * 1000));
        }
      } else {
        if (!(body.token) || !(cachedToken.token)) {
          result = false;
        } else if (body.token !== cachedToken.Token) {
          response = await axios.post(url, data);
          if (response && response.IsLoggedIn) {
            result = true;
            cache.put(employeeId, data, (8 * 60 * 60 * 1000));
          }
        } else {
          result = true;
        }
      }
    } else if (name === 'dialer') {
      let data = {
        jwt: token
      };

      if (!cachedToken) {
        response = await axios.post(url, data);
        if (response && response.data) {
          result = true;
          cache.put(employeeId, data, (8 * 60 * 60 * 1000));
        }
      } else {
        if (!(body.token) || !(cachedToken.jwt)) {
          result = false;
        } else if (body.token !== cachedToken.jwt) {
          response = await axios.post(url, data);
          if (response && response.data) {
            result = true;
            cache.put(employeeId, data, (8 * 60 * 60 * 1000));
          }
        } else {
          result = true;
        }
      }
    } else if (name === 'claim') {
      let claimUrl = `${url}/${employeeId}/${token}`;
      let dataToken = {
        token: token
      }

      let claimHeaders = {
        SourceKey: conf.SOURCE_KEY_CLAIM,
        ValidationKey: conf.VALIDATION_KEY_CLAIM
      }

      // console.log("ValidateToken claim", 2);
      // console.log("ValidateToken claim", claimUrl);
      if (!cachedToken) {
        response = await axios.get(claimUrl, { headers: claimHeaders });
        const { status, headers, data } = response;
        const logResponse = { status, headers, data };
        // console.log('claim response ', response);
        createLog(employeeId, "ClaimResponse", "Authentication", claimUrl, logResponse, "", "")
        if (response && response.data && response.data.Data) {
          result = true;
          cache.put(employeeId, dataToken, (8 * 60 * 60 * 1000));
        }
      } else {
        if (!(body.token) || !(cachedToken.token)) {
          result = false;
        } else if (body.token !== cachedToken.token) {
          response = await axios.get(claimUrl, { headers: claimHeaders });
          const { status, headers, data } = response;
          const logResponse = { status, headers, data };
          // console.log('claim response', response);
          createLog(employeeId, "ClaimResponse", "Authentication", claimUrl, logResponse, "", "")
          if (response && response.data && response.data.Data) {
            result = true;
            cache.put(employeeId, dataToken, (8 * 60 * 60 * 1000));
          }
        } else {
          result = true;
        }
      }
    }

    return result;

  } catch (err) {
    console.log('Inside ValidateToken', err);
    return false;
  }

}

function getMtxPayload(cookie) {
  try {
    let details = "{}", body = {};
    if (cookie) {
      details = Base64Decoding(cookie);
    }

    // console.log("getMtxPayload", cookie, details)

    const { UserId, AsteriskToken, EmployeeId } = JSON.parse(details);
    return {
      userId: UserId,
      employeeId: EmployeeId,
      token: AsteriskToken
    }
  } catch (err) {
    console.log(err);
    return {
      userId: "",
      employeeId: "",
      token: ""
    }
  }
}

function getBmsPayload(cookie, userId) {
  try {
    // console.log("getBmsPayload", cookie, userId)
    return {
      userId: userId,
      employeeId: "BMS_" + userId,
      token: cookie
    }

  } catch (err) {
    console.log(err);
    return {
      userId: "",
      employeeId: "",
      token: ""
    }
  }

}

function getDialerPayload({ payload, userId }) {
  // console.log("getDialerPayload", payload, userId)
  return {
    userId: userId,
    employeeId: userId,
    token: payload
  }
}

function getClaimPayload({ payload, userId }) {

  //console.log("getClaimPayload", payload, userId)

  return {
    userId: userId,
    employeeId: userId,
    token: payload
  }
}

async function ValidateUser(req, AgentId) {
  let err;
  let IP = (req.headers['x-forwarded-for'] || '').split(',').pop().trim() || req.socket.remoteAddress;

  try {
    let IsValid = false;
    const mtxCookie = parseCookies(req)[conf.UUID_MTX];
    const bmsCookie = parseCookies(req)[conf.UUID_BMS];
    const dialerCookie = parseCookies(req)[conf.UUID_DIALER];
    const claimCookie = parseCookies(req)[conf.UUID_CLAIM];

    const mtxUrl = conf.VERIFY_TOKEN_MTX;
    const bmsUrl = conf.VERIFY_TOKEN_BMS;
    const dialerUrl = conf.VERIFY_TOKEN_DIALER;
    const claimUrl = conf.VERIFY_TOKEN_CLAIM;

    const userIdDialer = parseCookies(req)[conf.AGENTID_DIALER];
    const userIdClaim = parseCookies(req)[conf.AGENTID_CLAIM];

    const validationArr = [
      { url: mtxUrl, cookie: mtxCookie, name: 'mtx' },
      { url: dialerUrl, cookie: dialerCookie, name: 'dialer' },
      { url: claimUrl, cookie: claimCookie, name: 'claim' },
      { url: bmsUrl, cookie: bmsCookie, name: 'bms' },
    ]

    for (let i = 0; i < validationArr.length; i++) {
      let { url, cookie, name } = validationArr[i];
      let userId = '', token = '', employeeId = '';
      if (name === 'mtx') {

        userId = getMtxPayload(cookie).userId;
        employeeId = getMtxPayload(cookie).employeeId;
        token = getMtxPayload(cookie).token;

      } else if (name === 'bms') {

        userId = getBmsPayload(cookie, AgentId).userId;
        employeeId = getBmsPayload(cookie, AgentId).employeeId;
        token = getBmsPayload(cookie, AgentId).token;

      } else if (name === 'dialer') {

        userId = getDialerPayload({ payload: cookie, userId: userIdDialer }).userId;
        employeeId = getDialerPayload({ payload: cookie, userId: userIdDialer }).employeeId;
        token = getDialerPayload({ payload: cookie, userId: userIdDialer }).token;

      } else if (name === 'claim') {

        userId = getClaimPayload({ payload: cookie, userId: userIdClaim }).userId;
        employeeId = getClaimPayload({ payload: cookie, userId: userIdClaim }).employeeId;
        token = getClaimPayload({ payload: cookie, userId: userIdClaim }).token;
      }

      let body = { userId, employeeId, token };

      isValid = await ValidateToken({ url, body, name });
      createLog(userId, "VERIFY_TOKEN", url, body, IsValid, "", IP);
      if (isValid) {
        req['user'] = body;
        break;
      }
      req['user'] = { userId : '8324', employeeId, token };;
    }

    return isValid;
  } catch (e) {
    console.log(e);
    err = e;
    return false;
  }

}

async function Auth(req, res, next) {
  try {
    if (process.env.ENVIRONMENT_MTX_DASH === 'DEV_MTX_DASH') {
      const SecretConfig = await GetParsedConfigLocal();
      global.SecretConfig = SecretConfig;
      // console.log('Inside Auth ENV: ', process.env.ENVIRONMENT_MTX_DASH)
    } else {
      const SecretConfig = await GetParsedConfigFromCache({ key: 'ENV_CONFIG' });
      global.SecretConfig = SecretConfig;
    }

    if (req.originalUrl.includes('/health.html')) {
      res.status(200).send("health page");
      return;
    }
    let isAllowURL = false;

    let isValid = false;
    let AgentId = null;
    AgentId = parseCookies(req)['AgentId'];
    let IP = (req.headers['x-forwarded-for'] || '').split(',').pop().trim() || req.socket.remoteAddress;

    let reqdata = {
      headers: req.headers,
      method: req.method,
      url: req.url,
      httpVersion: req.httpVersion,
      body: req.body,
      cookies: req.cookies,
      path: req.path,
      protocol: req.protocol,
      query: req.query,
      hostname: req.hostname,
      ip: req.ip,
      originalUrl: req.originalUrl,
      params: req.params,
    }

    // let AllowURL = await fetchAllowURLS();
    // AllowURL.forEach(async function (val, key) {
     
    //   if (req.url.includes(val.url)) {
    //     isAllowURL = true;
    //   }
    // });
    // createLog(parseCookies(req)['AgentId'], "ValidateUser", "Authentication", reqdata, isAllowURL, "", IP)

    // if (isAllowURL) {
    //   isValid = true;
    // }
    // else {
      isValid = await ValidateUser(req, AgentId);
    // }

    // console.log("env", process.env.ENVIRONMENT);
    if (isValid || process.env.ENVIRONMENT_MTX_DASH === "DEV_MTX_DASH") {
      next();
    }
    else {
      res.status(401).send("Please login in Matrix Or BMS to see this page or contact your administrator.")
    }

  } catch (err) {
     console.log('Inside Auth', err);
    res.status(500).send("Auth Error");
  }
}

module.exports = {
  Auth: Auth,
  Base64Decoding: Base64Decoding,
  Base64Encode: Base64Encode,
  ValidateUser: ValidateUser,
  ValidateToken: ValidateToken,
  // fetchAllowURLS: fetchAllowURLS,
  createLog: createLog,
  parseCookies: parseCookies,
  // checkSettings: checkSettings
}