import { React, useState, useEffect } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction" // needed for dayClick

import {
  GetCommonData, GetCommonspData, GetCommonspDataV2
} from "../../store/actions/CommonAction";
import { getRMSUser } from '../../utility/utility';


import { connect } from "react-redux";
import { Modal, Box, Grid} from "@mui/material";
import LeaveApplicationPopUp from "./LeaveApplicationPopUp"
import moment from 'moment';
import CloseIcon from '@mui/icons-material/Close';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import ThumbDownIcon from '@mui/icons-material/ThumbDown';
import { Opacity } from "@mui/icons-material";


const ApplyLeave = (props) => {
  const { EmployeeId, deviceType, processId } = props;
  const [leaves, setLeaves] = useState();
  const [leavePopUp, setLeavePopUp] = useState(false);
  const [rosterData, setRosterData] = useState();
  const [calendarLoad, setCalendarLoad] = useState(0);
  const [selectedDate, setSelectedDate] = useState();
  const [events, setEvents] = useState();
  const [disabledDates, setDisabledDates] = useState([]);
  const [festive, setFestive] = useState([]);
  const [openErrorPopUp, setOpenErrorPopUp] = useState(false);
  const [ErrorMessage, setErrorMessage] = useState('');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [lastMonthFirstDate, setLastMonthFirstDate] = useState();
  const [lastMontLastDate, setLastMonthLastDate] = useState();

  useEffect(() => {
    console.log(window.location)
    props.GetCommonspDataV2({
      root: 'CheckJagUser',
      c: "R",
    }, function (errorStatus, data) {
      if (!errorStatus && data) {
        let item = data.data[0]
        setRosterData(item);
        setCalendarLoad(1);
      }
    });

  }, [])

  useEffect(() => {
    if (rosterData && rosterData[0]?.Id) {
      props.GetCommonData({
        root: 'GetHolidays',
        c: "R",
        params: [{ RosterId: rosterData[0]?.Id }],
      }, function (result) {
        if (result) {
          let data = result;
          setFestive(data || []);
        }
      });
    }
  }, [rosterData])

  useEffect(() => {
    if (rosterData) {
      GetLeaves();
    }

  }, [rosterData])


  useEffect(() => {
    GetLeaveStatus();
  }, [festive])

  const extractFirstAlphabet = (sentence) => {
    const words = sentence.split(' '); // Split the sentence into words
    const firstLetters = words.map(word => word.charAt(0)); // Extract the first character (alphabet) of each word
    return firstLetters.join(''); // Join the extracted first letters into a single string
  }

  const GetLeaveStatus = () => {
    props.GetCommonspDataV2({
      root: 'GetLeaveTakenAndSlotAvailibility_v2',
      // params: [{ currentDate: currentDate }],
      c: "R",
    }, function (errorStatus, result) {
      if (!errorStatus) {
        let arr = [];
        if (result && result.data && result.data[0].length > 0) {
          let data = result.data[0];
          // let disabledate = []

          for (let i = 0; i < data.length; i++) {
            if (data[i].LeaveTakenDate) {
              let title = '';
              if (deviceType == 'Mobile') {
                title = extractFirstAlphabet(data[i].LeaveName);
              }
              else {
                if(data[i].LeaveType == 1)
                {
                  title = data[i].Abbreviation;
                }else{
                  title = data[i].Abbreviation + (data[i].Status == 3 ? '- P' : '- A');
                }
              }
              arr.push({ title: title, start: moment(data[i].LeaveTakenDate).format('YYYY-MM-DD'), backgroundColor: data[i].Color, 
                className: data[i].Status === 3 ? 'low-opacity-event center-text' : 'center-text' 
              });
            }
            else if (data[i].IsRoster == 1 && data[i].SlotFull <= 0) {
              let title = '';
              deviceType == 'Mobile' ? title = 'NoSlot' : title = 'W/O - No Slots';
              arr.push({ title: title, start: moment(data[i].MonthlyDate).format('YYYY-MM-DD'), backgroundColor: "#f76767" });
            }
          }
          setDisabledDates(result.data[0]);
        }
        for (let j = 0; j < festive.length; j++) {
          arr.push({ title: festive[j].HolidayName, start: moment(festive[j].HolidayDate).format('YYYY-MM-DD'), backgroundColor: "green", fontSize: "10px" });
        }
        setEvents(arr);
      }

    });
  }

  const GetLeaves = () => {
    props.GetCommonspDataV2({
      root: 'GetLeaves',
      c: "R",
    }, function (errorStatus, result) {
      if (!errorStatus) {
        if (result && result.data && result.data[0].length > 0) {
          // Get user's role from localStorage or props
          const userRole = getRMSUser();

          const isAllowedForLeaveType1 = userRole?.RoleId != 13 ? true : false;
          
          let leavesData = result.data[0];
          let leaves = [];
          
          for (let i = 0; i < leavesData.length; i++) {
            // Skip leave type 2 if user's role is not 13
            if (leavesData[i].Id == 1 && isAllowedForLeaveType1) {
              continue;
            }
            
            if (leavesData[i].Id == 1) {
              if (rosterData[0].Enabled) {
                leaves.push(leavesData[i]);
              }
            } else {
              leaves.push(leavesData[i]);
            }
          }
          setLeaves(leaves);
        }
      }
    });
  }


  const ErrorCheck = async (ErrorCode) => {
    await props.GetCommonData({
      limit: 10,
      skip: 0,
      root: 'ErrorMessage',
      con: [{ "IsActive": 1 }, { "ErrorCode": ErrorCode }],
      c: "R",
    }, function (data) {
      if (data && data.length > 0) {

        setErrorMessage(data[0].Message);
        setOpenErrorPopUp(true);
        setLeavePopUp(false);
      }
    });
  }


  const handleDateClick = (event) => {
    //----------
    // if (lastMonthFirstDate > event.dateStr || lastMontLastDate < event.dateStr) {
    //   setErrorMessage("Please Select Valid Date");
    //   setOpenErrorPopUp(true);
    //   return;
    // }
    //---------
    setSelectedDate(event.dateStr);

    if (event.dateStr)
      for (let i = 0; i < disabledDates.length; i++) {
        if (disabledDates[i].LeaveTakenDate && (disabledDates[i].LeaveTakenDate).includes(event.dateStr) && disabledDates[i].IsRoster != 1) {
          // alert("Already leave Applied on this Date!");
          // ErrorCheck(405)
          setErrorMessage(disabledDates[i].Message);
          setOpenErrorPopUp(true);
          setLeavePopUp(false);
          return;
          // return false; // Prevents the default behavior of dateClick (e.g., selecting the date)
        }
      }
    if (leaves) {
      for (let i = 0; i < leaves.length; i++) {
        if (leaves[i].LeaveCount >= 1) {
          setLeavePopUp(true)
          break;
        }
        else {
          setLeavePopUp(false)
        }
      }
    }
  };

  const handleEventClick = (event) => {
    for (let i = 0; i < disabledDates.length; i++) {
      if (disabledDates[i].LeaveTakenDate && moment(disabledDates[i].LeaveTakenDate).format('YYYY-MM-DD') == moment(event.event.start).format('YYYY-MM-DD')
      && disabledDates[i].IsRoster != 1) {
        // alert("Already leave Applied on this Date!");
        // ErrorCheck(405)
        setErrorMessage(disabledDates[i].Message);
        setOpenErrorPopUp(true);
        setLeavePopUp(false);
        return;
        // return false; // Prevents the default behavior of dateClick (e.g., selecting the date)
      }
      else {
        for (let i = 0; i < festive.length; i++) {
          if (moment(festive[i].HolidayDate).format('YYYY-MM-DD') == moment(event.event.start).format('YYYY-MM-DD')) {

            setSelectedDate(festive[i].HolidayDate)
            setLeavePopUp(true);
            return;
          }
        }
      }
    }
    setSelectedDate(event.event.startStr);
    setLeavePopUp(true);
  }

  const handleViewChange = (viewInfo) => {
    setCurrentDate(moment(viewInfo.view.currentStart).format('YYYY-MM-DD'));

    let currentDate = new Date(); // Get the current date
    currentDate.setMonth(currentDate.getMonth() - 1); // Subtract one month
    currentDate.setDate(1); // Set the day of the month to 1
    console.log(currentDate);
    setLastMonthFirstDate(moment(currentDate).format('YYYY-MM-DD'));

    currentDate = new Date(); // Get the current date
    currentDate.setMonth(currentDate.getMonth() + 2); // Add two months to go to the next month
    currentDate.setDate(0);
    setLastMonthLastDate(moment(currentDate).format('YYYY-MM-DD'));
  };

  const handleSelected = (event) => {
    // setSelected(event);
    console.log(event);
  };

  const handleClose = () => {
    GetLeaves();
    GetLeaveStatus();
    setLeavePopUp(false);
  }

  const dayCellClassNames = (date) => {

    let interval = true
    if (moment.utc(date.date).local(true) >= moment(rosterData[0]?.StartDate).subtract(1, 'days') && moment(date.date) <= moment(rosterData[0]?.EndDate)) {
      interval = false
    }

    return interval ? 'disableDate' : 'RosterDate';
  };

  const handleCloseErrorPopUp = () => {
    setOpenErrorPopUp(false);
  }

  const handleEventMount = (eventInfo) => {
    console.log(eventInfo.event.title);
    eventInfo.el.setAttribute("title", eventInfo.event.title);
  };

  return (
    <>
      {calendarLoad == 1 ?
        <Grid container spacing={2}>
          <Grid item xs={12} md={3} sm={3} className="leave-balance">
            {leaves && leaves.length > 0 && <div className="leftSide">
              <p>LEAVE BALANCE</p>
              <ul>
                {
                  leaves.map((leave) => (
                    <>
                      {/* <li><span className={leave.LeaveType && leave.LeaveType.split(" ").length > 0 && leave.LeaveType.split(" ").join("")}>{leave.LeaveCount}</span></li> */}
                      {leave && ([4, 5].indexOf(leave.LeaveCategoryId) == -1) &&
                        <>
                          {leave.LeaveCategoryId == 1 ? <><li><span className={leave.LeaveType} style={{ background: leave.Color }}>{leave.LeaveCount}</span></li><li>{leave.LeaveType}</li></>
                          :
                          <><li><span className={leave.LeaveType} style={{ background: leave.Color }}>{leave.LeaveCount > 0 ? <ThumbUpIcon></ThumbUpIcon> : <ThumbDownIcon></ThumbDownIcon>}</span></li><li>{leave.LeaveType}</li></>
                          }
                          <hr />
                        </>}
                    </>
                  ))
                }
              </ul>
            </div>}
          </Grid>
          <Grid item xs={12} md={9} sm={9} className="calender-details">
            <div className="my-calendar">

              {/* <p className="noSlot"><span></span>No Slots Available</p> */}
              <FullCalendar
                plugins={[dayGridPlugin, interactionPlugin, timeGridPlugin]}
                initialView="dayGridMonth"
                dateClick={handleDateClick}
                datesSet={(info) => handleViewChange(info)}
                headerToolbar={{
                  center: "title",
                  left: "prev,next",
                  right: "",
                }}
                themeSystem="Simplex"
                events={events}
                eventClick={handleEventClick}
                dayCellClassNames={dayCellClassNames}
                firstDay={1}
                eventDidMount={handleEventMount} // Add tooltip on hover
              />

            </div>
          </Grid>
        </Grid> : <Grid></Grid>}
      {<LeaveApplicationPopUp open={leavePopUp} leaves={leaves} rosterData={rosterData} GetLeaves={GetLeaves} EmployeeId={EmployeeId}
        selectedDate={selectedDate} disabledDates={disabledDates} processId = {processId}
        lastMonthFirstDate={lastMonthFirstDate} lastMontLastDate={lastMontLastDate}
        handleClose={handleClose}
      />}

      <Modal
        open={openErrorPopUp}
        onClose={handleCloseErrorPopUp}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box className="LeaveApplicationPopup ErrorMsgPopup">
          <header>
            <p className="noSlot">{ErrorMessage}</p>
            <CloseIcon onClick={handleCloseErrorPopUp} className="closebtn" />
          </header>
        </Box>
      </Modal>
    </>
  );

}

function mapStateToProps(state) {
  return {
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    GetCommonspDataV2
  }
)(ApplyLeave);
