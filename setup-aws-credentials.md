# AWS Credentials Setup

## Option 1: Environment Variables
Set these environment variables in PowerShell:
```powershell
$env:AWS_ACCESS_KEY_ID="your_access_key"
$env:AWS_SECRET_ACCESS_KEY="your_secret_key"
$env:AWS_REGION="ap-south-1"
```

## Option 2: AWS Credentials File
Create a file at `%USERPROFILE%\.aws\credentials`:
```
[default]
aws_access_key_id = your_access_key
aws_secret_access_key = your_secret_key
region = ap-south-1
```

## Option 3: Local Development with CONNECTION Environment Variable
Set the CONNECTION environment variable with your database configuration:
```powershell
$env:CONNECTION='{"SQL_URL_RMS":{"user":"username","password":"password","server":"RMSSQL-DB.Etechaces.com","database":"database_name","port":1433,"encrypt":true,"trustServerCertificate":true}}'
$env:ENVIRONMENT_MTX_DASH="DEV_MTX_DASH"
```

Then run your application:
```powershell
node app.js
```
