import { lazy } from 'react';
import Loadable from '../../components/Loadable';


const AppTable = Loadable(lazy(() => import('./tables/AppTable')));
const AppForm = Loadable(lazy(() => import('./forms/AppForm')));
const AppButton = Loadable(lazy(() => import('./buttons/AppButton')));
const AppIcon = Loadable(lazy(() => import('./icons/AppIcon')));
const AppProgress = Loadable(lazy(() => import('./AppProgress')));
const AppMenu = Loadable(lazy(() => import('./menu/AppMenu')));
const AppCheckbox = Loadable(lazy(() => import('./checkbox/AppCheckbox')));
const AppSwitch = Loadable(lazy(() => import('./switch/AppSwitch')));
const AppRadio = Loadable(lazy(() => import('./radio/AppRadio')));
const AppSlider = Loadable(lazy(() => import('./slider/AppSlider')));
const AppDialog = Loadable(lazy(() => import('./dialog/AppDialog')));
const AppSnackbar = Loadable(lazy(() => import('./snackbar/AppSnackbar')));
const AppAutoComplete = Loadable(lazy(() => import('./auto-complete/AppAutoComplete')));
const AppExpansionPanel = Loadable(lazy(() => import('./expansion-panel/AppExpansionPanel')));
const Analytics= Loadable(lazy(()=>import('../../views/dashboard/Analytics')));
const AppEchart= Loadable(lazy(()=>import('../../views/charts/echarts/AppEchart')));
const ShrinkageEditor= Loadable(lazy(()=>import('../../../RMSAdmin/ShrinkageEditor')));
const AttendanceDashboard = Loadable(lazy(()=>import('../../../RMSAdmin/AttendanceDashboard')));
const ConfigurationEditor= Loadable(lazy(()=>import('../../../RMSAdmin/ConfigurationEditor')));
const AddConfiguration = Loadable(lazy(()=>import('../../../RMSAdmin/Components/AddConfiguration')));
// const Approval = Loadable(lazy(()=>import('../../../RMSAdmin/Approval')));
const RosterUserHistoryUpload= Loadable(lazy(()=>import('../../../RMSAdmin/RosterUserHistoryUpload')));
const SystemApplyLeaves= Loadable(lazy(()=>import('../../../RMSAdmin/SystemApplyLeaves')));
const RosterConfirmation= Loadable(lazy(()=>import('../../../RMSAdmin/Confirmation/RosterConfirmation')));
const History = Loadable(lazy(()=>import('../../../RMSAdmin/Confirmation/History')));
const Mis = Loadable(lazy(()=>import('../../../RMSAdmin/Confirmation/Mis')));
// const MisHistory = Loadable(lazy(()=>import('../../../RMSAdmin/Confirmation/MisHistory')));
const AdminDashboard= Loadable(lazy(()=>import('../../../RMSAdmin/AdminDashboard/AdminDashboard')));
const Approval = Loadable(lazy(()=>import('../../../RMSAdmin/Approvaldashboard/Approval')));
const MultiApproval = Loadable(lazy(()=>import('../../../RMSAdmin/Approvaldashboard/MultiApproval')));
const SingleApproval = Loadable(lazy(()=>import('../../../RMSAdmin/Approvaldashboard/SingleApproval')));


const materialRoutes = [
  { path: '/material/table', element: <AppTable /> },
  { path: '/material/form', element: <AppForm /> },
  { path: '/material/buttons', element: <AppButton /> },
  { path: '/material/icons', element: <AppIcon /> },
  { path: '/material/progress', element: <AppProgress /> },
  { path: '/material/menu', element: <AppMenu /> },
  { path: '/material/checkbox', element: <AppCheckbox /> },
  { path: '/material/switch', element: <AppSwitch /> },
  { path: '/material/radio', element: <AppRadio /> },
  { path: '/material/slider', element: <AppSlider /> },
  { path: '/material/autocomplete', element: <AppAutoComplete /> },
  { path: '/material/expansion-panel', element: <AppExpansionPanel /> },
  { path: '/material/dialog', element: <AppDialog /> },
  { path: '/material/snackbar', element: <AppSnackbar /> },
  {path: '/dashboard/default',
  element: <Analytics />},
  {
    path: '/charts/echarts',
    element: <AppEchart />,
  },
  {
    path:'/RosterShrinkage',
    element:<ShrinkageEditor/>
  },
  {
    MenuId : 2,
    path:'/AttendanceDashboard',
    element:<AttendanceDashboard/>
  },
  {
    path:'/Configurations',
    element:<ConfigurationEditor/>
  },

  {
    path:'/AddConfiguration',
    element: <AddConfiguration/>
  },
  {
    path:'/Approval',
    element: <Approval/>
  },
  {
    MenuId : 5,
    path:'/UploadRosterHistory',
    element: <RosterUserHistoryUpload/>
  },
  {
    path:'/SystemApplyLeaves',
    element: <SystemApplyLeaves/>
  },
  {
    MenuId : 3,
    path:'/RosterConfirmation',
    element: <RosterConfirmation/>
  },
  {
    MenuId : 6,
    path:'/History',
    element: <History/>
  },
  {
    path:'/AdminDashboard',
    element: <AdminDashboard/>
  },
  {
    MenuId : 4,
    path:'/Mis',
    element: <Mis/>
  },
  // {
  //   path:'/MisHistory',
  //   element: <MisHistory/>
  // },
  {
    path:'/Approval',
    element: <Approval/>
  },
  {
    path:'/MultiApproval',
    element: <MultiApproval/>
  },
  // {
  //   path:'/SingleApproval',
  //   element: <SingleApproval/>
  // },
];

export default materialRoutes;
