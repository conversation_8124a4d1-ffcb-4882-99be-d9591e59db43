var express = require("express");
// const rateLimit = require('express-rate-limit');
const router = express.Router();
const controller = require("./RmsController");

// Rate limiting configuration for leave submission
// const leaveSubmissionLimiter = rateLimit({
//     windowMs: 60 * 1000, // 1 minute
//     max: 10, // Limit each user to 10 leave requests per minute
//     message: JSON.stringify({ 
//         error: 'Too many leave requests. Please wait a minute before submitting again.' 
//     }),
//     standardHeaders: true,
//     legacyHeaders: false,
//     keyGenerator: (req) => {
//         // Log IP information
//         const ip = req.headers['x-forwarded-for']?.split(',')[0]?.trim() || 
//                  req.connection?.remoteAddress || 
//                  req.socket?.remoteAddress ||
//                  (req.connection?.socket ? req.connection.socket.remoteAddress : 'unknown-ip');
        
//         const userIdentifier = req.user?.userId || 'anonymous';
//         const requestKey = `${ip}:${userIdentifier}`;
        
//             console.log('Rate limiter - New request:', {
//                 timestamp: new Date().toISOString(),
//                 path: req.originalUrl,
//                 method: req.method,
//                 userAgent: req.headers['user-agent'],
//                 ipDetails: {
//                     xForwardedFor: req.headers['x-forwarded-for'],
//                     connectionRemoteAddress: req.connection?.remoteAddress,
//                     socketRemoteAddress: req.socket?.remoteAddress,
//                     connectionSocketRemoteAddress: req.connection?.socket?.remoteAddress,
//                     finalIpUsed: ip
//                 },
//                 userId: userIdentifier,
//             rateLimitKey: requestKey
//             });

//         return requestKey;
//     },
//     handler: (req, res) => {
//         const ip = req.headers['x-forwarded-for']?.split(',')[0]?.trim() || 
//                  req.connection?.remoteAddress || 
//                  'unknown-ip';
        
//         console.log('Rate limit exceeded:', {
//             timestamp: new Date().toISOString(),
//             ip: ip,
//             userId: req.user?.userId || 'anonymous',
//             path: req.originalUrl
//         });
        
//         res.status(429).json({
//             status: 429,
//             message: 'Too many leave requests. Please wait before submitting again.'
//         });
//     }
// });


// router.get("/createRoster", controller.CreateRoster);
// router.get("/createRosterv2", controller.CreateRosterv2);
router.get("/RmsRedirectionToUrl", controller.RmsRedirectionToUrl);
router.get("/DownloadRotaData", controller.DownloadRotaData);
router.post("/InsertRosterHistory", controller.InsertRosterHistory);
router.post("/InsertLeaveRequest", controller.InsertLeaveRequest);
router.get("/ApplyAutoLeave", controller.ApplyAutoLeave);
router.post("/InsertTransferRequest", controller.InsertTransferRequest);
router.post("/updateAdvisorTransferData", controller.updateAdvisorTransferData);
router.post("/addAdvisorsProcess", controller.addAdvisorsProcess);
router.get("/FreezedTlData", controller.FreezedTlData);
router.get("/getTLRosterData", controller.getTLRosterData);
router.get("/MenuAuthentication", controller.MenuAuthentication)
router.get("/GetUserMenus", controller.GetUserMenus)
router.post("/updateAdvisorRemoveRequest", controller.updateAdvisorRemoveRequest);
router.get("/GetRosterData", controller.GetRosterData);
router.post("/updateLeaveApproval", controller.updateLeaveApproval);
router.post("/updateLeaveRejection", controller.updateLeaveRejection);
router.post("/updateLeaveCancellation", controller.updateLeaveCancellation);
router.post("/LeaveAppliedByManager", controller.leaveAppliedByManager);
router.get("/getAdvisorData", controller.getAdvisorData);
router.post("/validationPlannedLeave", controller.validationPlannedLeave);
// Apply rate limiting to leave submission
router.post("/plannedLeaveRules", controller.plannedLeave);

// router.get("/createRoster", controller.CreateRoster);
module.exports = router;
