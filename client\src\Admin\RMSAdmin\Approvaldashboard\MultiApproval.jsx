import React, { useEffect, useState } from 'react';
import { Grid, Table, TableBody, TableContainer, TableCell, TableHead, TableRow, Checkbox, Select, FormControl, InputLabel, MenuItem } from '@mui/material';

import "../Approvaldashboard/ApprovalDashboard.scss"
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import CircleIcon from '@mui/icons-material/Circle';
import { getUrlParameter } from '../../../Agent/utility/utility.jsx'
import {
  GetCommonData, InsertData, UpdateData, GetCommonspData, GetCommonspDataV2, updateLeaveApproval
} from "../../store/actions/CommonAction";
import { connect } from "react-redux";
import moment from 'moment';
import _ from 'underscore';
import { useNavigate } from "react-router-dom";

const MultiApproval = (props) => {

  const selectedRoster = { label: "Test", value: "Test" }
  const [agentsData, setAgentsData] = useState();
  const [selectedData, setSelectedData] = useState({});
  const [userSelectedData, setUserSelectedData] = useState([]);


  const navigate = useNavigate();
  useEffect(() => {
    getData();
  }, [])

  const getData = () => {
    let UserId = getUrlParameter('UserId');
    let RosterId = getUrlParameter('RosterId');
    let ManagerId = getUrlParameter('ManagerId');
    console.log(UserId);
    try {
      props.GetCommonspDataV2({
        root: 'GetMultiUsersManagerDetails',
        c: "R",
        params: [{ UserIds: UserId || 0, RosterId: RosterId, ManagerId: ManagerId }],

      }, function (errorStatus, response) {
        if (!errorStatus) {
          console.log(response.data[0])
          let arr = response.data[0]
          let GroupedData = _.groupBy(arr, (item) => {
            return item.UserId;
          })
          console.log(GroupedData);
          setAgentsData(GroupedData);

        } else {
          //
        }
      })
    }
    catch (e) {

    }
  }

  const ApproveLeave = (e, User) => {
    console.log(e.target.checked);
    console.log(User);
    let updatedData = JSON.parse(JSON.stringify(selectedData))
    if(!Array.isArray(updatedData[User.UserId])){
      updatedData[User.UserId] =[];
    }
    if(e.target.checked){
      // let obj = User.UserId;
      updatedData[User.UserId].push(User.Id)
    }
    else if(!e.target.checked){
      const index = updatedData[User.UserId].indexOf(User.Id);
      if (index > -1) { // only splice array when item is found
        updatedData[User.UserId].splice(index, 1); // 2nd parameter means remove one item only
      }
      
    }
    // if(!ArrayData.includes(agentsData[e.target.value])){
      
    // }
    setSelectedData( updatedData);
    // let arr =  _.groupBy(ArrayData, (item) => {
    //     return item.UserId;
    //   })
      console.log(Object.values(updatedData));
  }

  const ApproveAllLeave = (e,User) => {
    console.log(e.target.checked);
    let updatedData = JSON.parse(JSON.stringify(selectedData))
    // if(e.target.checked){
      // console.log(selectedData.includes(User));
      if(!Array.isArray(updatedData[User[0].UserId])){
        updatedData[User[0].UserId] =[];
      }
      if(e.target.checked){
        // let obj = User.UserId;
        for(let i = 0; i < agentsData[User[0].UserId].length; i++){
          console.log(agentsData[User[i].UserId][i])
          if(! updatedData[User[0].UserId].includes(agentsData[User[0].UserId][i].Id)){
            updatedData[User[0].UserId].push(User[i].Id)
          }
        }
      }
      else if(!e.target.checked){
        // let obj = User.UserId;
        for(let i = 0; i < agentsData[User[0].UserId].length; i++){
          console.log(agentsData[User[i].UserId][i])
          // if(! updatedData[User[0].UserId].includes(agentsData[User[0].UserId][i].Id)){
            updatedData[User[0].UserId].pop(User[i].Id)
          // }
        }
      }
      console.log(updatedData)
      setSelectedData(updatedData);
      // console.log(agentsData[e.target.value]);
      // let Userdata = agentsData[e.target.value];
    //   for(let i = 0; i < Userdata.length; i++){
    //     if(!ArrayData.includes(Userdata[i].Id)){
    //       ArrayData.push(Userdata[i].Id)
    //     }
    //   }
    // }
    // else if(!e.target.checked){
    //   console.log(agentsData[e.target.value]);
    //   let Userdata = agentsData[e.target.value];
    //   for(let i = 0; i < Userdata.length; i++){
    //     const index = ArrayData.indexOf(Userdata[i].Id);
    //     if (index > -1) { // only splice array when item is found
    //       ArrayData.splice(index, 1); // 2nd parameter means remove one item only
    //     }
    //   }
    // }
    // setSelectedData(ArrayData);
  }

  const agentCalender = (e, Emid) => {
    console.log(e.target.value)
    // let Emid = e.target.value
    let url = document.location.origin + `/admin/rmsredirection?target=L2FnZW50L0xlYXZlTWFuYWdlbWVudC8=&EmployeeId=${Emid}&Mode=m`;
    window.open(url, "_blank");
  }

  const LeaveApproval = (ApprovalValue) => {
    let leaveIds = Object.values(selectedData).flat();
    console.log(leaveIds);
    let body = { 
      leaveIds : leaveIds,
      ApprovalValue: ApprovalValue
    }
    updateLeaveApproval(body, function (results) {
      debugger;
      if (results && results.data && results.data.status == 200) {
        if(results.data.result = 1){
          getData();
        }
      }
    })
  }

  return (
    <>
      <div className="approvalDashboard pd25">
        <Grid container>
          <Grid item xs={12} md={12}>
            <p className="pageStatus mt-1">Dashboard</p>
            <img src="/Lms/vistalogo.svg" />

          </Grid>

          <Grid item xs={12} md={12}>

            {agentsData && Object.values(agentsData).map((User, index) => (
              <>
                <div className="multiApproval">
                  <div className="RosterDetails">
                    <ul>
                      <li>Emp Name <strong>{User[0].EmployeeName}</strong></li>
                      <li>E-Code <strong>{User[0].EmployeeId}</strong></li>
                      <li>TL Name <strong>{User[0].TLName}</strong></li>
                    </ul>
                    <button onClick={(e)=>agentCalender(e, User[0].EmployeeId)}>Calendar <OpenInNewIcon /></button>
                  </div>

                  <div className="ActiveRoster">
                    <div className="ActiveRosterDate"><CircleIcon /><h4>Active Roster: </h4> <p>{moment(User[0].StartDate).format('DD MMM YY')} - {moment(User[0].EndDate).format('DD MMM YY')}</p></div>
                    <TableContainer className="ApprovalDetails" style={{ maxHeight: '400px' }}>
                      <Table stickyHeader aria-label="customized table" >
                        <TableHead>
                          <TableRow>
                            <TableCell align="left"><Checkbox className="RequestCheck" onChange={(e) => ApproveAllLeave(e,User)} />  Request No.</TableCell>
                            <TableCell align="left">Application Date</TableCell>
                            <TableCell align="left">Current Shrinkage</TableCell>
                            <TableCell align="left">Shrinkage(On Approval)</TableCell>
                            <TableCell align="left">Application Type</TableCell>

                          </TableRow>
                        </TableHead> 
                        <TableBody>
                          {User && User.length > 0 && User.map((data) => (
                            <TableRow>
                              {console.log((selectedData[data.UserId]?.includes(data.Id)))}
                              <TableCell align="left"><Checkbox onChange={(e) => ApproveLeave(e,data)} checked={(
                                // selectedData 
                                // && Array.isArray(selectedData[data.UserId])
                                // && 
                                (selectedData[data.UserId]?.includes(data.Id)))? true : false}/>  {data.Id}</TableCell>
                              <TableCell align="left">{moment(data.ApplicationDate).format('DD MMMM')}</TableCell>
                              <TableCell align="left">32%</TableCell> 
                              <TableCell align="left">32%</TableCell>
                              <TableCell align="left">{data.LeaveType}</TableCell>

                            </TableRow>
                          )
                          )}

                          {/* <TableRow>
                        <TableCell align="left"><Checkbox />  02</TableCell>
                        <TableCell align="left">12th October</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">SL</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell align="left"><Checkbox />  03</TableCell>
                        <TableCell align="left">12th October</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">SL</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell align="left"><Checkbox />  04</TableCell>
                        <TableCell align="left">12th October</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">SL</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell align="left"><Checkbox />  05</TableCell>
                        <TableCell align="left">12th October</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">SL</TableCell>
                      </TableRow> */}


                        </TableBody>
                      </Table>

                    </TableContainer>
                  </div>
                </div>
              </>
            ))}




            {/* <div className="multiApproval">
              <div className="RosterDetails">
                <ul>
                  <li>Emp Name <strong>Gunjan Sharma</strong></li>
                  <li>E-Code <strong>ET01843</strong></li>
                  <li>TL Name <strong>Narender Rastogi</strong></li>
                </ul>
                <button>Calendar <OpenInNewIcon /></button>
              </div>

              <div className="ActiveRoster">
                <div className="ActiveRosterDate"><CircleIcon /><h4>Active Roster: </h4> <p>02 Oct 2023 - 15 Oct 2023</p></div>
                <TableContainer className="ApprovalDetails" style={{ maxHeight: '400px' }}>
                  <Table stickyHeader aria-label="customized table" >
                    <TableHead>
                      <TableRow>
                        <TableCell align="left"><Checkbox className="RequestCheck" />  Request No.</TableCell>
                        <TableCell align="left">Application Date</TableCell>
                        <TableCell align="left">Current Shrinkage</TableCell>
                        <TableCell align="left">Shrinkage(On Approval)</TableCell>
                        <TableCell align="left">Application Type</TableCell>

                      </TableRow>
                    </TableHead>
                    <TableBody>

                      <TableRow>
                        <TableCell align="left"><Checkbox />  01</TableCell>
                        <TableCell align="left">12th October</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">SL</TableCell>

                      </TableRow>
                      <TableRow>
                        <TableCell align="left"><Checkbox />  02</TableCell>
                        <TableCell align="left">12th October</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">SL</TableCell>
                      </TableRow> 
                      <TableRow>
                        <TableCell align="left"><Checkbox />  03</TableCell>
                        <TableCell align="left">12th October</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">SL</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell align="left"><Checkbox />  04</TableCell>
                        <TableCell align="left">12th October</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">SL</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell align="left"><Checkbox />  05</TableCell>
                        <TableCell align="left">12th October</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">32%</TableCell>
                        <TableCell align="left">SL</TableCell>
                      </TableRow>


                    </TableBody>
                  </Table>
                 
                </TableContainer>
              </div>
            </div> */}

          </Grid>
          <Grid item xs={12} md={12}>
            <div className="LeaveApproveFooter">
              {/* <button className="Undobtn disableBtn">Undo</button> */}
              <button className="approvebtn" onClick = {() => LeaveApproval(1)}>Approve</button>
              <button className="RejectedBtn" onClick = {() => LeaveApproval(0)}>Reject</button>
              {/* <button  className="RejectedBtn disableBtn">Freeze</button> */}
            </div>
          </Grid>
        </Grid>
      </div>
    </>
  );
};

// export default MultiApproval;
function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    GetCommonspDataV2
  }
)(MultiApproval);


