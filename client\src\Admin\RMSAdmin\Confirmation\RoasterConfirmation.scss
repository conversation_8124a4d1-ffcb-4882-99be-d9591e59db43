@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');
$TextColor: #253858E3;
$fontRoboto: Roboto;
$fontPoppins: Poppins;
$boxShadow: 0px 3px 12px 0px rgba(0, 101, 255, 0.16);
$lightTextColor: #25385899;
$blueColor: #0065FF;

::-webkit-scrollbar {
    width: 3px;
    border-radius: 3px;
  }
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  ::-webkit-scrollbar-thumb {
    background: #ece9e9;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: #c7c5c5;
  }

@mixin headingFontSize {
    font-family: $fontPoppins;
    font-size: 36px;
    font-weight: 900;
    line-height: 54px;
    text-align: left;
    color: $TextColor;
    letter-spacing: normal;
}

@mixin fontsize12 {
    font-size: 12px;
    font-style: normal;
    line-height: 16px;
}
@mixin normalfont {
    font-size: 14px;
    font-style: normal;
    line-height: 20px;
}
@mixin flax {
    display: flex;
    justify-content: center;
    align-items: center;
}
@mixin fontSize16{
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
}


.theme-light {
    background-color: #F7FBFF;
}

.alignCenter {
    text-align: center;
}

.TextUppercase{
    text-transform: uppercase;
}
.BlueBgColor {
    border-radius: 8px;
    background: #DDECFF;
    color: $TextColor;
    font-family: $fontPoppins;
    @include normalfont;
    font-weight: 500;

    /* 142.857% */
    fieldset {
        border: none;
    }
}
.scrollbar{
    overflow-y: auto;
    height: 26vh;
}
label {
    font-family: $fontPoppins;
    @include fontsize12;
    font-weight: 600;
    display: block;
    margin-bottom: 6px;
    color: $TextColor;
    letter-spacing: normal;
}

.MuiOutlinedInput-input {
    padding: 14px;
}

.Active {
    background-color: $blueColor;

    p {
        color: #fff !important;
    }

    .badge {
        color: $blueColor !important;
        background-color: #fff !important;
    }
}

.transferPopup {
    h4 {
        margin: 5px 0px;
        color: $TextColor;
        font-family: $fontPoppins;
        @include fontSize16;
        line-height: 24px;
    }

    h2 {
        color: $TextColor;
        font-family: $fontPoppins;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: 32px;
        padding: 25px 28px 10px;
    }

    .sendTransferbutton {
        color: #FFF;
        text-align: center;
        font-family: $fontPoppins;
        @include normalfont;
        background-color: $blueColor;
        border: none;
        border-radius: 8px;
        box-shadow: $boxShadow;
        margin: 0px auto 10px;
        height: 50px;
        padding: 16px 32px;
        cursor: pointer;
    }

    .MuiPaper-rounded {
        border-radius: 8px;
        max-width: 425px;
    }

    .MuiDialogContent-root {
        padding: 10px 30px 20px;
    }
}

.RemovePopup {
    h2 {
        color: $TextColor;
        font-family: $fontPoppins;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: 32px;
        padding: 25px 28px 10px;
    }

    .RemoveRequestbutton {
        color: #FFF;
        text-align: center;
        font-family: $fontPoppins;
        @include normalfont;
        border-radius: 8px;
        background: #E44A4A;
        border: none;
        width: 100%;
        box-shadow: $boxShadow;
        margin: 20px auto 10px;
        height: 50px;
        padding: 16px;
        cursor: pointer;
    }

    .MuiPaper-rounded {
        border-radius: 8px;
        max-width: 380px;
    }

    .MuiDialogContent-root {
        padding: 0px 30px 20px;

        p {
            color: $lightTextColor;
            font-family: Poppins;
            @include normalfont;
            font-weight: 600;
        }
    }

    h5 {
        margin: 0px 0px 20px 0px;
        color: $lightTextColor;
        font-family: $fontPoppins;
        @include fontsize12;
        font-weight: 400;
        display: flex;

        &::before {
            height: 16px;
            width: 2px;
            background-color: #B9CBFF;
            content: "";
            display: block;
            margin-right: 3px;
        }

        b {
            font-weight: 600;
            color: $lightTextColor;
            margin-right: 5px;
        }
    }



}

.AddAdvisorPopop {
    h2 {
        color: $TextColor;
        font-family: $fontPoppins;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: 32px;
        padding: 25px 28px 10px;
    }

    .MuiPaper-rounded {
        border-radius: 8px;
        max-width: 380px;
        max-height: calc(100% - 30px);
    }

    .EmiDetails {
        @include flax;
        justify-content: space-between !important;

        svg {
            cursor: pointer;
        }
    }

    .EMIName {
        font-family: $fontRoboto;       
        color: $lightTextColor;
        line-height: normal;
        letter-spacing: 0.24px;
        margin-bottom: 0px;
        margin-top: 5px;
        @include fontSize16;
    }

    .EmiCode {
        @include flax;
        justify-content: right !important;
        color: $blueColor;
        font-family: $fontRoboto;
        @include fontSize16;
        font-weight: 500 !important;
        line-height: normal;
        letter-spacing: 0.24px;
        margin-bottom: 0px;
        margin-top: 5px;

        svg {
            width: 12px;
            margin-left: 5px;
            cursor: pointer;
        }
    }

    .sendTransferbutton {
        color: #FFF;
        text-align: center;
        font-family: $fontPoppins;
        @include normalfont;
        background-color: $blueColor;
        border: none;
        border-radius: 8px;
        box-shadow: $boxShadow;
        margin: 0px auto 10px;
        height: 50px;
        padding: 16px 32px;
        cursor: pointer;
    }
}

.MuiTooltip-tooltip {
    background-color: #000DB2;
    color: #F7FBFF;
    font-family: $fontPoppins;
    @include fontsize12;
    font-weight: 500;
    padding: 7px 12px;
    border-radius: 4px;

    .MuiTooltip-arrow {
        color: #000DB2;
    }
}

.RoasterConfirmation {
    padding: 20px 10px;

    header {
        .Heading {
            @include headingFontSize;
        }

        p {
            margin: 0px;
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
            text-align: left;
            color: $lightTextColor;
            font-family: $fontPoppins;

            b {
                color: $TextColor;
            }
        }
    }

    .HistoryButton {
        width: 100px !important;
        float: right;
        margin-top: 22px;
        justify-content: space-evenly !important;
        cursor: pointer;
    }

    .bgFrame {
        width: 100%;
        height: 48px;
        @include flax;

        p {
            overflow: hidden;
            color: $TextColor;
            text-overflow: ellipsis;
            font-family: $fontPoppins;
            @include normalfont;
            font-weight: 500;
            width: 80px;
            white-space: nowrap;

        }

        .badge {
            border-radius: 32px;
            background: #6184FF;
            width: 24px;
            height: 24px;
            color: #F7FBFF;
            text-align: center;
            font-family: $fontRoboto;
            @include fontsize12;
            font-weight: 600;
            line-height: normal;
            @include flax;
            margin-left: 5px;
        }
    }

    .Rostertable {

        table {
            th {
                color: $lightTextColor;
                font-family: $fontRoboto;
                @include normalfont;
                font-weight: 600;
                background-color: #F7FBFF;
            }

            td {
                color: $TextColor;
                font-family: $fontRoboto;
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
            }

            .TransferBtn {
                color: $blueColor;
                margin-right: 40px;
            }

            .RemoveBtn {
                color: #E44A4A;
            }
        }
    }

    button {
        background-color: transparent;
        border: none;
        padding: 0px;
        font-family: $fontPoppins;
        font-weight: 500;
        display: inline-flex !important;
        @include normalfont;
        @include flax;
        cursor: pointer;

        img {
            margin-right: 5px;
        }
    }

    .BottomBtn {
        width: 100%;
        position: fixed;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #FFF 54.5%);
        padding: 24px;
        bottom: -20px;
        left: 0px;
        right: 0px;
        display: flex;
        justify-content: right;

        .AddBtn {
            border-radius: 8px;
            border: 1px solid $blueColor;
            background: #FFF;
            box-shadow: $boxShadow;
            @include normalfont;
            font-family: $fontPoppins;
            font-weight: 500;
            height: 52px;
            padding: 16px 32px;
            cursor: pointer;
            color: $blueColor;

            svg {
                margin-right: 10px;
            }
        }

        .FreezeBtn {
            border-radius: 8px;
            background: #E44A4A;
            @include normalfont;
            font-family: $fontPoppins;
            font-weight: 500;
            margin: 0px 30px 0px 15px;
            height: 52px;
            padding: 16px 32px;
            cursor: pointer;
            color: #fff;
            box-shadow: $boxShadow;
        }
    }
}