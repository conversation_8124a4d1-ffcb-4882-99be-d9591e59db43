import {
  ADD_COURSE,
  ADD_QUIZ,
  ADD_RESPONSE,
  ADD_CURRENT_NESTED_QUESTION,
  ADD_CORRECT_RESPONSES
} from './CourseActionTypes';

export const AddCourseData = (data) => {
  return {
    type: ADD_COURSE,
    payload: data
  }
}


export const AddQuiz = (data) => {
  return {
    type: ADD_QUIZ,
    payload: data
  }
}

export const AddResponse = (data, Type) => {
  return {
    type: ADD_RESPONSE,
    payload: { ...data, Type }
  }
}

export const AddCorrectResponses = (data) => {
  return {
    type: ADD_CORRECT_RESPONSES,
    payload: data
  }
}
 

export const ChangeCurrentNestedQuestion = (data) => {
  return {
    type: ADD_CURRENT_NESTED_QUESTION,
    payload: data
  }
}

 

 