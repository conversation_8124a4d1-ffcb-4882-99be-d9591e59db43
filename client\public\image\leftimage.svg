<svg xmlns="http://www.w3.org/2000/svg" width="419.3" height="325.221" viewBox="0 0 419.3 325.221">
  <g id="Group_227842" data-name="Group 227842" transform="translate(-192.5 -221.389)">
    <path id="Path_513" data-name="Path 513" d="M671.4,414.766c-.037,5.914-.11,11.864-.146,17.778,0,1.46-.037,2.921-.037,4.344a8.436,8.436,0,0,0,.183,3.176c.62,1.606,2.7,1.168,3.468,2.519a5.838,5.838,0,0,0,.183-.767c-2.665,1.314-3.358,4.271-3.578,7.009-.183,2.628-.292,6.863,3.14,7.374a9.108,9.108,0,0,0-.146-.986c-5.184,2.373-3.1,11.645-.913,15.369a26.42,26.42,0,0,0,6.425,6.936c1.387,1.132,2.775,2.191,4.2,3.286.84.657,1.679,1.278,2.482,1.935.438.328,2.3,1.35,2.373,1.971a6.211,6.211,0,0,1,.4-.511h.073a5.839,5.839,0,0,1,.183.767c.037-.037.037-.073.073-.109.292-.621-.62-1.168-.949-.548-.037.036-.037.073-.073.109a.584.584,0,0,0,.183.766.658.658,0,0,0,.767.11c.219-.073.438-.292.4-.511-.146-1.132-1.278-1.643-2.154-2.3-1.278-1.022-2.592-2.008-3.87-3.03a59.945,59.945,0,0,1-6.936-5.914,15.548,15.548,0,0,1-4.417-8.688c-.4-2.775-.766-7.228,2.3-8.652.4-.183.329-.949-.146-.986-2.92-.438-2.519-4.6-2.3-6.827.219-2.191.84-4.491,2.994-5.549a.575.575,0,0,0,.183-.767c-.767-1.314-2.628-1.058-3.322-2.227-.548-.876-.182-2.921-.146-3.979,0-1.46.037-2.921.037-4.344.037-5.548.073-11.134.146-16.683a.531.531,0,1,0-1.058-.073Z" transform="translate(-146.408 12.215)" fill="#e8e8e8"/>
    <path id="Path_514" data-name="Path 514" d="M672.3,414.737c5.111.256,8.688,4.162,11.755,7.812,1.46,1.752,2.993,3.4,4.49,5.038,1.533,1.643,3.541,4.965.767,6.571a.553.553,0,0,0,.292,1.022c4.235.11,6.1,5.257,7.411,8.506a78.763,78.763,0,0,1,3.723,12.12,76.461,76.461,0,0,1,1.168,25.773c-.073.694,1.022.694,1.1,0a78.488,78.488,0,0,0-1.825-28.584,67.508,67.508,0,0,0-4.527-12.886c-1.387-2.921-3.541-5.95-7.045-6.024.109.329.182.694.292,1.022,5.33-3.067-1.278-8.907-3.432-11.427-3.833-4.453-7.7-9.747-14.128-10.076a.567.567,0,0,0-.036,1.132Z" transform="translate(-146.726 12.536)" fill="#e8e8e8"/>
    <path id="Path_515" data-name="Path 515" d="M708.791,394.411a119.676,119.676,0,0,1,10.733-25.992,5.8,5.8,0,0,1-.767.183c.986.584,2.008,1.168,2.994,1.752a.569.569,0,0,0,.767-.766c-2.629-5.257,5.257-8.615,7.922-11.755a4.062,4.062,0,0,1-.657.073c.511.292,1.058.584,1.57.876a.583.583,0,0,0,.766-.183c2.921-4.965,8.068-7.739,13.032-10.294-.255-.219-.548-.4-.8-.621-1.57,5.147.913,10.769,1.387,15.917a49.613,49.613,0,0,1,.073,8.4c-.109,1.387,0,3.468-.547,4.709,0,.912-.511,1.424-1.57,1.606-.329.037-.621.073-.949.11-.73.073-1.5.146-2.227.219-.292.037-.584.219-.547.548a1.667,1.667,0,0,0,.8,1.424c.694.584,1.241.657,1.57,1.679a5.5,5.5,0,0,1,.183,1.716,56.4,56.4,0,0,1-.986,8.908,65.711,65.711,0,0,1-2.117,8.4c-.767,2.3-1.789,5.732-4.636,6.1a.566.566,0,0,0-.474.84c2.117,3.249-1.022,7.191-2.555,10-1.679,2.994-3.213,6.06-4.637,9.163a136.122,136.122,0,0,0-7.082,19.092.549.549,0,0,0,1.059.292,132.772,132.772,0,0,1,8.4-21.83c1.606-3.322,3.541-6.462,5.22-9.747,1.2-2.373,2.154-5.147.584-7.556-.146.292-.329.548-.475.84,2.154-.255,3.541-1.789,4.49-3.651a40.5,40.5,0,0,0,2.884-9.236,63.058,63.058,0,0,0,1.46-10.441,7.423,7.423,0,0,0-.329-3.578c-.292-.73-2.154-1.862-2.19-2.373l-.548.548c2.045-.219,4.965.292,5.659-2.044.912-2.921.876-6.535.84-9.528a52.009,52.009,0,0,0-.913-9.528c-.657-3.541-1.825-7.082-.73-10.66a.554.554,0,0,0-.8-.621c-5.147,2.665-10.44,5.549-13.434,10.7a5.812,5.812,0,0,1,.766-.183c-.511-.292-1.058-.584-1.57-.876a.55.55,0,0,0-.657.073c-3.067,3.614-11.134,7.045-8.1,13.069l.767-.767c-.986-.584-2.008-1.168-2.993-1.752a.576.576,0,0,0-.767.182,124.737,124.737,0,0,0-10.842,26.248.521.521,0,1,0,.986.329Z" transform="translate(-164.887 46.003)" fill="#e8e8e8"/>
    <path id="Path_516" data-name="Path 516" d="M647.841,345.121c-6.133.438-9.382-6.754-8.36-11.937a10.991,10.991,0,0,1,11.025-8.506c6.68.146,10.112,6.681,8.907,12.777-1.059,5.44-6.535,9.455-12.011,7.3-.657-.255-.949.8-.292,1.059a9.973,9.973,0,0,0,11.9-4.271,13.3,13.3,0,0,0-.694-14.237c-2.994-4.015-8.725-4.563-13.069-2.628a11.665,11.665,0,0,0-7.082,10.915c.109,5.184,3.943,11.025,9.637,10.623.73-.037.766-1.132.036-1.1Z" transform="translate(-129.747 57.985)" fill="#e8e8e8"/>
    <path id="Path_517" data-name="Path 517" d="M763.29,204.782a75.557,75.557,0,0,1-7.338,11.171c-.438.548.329,1.314.767.767a75.859,75.859,0,0,0,7.52-11.39c.329-.621-.621-1.168-.949-.548Z" transform="translate(-189.158 118.12)" fill="#e8e8e8"/>
    <path id="Path_518" data-name="Path 518" d="M770.794,206.155c1.314,3.468,2.7,6.936,4.125,10.368.255.657,1.314.365,1.058-.292-1.424-3.432-2.811-6.9-4.125-10.368C771.6,205.206,770.538,205.5,770.794,206.155Z" transform="translate(-196.699 117.624)" fill="#e8e8e8"/>
    <path id="Path_519" data-name="Path 519" d="M764.934,207.3a182.588,182.588,0,0,1-5.257,40.266.549.549,0,0,0,1.058.292,183.013,183.013,0,0,0,5.294-40.558c.037-.73-1.059-.73-1.1,0Z" transform="translate(-191.096 116.985)" fill="#e8e8e8"/>
    <path id="Path_520" data-name="Path 520" d="M782.359,252.389a77.642,77.642,0,0,1-.694,23.437c-.109.694.913.986,1.059.292a79.583,79.583,0,0,0,.73-23.729C783.345,251.7,782.25,251.7,782.359,252.389Z" transform="translate(-202.205 94.205)" fill="#e8e8e8"/>
    <path id="Path_521" data-name="Path 521" d="M774.539,282.747c1.278,2.92,3.5,5.586,3.65,8.871a.555.555,0,0,0,1.022.292c1.278-2.227,2.592-4.453,3.869-6.681a.548.548,0,0,0-.949-.548c-1.278,2.227-2.592,4.454-3.87,6.681.329.109.694.183,1.022.292-.146-3.541-2.445-6.279-3.8-9.419-.292-.694-1.241-.146-.949.511Z" transform="translate(-198.582 79.034)" fill="#e8e8e8"/>
    <path id="Path_522" data-name="Path 522" d="M270.916,649.742c22.013-2.044,44.063-4.709,66.148-5.622,22.269-.912,44.647-.328,66.915.256,12.777.328,25.554.949,38.258,2.153.694.073.694-1.022,0-1.095-22.342-2.117-44.756-2.373-67.171-2.628-11.244-.146-22.488-.255-33.695.109-10.915.329-21.794,1.241-32.673,2.227l-37.784,3.5C270.223,648.683,270.223,649.779,270.916,649.742Z" transform="translate(55.961 -103.133)" fill="#e8e8e8"/>
    <path id="Path_523" data-name="Path 523" d="M502.447,618.292c9.163,1.716,18.217,1.643,27.453.8,9.491-.876,18.947-1.424,28.474-1.862,19.129-.84,38.258-1.606,57.35-2.409,10.915-.475,21.831-.913,32.746-1.387.694-.037.694-1.132,0-1.1l-57.46,2.409c-18.837.8-37.783,1.2-56.584,2.811-5.111.438-10.222,1.022-15.332,1.1a82.112,82.112,0,0,1-16.355-1.46c-.694-.073-.986.986-.292,1.1Z" transform="translate(-61.015 -87.817)" fill="#e8e8e8"/>
    <path id="Path_524" data-name="Path 524" d="M.548,607.387q25.518.712,51.035-.219c.694-.037.694-1.132,0-1.1q-25.518.931-51.035.219c-.73-.037-.73,1.058,0,1.1Z" transform="translate(192.5 -84.652)" fill="#e8e8e8"/>
    <path id="Path_525" data-name="Path 525" d="M37.974,636.535c6.206-1.314,12.777-.475,19.056-.183,6.316.329,12.594.621,18.91.949,12.85.657,25.663,1.314,38.513,1.935.694.036.694-1.059,0-1.1L75.209,636.17l-19.6-.986c-5.95-.292-12.01-.986-17.888.292-.694.146-.4,1.2.256,1.059Z" transform="translate(173.655 -99.125)" fill="#e8e8e8"/>
    <path id="Path_526" data-name="Path 526" d="M236.921,576.744c22.561,1.533,44.756-4.308,67.28-4.235a.548.548,0,0,0,0-1.1c-22.524-.073-44.72,5.732-67.28,4.235-.694-.073-.694,1.059,0,1.1Z" transform="translate(73.127 -67.151)" fill="#e8e8e8"/>
    <path id="Path_527" data-name="Path 527" d="M202.171,342.813a10.894,10.894,0,0,1,.657-1.825c.183-.255.329-.475.511-.73.766-.073,1.57-.183,2.336-.255.219.146.4.584.548.8a6.641,6.641,0,0,0,1.972,1.935,6.079,6.079,0,0,0,5.257.511c4.49-1.643,6.169-7.593,11.645-7.009A6.52,6.52,0,0,1,229,337.994c.8.766,1.424,1.716,2.263,2.446a5.06,5.06,0,0,0,5.147.766c2.19-.766,3.87-2.409,5.512-3.979,2.227-2.081,5.476-4.6,8.433-2.373,1.935,1.424,3.066,3.614,5.476,4.381a7.194,7.194,0,0,0,6.571-1.132c.548-.4,0-1.387-.548-.949a6.087,6.087,0,0,1-8.214-.657c-1.533-1.5-2.847-3.358-5.111-3.614-5.33-.657-8.1,5.841-12.631,7.337-3.431,1.132-4.563-1.935-6.827-3.614a7.825,7.825,0,0,0-5.877-1.424,9.958,9.958,0,0,0-5.4,3.14c-1.789,1.789-3.687,4.235-6.462,4.235a5.208,5.208,0,0,1-4.307-2.483c-.584-.8-1.059-1.46-2.117-1.57-2.3-.219-3.286,2.191-3.833,3.979a.573.573,0,0,0,1.1.329Z" transform="translate(90.976 53.32)" fill="#e8e8e8"/>
    <path id="Path_528" data-name="Path 528" d="M315.937,310.606c1.314-3.833,5.914-.584,7.666.84,1.534,1.241,3.322,2.519,5.4,2.446,3.432-.146,5.257-3.651,8.068-5.111,3.906-2.008,6.279,3.212,10.076,2.7,1.679-.219,2.555-1.57,3.614-2.738,1.132-1.241,2.3-1.241,3.906-.876,1.5.365,3.03.73,4.527,1.1a.549.549,0,0,0,.292-1.058c-1.5-.365-3.03-.73-4.527-1.1a5.887,5.887,0,0,0-3.067-.255c-2.008.621-2.592,3.286-4.6,3.76-2.154.511-4.271-1.9-6.133-2.665a5.1,5.1,0,0,0-4.6.146c-1.606.84-2.811,2.264-4.235,3.4a5.865,5.865,0,0,1-3.249,1.533c-1.9.109-3.468-1.2-4.855-2.3-1.679-1.314-3.468-2.884-5.768-2.7a3.825,3.825,0,0,0-3.541,2.555C314.66,310.971,315.719,311.263,315.937,310.606Z" transform="translate(33.501 66.653)" fill="#e8e8e8"/>
    <path id="Path_529" data-name="Path 529" d="M649.779,99.151c-.329,6.827-.547,13.69-.621,20.553a.548.548,0,0,0,1.1,0c.073-6.863.292-13.689.621-20.553C650.91,98.458,649.815,98.458,649.779,99.151Z" transform="translate(-135.298 171.584)" fill="#e8e8e8"/>
    <path id="Path_530" data-name="Path 530" d="M651.185,98.163c8.433-1.168,17.012-2.3,25.408-.329a.549.549,0,0,0,.292-1.059c-8.579-2.044-17.377-.876-25.992.329-.693.073-.4,1.132.292,1.059Z" transform="translate(-135.974 173.084)" fill="#e8e8e8"/>
    <path id="Path_531" data-name="Path 531" d="M700.99,100.257c.182,13.873-.073,27.781-.8,41.653-.037.694,1.059.694,1.1,0q1.1-20.808.8-41.653a.548.548,0,0,0-1.1,0Z" transform="translate(-161.065 171.026)" fill="#e8e8e8"/>
    <path id="Path_532" data-name="Path 532" d="M651,176.881c.182,1.752.255,3.578.584,5.293a2.829,2.829,0,0,0,1.2,1.935c2.3,1.5,6.024.621,8.542.475,3.578-.219,7.191-.438,10.769-.657,1.606-.11,4.162-.548,3.176-2.775-.292-.657-1.242-.073-.95.548.183.4-1.861,1.059-2.227,1.132a14.647,14.647,0,0,1-1.789.109c-1.059.073-2.154.146-3.213.183-2.153.146-4.307.255-6.461.4-1.971.11-4.162.511-6.133.073-2.482-.584-2.154-4.6-2.409-6.681-.073-.73-1.168-.73-1.1-.037Z" transform="translate(-136.226 132.332)" fill="#e8e8e8"/>
    <path id="Path_533" data-name="Path 533" d="M627.406,174.422c-3.213,4.709-6.535,9.345-9.893,13.945a.5.5,0,0,0,0,.548,2.209,2.209,0,0,0,2.629.949c1.57-.475,2.336-2.263,3.14-3.5a52.1,52.1,0,0,1,4.417-5.841c.694-.767,1.387-1.534,2.117-2.263.84-.84,2.263-2.629,3.468-2.811.693-.109.4-1.168-.292-1.058a5.575,5.575,0,0,0-2.628,1.789c-1.059.986-2.081,2.008-3.067,3.1a54.432,54.432,0,0,0-5.184,6.754,15.178,15.178,0,0,1-1.351,2.044c-.584.84-1.2.913-1.935.219.219-.292.438-.584.621-.876.986-1.35,1.971-2.7,2.92-4.052,2.008-2.811,3.979-5.622,5.914-8.47.475-.511-.474-1.058-.876-.474Z" transform="translate(-119.277 133.44)" fill="#e8e8e8"/>
    <path id="Path_534" data-name="Path 534" d="M641.252,157.781a11.446,11.446,0,0,0,10.66-6.79c1.606-3.76.73-8.652-2.665-11.134-3.906-2.811-8.907-.694-11.937,2.373a12.378,12.378,0,0,0-3.65,6.681,7.69,7.69,0,0,0,.146,3.03,6.448,6.448,0,0,0,.657,1.643c.073.146,1.753,2.519,1.022,2.482-.694-.073-.694,1.022,0,1.1a1.369,1.369,0,0,0,1.387-1.935c-.255-.84-1.058-1.424-1.5-2.19a6.389,6.389,0,0,1-.621-3.943c.621-4.673,5.586-9.564,10.4-9.491,4.745.037,7.228,5.33,6.242,9.491a10.232,10.232,0,0,1-10.149,7.557A.566.566,0,0,0,641.252,157.781Z" transform="translate(-127.429 151.432)" fill="#e8e8e8"/>
    <path id="Path_535" data-name="Path 535" d="M658.546,106.845l-.037-.036v.766c.475-.548,3.76-.511,4.637-.62,1.387-.146,2.811-.256,4.2-.329s2.811-.146,4.235-.183c.621,0,3.87.292,4.089-.292-.255.657.8.949,1.059.292.037-.073.037-.109.073-.183a.525.525,0,0,0-.146-.548c-.8-.8-2.263-.438-3.322-.438-2.008,0-3.979.073-5.987.219-1.862.109-3.724.292-5.622.474-.949.11-3.322-.036-4.016.767a.563.563,0,0,0,0,.767l.037.036c.547.584,1.314-.219.8-.694Z" transform="translate(-139.538 168.271)" fill="#e8e8e8"/>
    <path id="Path_536" data-name="Path 536" d="M658.118,118.214a61.021,61.021,0,0,0,10.952.219c.694-.036.694-1.132,0-1.1a61.1,61.1,0,0,1-10.66-.183C657.717,117.083,657.425,118.141,658.118,118.214Z" transform="translate(-139.623 162.232)" fill="#e8e8e8"/>
    <path id="Path_537" data-name="Path 537" d="M653.015,126.957c-.109.109-.219.255-.329.365a.562.562,0,0,0,.4.949,150.388,150.388,0,0,0,19.019-.876c.694-.073.694-1.168,0-1.1a150.478,150.478,0,0,1-19.019.876c.146.329.256.621.4.949.109-.109.219-.255.328-.365.438-.548-.328-1.314-.8-.8Z" transform="translate(-137.001 157.615)" fill="#e8e8e8"/>
    <path id="Path_538" data-name="Path 538" d="M668.805,138.19a41.118,41.118,0,0,0,9.929-.438c.694-.11.4-1.168-.292-1.059a40.651,40.651,0,0,1-9.637.4c-.73-.036-.73,1.059,0,1.1Z" transform="translate(-144.942 152.369)" fill="#e8e8e8"/>
    <path id="Path_539" data-name="Path 539" d="M645.438,157.467a4.271,4.271,0,0,1-4.527-5.439,8.51,8.51,0,0,1,5.95-5.293,4.174,4.174,0,0,1,3.432.949c1.351,1.241.986,3.067.365,4.563-1.059,2.629-2.994,5-5.951,5.33-.694.073-.694,1.168,0,1.1,3.8-.4,6.5-3.65,7.411-7.228a4.581,4.581,0,0,0-5.074-5.841,9.186,9.186,0,0,0-7.338,6.717,5.366,5.366,0,0,0,5.731,6.242c.694-.037.694-1.132,0-1.1Z" transform="translate(-130.482 147.876)" fill="#e8e8e8"/>
    <path id="Path_540" data-name="Path 540" d="M648.488,148.566c-1.46,3.139-2.811,6.315-4.016,9.528-.255.657.8.949,1.059.292,1.2-3.139,2.482-6.242,3.906-9.272.292-.657-.62-1.2-.949-.548Z" transform="translate(-132.912 146.519)" fill="#e8e8e8"/>
    <path id="Path_541" data-name="Path 541" d="M653.093,154.916c-.949,1.971-1.862,3.943-2.811,5.914-.292.621.657,1.2.949.548.949-1.971,1.862-3.943,2.811-5.914C654.334,154.843,653.421,154.258,653.093,154.916Z" transform="translate(-135.838 143.309)" fill="#e8e8e8"/>
    <path id="Path_542" data-name="Path 542" d="M107.812,34.258v10a.548.548,0,0,0,1.1,0v-10A.548.548,0,0,0,107.812,34.258Z" transform="translate(138.06 204.353)" fill="#e8e8e8"/>
    <path id="Path_543" data-name="Path 543" d="M108.682,54.836h9.273a.548.548,0,0,0,0-1.1h-9.273A.548.548,0,0,0,108.682,54.836Z" transform="translate(137.883 194.252)" fill="#e8e8e8"/>
    <path id="Path_544" data-name="Path 544" d="M128.154,41.966V31.087a.548.548,0,0,0-1.1,0V41.966A.548.548,0,0,0,128.154,41.966Z" transform="translate(128.341 205.954)" fill="#e8e8e8"/>
    <path id="Path_545" data-name="Path 545" d="M118.248,30.216h-9.491a.548.548,0,0,0,0,1.1h9.491A.548.548,0,0,0,118.248,30.216Z" transform="translate(137.845 206.131)" fill="#e8e8e8"/>
    <path id="Path_546" data-name="Path 546" d="M117.177,56.6V67.736a.548.548,0,0,0,1.1,0V56.6A.548.548,0,0,0,117.177,56.6Z" transform="translate(133.33 193.07)" fill="#e8e8e8"/>
    <path id="Path_547" data-name="Path 547" d="M90.266,67.056q6.078-5.8,12.668-11.134c.548-.438-.219-1.2-.767-.767C97.786,58.7,93.588,62.384,89.5,66.29,88.988,66.764,89.755,67.531,90.266,67.056Z" transform="translate(147.392 193.604)" fill="#e8e8e8"/>
    <path id="Path_548" data-name="Path 548" d="M119.306,56.492l13.142,9.966c.548.438,1.1-.511.548-.949l-13.142-9.966C119.306,55.1,118.758,56.054,119.306,56.492Z" transform="translate(132.37 193.399)" fill="#e8e8e8"/>
    <path id="Path_549" data-name="Path 549" d="M82.135,80.867H89.4a.548.548,0,0,0,0-1.1H82.135A.548.548,0,0,0,82.135,80.867Z" transform="translate(151.288 181.107)" fill="#e8e8e8"/>
    <path id="Path_550" data-name="Path 550" d="M96.013,81.868v8.25a.548.548,0,0,0,1.1,0v-8.25a.548.548,0,1,0-1.1,0Z" transform="translate(144.018 180.326)" fill="#e8e8e8"/>
    <path id="Path_551" data-name="Path 551" d="M81.043,80.835v9.637a.548.548,0,0,0,1.1,0V80.835A.548.548,0,1,0,81.043,80.835Z" transform="translate(151.576 180.847)" fill="#e8e8e8"/>
    <path id="Path_552" data-name="Path 552" d="M81.4,101.072h6.754a.548.548,0,0,0,0-1.095H81.4A.548.548,0,0,0,81.4,101.072Z" transform="translate(151.66 170.905)" fill="#e8e8e8"/>
    <path id="Path_553" data-name="Path 553" d="M110.393,81.352v9.491a.548.548,0,0,0,1.1,0V81.352a.548.548,0,1,0-1.1,0Z" transform="translate(136.756 180.586)" fill="#e8e8e8"/>
    <path id="Path_554" data-name="Path 554" d="M110.968,100.556h6.644a.548.548,0,0,0,0-1.1h-6.644A.548.548,0,0,0,110.968,100.556Z" transform="translate(136.729 171.166)" fill="#e8e8e8"/>
    <path id="Path_555" data-name="Path 555" d="M111.706,81.9h5.877a.548.548,0,0,0,0-1.1h-5.877A.548.548,0,0,0,111.706,81.9Z" transform="translate(136.355 180.586)" fill="#e8e8e8"/>
    <path id="Path_556" data-name="Path 556" d="M123.3,81.085V90.1a.548.548,0,0,0,1.1,0V81.085A.548.548,0,0,0,123.3,81.085Z" transform="translate(130.239 180.707)" fill="#e8e8e8"/>
    <path id="Path_557" data-name="Path 557" d="M141.474,81.085c-.109,2.373-.183,4.746-.183,7.119a.548.548,0,0,0,1.1,0c0-2.373.073-4.746.183-7.119C142.605,80.391,141.51,80.391,141.474,81.085Z" transform="translate(121.154 180.707)" fill="#e8e8e8"/>
    <path id="Path_558" data-name="Path 558" d="M142.074,80.191c2.409-.256,4.819-.548,7.228-.8.694-.073.694-1.168,0-1.1-2.409.256-4.819.548-7.228.8C141.381,79.169,141.344,80.264,142.074,80.191Z" transform="translate(121.028 181.856)" fill="#e8e8e8"/>
    <path id="Path_559" data-name="Path 559" d="M156.332,79.315a25.512,25.512,0,0,0,.255,7.265c.11.694,1.168.4,1.059-.292a24.81,24.81,0,0,1-.219-6.973c.073-.694-1.022-.694-1.1,0Z" transform="translate(113.633 181.601)" fill="#e8e8e8"/>
    <path id="Path_560" data-name="Path 560" d="M142.654,95.707l7.228-1.1c.694-.11.4-1.168-.292-1.059l-7.228,1.1C141.668,94.758,141.96,95.78,142.654,95.707Z" transform="translate(120.813 174.152)" fill="#e8e8e8"/>
    <path id="Path_561" data-name="Path 561" d="M113.177,102.082a54.845,54.845,0,0,0-3.577,6.389c-.292.621.657,1.2.949.548a57.279,57.279,0,0,1,3.578-6.389C114.491,102.046,113.542,101.5,113.177,102.082Z" transform="translate(137.185 169.968)" fill="#e8e8e8"/>
    <path id="Path_562" data-name="Path 562" d="M117.447,102.409a37.04,37.04,0,0,1,3.5,5.732c.329.62,1.278.073.949-.548a41.472,41.472,0,0,0-3.5-5.731C117.995,101.277,117.046,101.825,117.447,102.409Z" transform="translate(133.243 170.079)" fill="#e8e8e8"/>
    <path id="Path_563" data-name="Path 563" d="M105.142,117.037c.767,0,1.534,0,2.337.037a10.434,10.434,0,0,1,1.059.073c.657.037.584-.182.694.4a19.489,19.489,0,0,1-.146,4.6c-.073.694,1.022.694,1.1,0,.073-1.1.146-2.227.146-3.322a5.086,5.086,0,0,0-.146-2.227c-.329-.548-1.1-.475-1.643-.511q-1.7-.11-3.4-.11c-.694-.036-.694,1.059,0,1.059Z" transform="translate(139.67 162.825)" fill="#e8e8e8"/>
    <path id="Path_564" data-name="Path 564" d="M104.2,117.5c.109,1.278.219,2.519.328,3.8a12.572,12.572,0,0,0,.329,3.5.814.814,0,0,0,.73.62,11.548,11.548,0,0,0,3.322-.438c.694-.109.4-1.132-.292-1.058a8.548,8.548,0,0,1-2.3.328c-.694-.182-.548-1.132-.584-1.861-.146-1.643-.292-3.249-.438-4.892-.073-.694-1.168-.73-1.1,0Z" transform="translate(139.888 162.326)" fill="#e8e8e8"/>
    <path id="Path_565" data-name="Path 565" d="M120.716,117.189a31.348,31.348,0,0,0-.073,3.212,26.58,26.58,0,0,1,.219,2.738c.182-.037.365-.109.548-.146-.037,0-.073-.037-.146-.037a6.229,6.229,0,0,0,.4.511v-.146a6.208,6.208,0,0,1-.4.511.135.135,0,0,0,.109-.036c.694-.146.4-1.2-.292-1.059a.133.133,0,0,0-.109.037.508.508,0,0,0-.4.511v.146a.547.547,0,0,0,.4.511c.949.182.949-.694.912-1.388a34.224,34.224,0,0,1-.073-5.476c.037-.621-1.059-.584-1.1.109Z" transform="translate(131.617 162.491)" fill="#e8e8e8"/>
    <path id="Path_566" data-name="Path 566" d="M121.075,116.836c0-.255,2.336-.328,2.665-.292.913.146.73.219.876.876a5.161,5.161,0,0,1-.073,1.5c-.036.912-.036,1.789,0,2.7,0,.511.365,2.044.11,2.409a5.812,5.812,0,0,1,.766-.183c-.037,0-.073-.037-.109-.037.073.219.183.4.255.621,0-.037.037-.073.037-.109a.549.549,0,0,0-1.059-.292c0,.037-.036.073-.036.109a.54.54,0,0,0,.255.621c.037,0,.073.036.109.036a.615.615,0,0,0,.767-.182c.255-.365.146-.73.109-1.168-.037-.986-.109-2.008-.109-2.994s.036-2.008.073-2.994a2.127,2.127,0,0,0-.146-1.424c-.62-.84-2.592-.584-3.395-.475-.912.11-2.117.219-2.19,1.314,0,.657,1.1.657,1.1-.037Z" transform="translate(131.915 163.1)" fill="#e8e8e8"/>
    <path id="Path_567" data-name="Path 567" d="M121.854,132.059a10.805,10.805,0,0,1,3.249.292.549.549,0,0,0,.292-1.058,10.554,10.554,0,0,0-3.541-.365c-.73.073-.73,1.168,0,1.132Z" transform="translate(131.245 155.287)" fill="#e8e8e8"/>
    <path id="Path_568" data-name="Path 568" d="M147.727,98.052c-.876,1.935-1.643,3.943-2.336,5.95-.219.657.84.949,1.058.292.657-1.935,1.388-3.833,2.227-5.695C148.968,97.943,148.019,97.4,147.727,98.052Z" transform="translate(119.099 172.026)" fill="#e8e8e8"/>
    <path id="Path_569" data-name="Path 569" d="M138.156,113.995a18.126,18.126,0,0,1,5.622-.694c.694.036.694-1.059,0-1.1a19.066,19.066,0,0,0-5.914.73A.549.549,0,0,0,138.156,113.995Z" transform="translate(123.085 164.736)" fill="#e8e8e8"/>
    <path id="Path_570" data-name="Path 570" d="M138.619,113.6a6.9,6.9,0,0,0,.037,2.994,31.083,31.083,0,0,1,.073,3.687c-.073,1.606.329,1.789,1.935,1.643,1.1-.109,2.592.183,2.993-1.022a20.436,20.436,0,0,0,.365-3.979c.109-1.314.255-2.665.365-3.979.073-.694-1.022-.694-1.1,0-.146,1.533-.292,3.1-.438,4.636a18.575,18.575,0,0,1-.183,2.008c-.219,1.022-.328,1.1-1.387,1.2-.328.037-.62.073-.949.109-.621.219-.8.037-.548-.584a10.608,10.608,0,0,1,.037-1.862c-.037-1.387-.584-3.249-.183-4.563C139.9,113.2,138.838,112.9,138.619,113.6Z" transform="translate(122.585 164.621)" fill="#e8e8e8"/>
    <path id="Path_571" data-name="Path 571" d="M153.139,99.583A39.173,39.173,0,0,1,155.4,103.6c.292.621,1.241.073.949-.548a36.367,36.367,0,0,0-2.263-4.016C153.686,98.414,152.737,98.962,153.139,99.583Z" transform="translate(115.221 171.519)" fill="#e8e8e8"/>
    <path id="Path_572" data-name="Path 572" d="M155.186,110.659h5.476a.548.548,0,0,0,0-1.1h-.328a.548.548,0,0,0,0,1.1h.328a.548.548,0,0,0,0-1.1h-5.476A.548.548,0,1,0,155.186,110.659Z" transform="translate(114.414 166.063)" fill="#e8e8e8"/>
    <path id="Path_573" data-name="Path 573" d="M154.635,111.06c.146,1.387.255,2.775.292,4.162.036.986-.292,2.665.146,3.578.255.548.584.62,1.168.62a40.438,40.438,0,0,0,4.162-.548.585.585,0,0,0,.4-.511c-.036-2.483-.073-4.928-.109-7.411a.548.548,0,0,0-1.1,0c.037,2.483.073,4.928.109,7.411a6.225,6.225,0,0,1,.4-.511c-.876.11-1.752.219-2.628.365-.292.037-1.132.292-1.388.073-.182-.146,0-1.278,0-1.5-.036-1.9-.146-3.8-.328-5.658C155.658,110.366,154.562,110.366,154.635,111.06Z" transform="translate(114.417 165.626)" fill="#e8e8e8"/>
    <path id="Path_574" data-name="Path 574" d="M71.83,111.692a62.678,62.678,0,0,1,5.914-4.892c.548-.4,0-1.351-.548-.949a62.776,62.776,0,0,0-6.133,5.075C70.553,111.4,71.319,112.167,71.83,111.692Z" transform="translate(156.701 167.987)" fill="#e8e8e8"/>
    <path id="Path_575" data-name="Path 575" d="M83.968,106.1a27.235,27.235,0,0,1,2.738,4.928c.292.657,1.241.073.949-.548a29.37,29.37,0,0,0-2.738-4.928C84.515,104.964,83.566,105.512,83.968,106.1Z" transform="translate(150.148 168.218)" fill="#e8e8e8"/>
    <path id="Path_576" data-name="Path 576" d="M62.45,120.279l2.92-.219a8.35,8.35,0,0,1,1.314-.109c.511-.183.657-.146.438.146a.958.958,0,0,0-.183.694,31.008,31.008,0,0,0-.548,5.512c.329-.146.621-.255.949-.4a.5.5,0,0,1-.11-.146c-.475-.511-1.241.256-.767.767a.5.5,0,0,1,.11.146.565.565,0,0,0,.949-.4,39.657,39.657,0,0,1,.4-4.673c.11-.657.584-1.752.183-2.409-.329-.511-.876-.4-1.387-.365-1.424.109-2.811.219-4.235.329-.73.109-.73,1.2-.037,1.132Z" transform="translate(161.226 161.408)" fill="#e8e8e8"/>
    <path id="Path_577" data-name="Path 577" d="M62.285,119.666h-.146a.624.624,0,0,0-.475.256c-.584.986-.548,2.7-.73,3.833l-.767,4.819c-.036.292.073.62.365.657A8.535,8.535,0,0,0,65.5,128.9c.657-.255.365-1.314-.292-1.058a7.481,7.481,0,0,1-4.381.328c.11.219.256.438.365.657.11-.73,1.387-8.068,1.022-8.1.767.109.767-.986.073-1.059Z" transform="translate(162.121 160.963)" fill="#e8e8e8"/>
    <path id="Path_578" data-name="Path 578" d="M84.266,118.81l-.037.037H85c-.036,0-.036-.037-.073-.037v.766a5.723,5.723,0,0,1,.84-.036c.4-.037.84-.073,1.241-.073a16.089,16.089,0,0,1,2.3.073c-.146-.329-.256-.621-.4-.949a.809.809,0,0,0-.183.146c-.694.073-.694,1.168,0,1.1a1.342,1.342,0,0,0,.986-.475.562.562,0,0,0-.4-.949,22.978,22.978,0,0,0-3.176-.037c-.511.037-1.57-.073-1.935.4a.529.529,0,0,0,0,.767c.036,0,.036.036.073.036a.528.528,0,0,0,.767,0l.036-.036c.438-.475-.329-1.242-.8-.73Z" transform="translate(150.069 161.636)" fill="#e8e8e8"/>
    <path id="Path_579" data-name="Path 579" d="M83.086,119.264c-.11,1.5-.219,2.994-.292,4.49-.037.84-.511,2.993,0,3.76a.959.959,0,0,0,.986.474,6.436,6.436,0,0,0,1.424-.438,9.6,9.6,0,0,1,2.7-.329.548.548,0,0,0,0-1.1,10.806,10.806,0,0,0-2.409.255c-.365.073-1.059.474-1.387.4-.256.183-.365.146-.292-.146a3.817,3.817,0,0,1,0-.913c.037-2.117.219-4.271.365-6.389a.55.55,0,1,0-1.1-.073Z" transform="translate(150.812 161.438)" fill="#e8e8e8"/>
    <path id="Path_580" data-name="Path 580" d="M92.722,119.5a55.146,55.146,0,0,0-.4,7.374.548.548,0,0,0,1.1,0,55.152,55.152,0,0,1,.4-7.374C93.927,118.811,92.831,118.811,92.722,119.5Z" transform="translate(145.885 161.307)" fill="#e8e8e8"/>
    <path id="Path_581" data-name="Path 581" d="M682.139,428.549a233.839,233.839,0,0,1,17.961,59.5c.11.694,1.168.4,1.059-.292A235.916,235.916,0,0,0,683.088,428c-.292-.62-1.241-.073-.949.548Z" transform="translate(-151.924 5.403)" fill="#e8e8e8"/>
    <path id="Path_582" data-name="Path 582" d="M746.931,364.385c-10.66,16.281-19.932,33.44-26.5,51.8-.256.657.8.949,1.058.292,6.535-18.289,15.771-35.338,26.394-51.546.365-.584-.584-1.132-.949-.547Z" transform="translate(-171.267 37.516)" fill="#e8e8e8"/>
    <path id="Path_583" data-name="Path 583" d="M674.268,468.7a25.089,25.089,0,0,1,14.383,10.368c.4.584,1.35.036.949-.548a26.26,26.26,0,0,0-15.04-10.879A.549.549,0,0,0,674.268,468.7Z" transform="translate(-147.777 -14.738)" fill="#e8e8e8"/>
    <path id="Path_584" data-name="Path 584" d="M699.44,438.939a28.51,28.51,0,0,0-3.906,10.806c-.109.694.949.986,1.059.292a27.985,27.985,0,0,1,3.8-10.55c.365-.584-.584-1.132-.949-.548Z" transform="translate(-158.712 -0.131)" fill="#e8e8e8"/>
    <path id="Path_585" data-name="Path 585" d="M726.214,396.76a21.092,21.092,0,0,1,3.979,6.681c.438,1.1.8,2.227,1.132,3.322.328,1.059,1.1,2.629.657,3.687-.255.657.8.949,1.059.292a4.477,4.477,0,0,0-.329-3.14c-.438-1.5-.913-2.993-1.5-4.453a23.192,23.192,0,0,0-4.271-7.192c-.438-.511-1.2.256-.73.8Z" transform="translate(-174.133 21.532)" fill="#e8e8e8"/>
    <path id="Path_586" data-name="Path 586" d="M750.45,394.108A34.689,34.689,0,0,1,767.645,383.3a.549.549,0,0,0-.292-1.058A35.824,35.824,0,0,0,749.684,393.3c-.438.548.329,1.35.767.8Z" transform="translate(-185.993 28.382)" fill="#e8e8e8"/>
    <path id="Path_587" data-name="Path 587" d="M724.4,454.249a35.379,35.379,0,0,1,11.244-6.5c1.862-.694,3.724-1.241,5.622-1.789,1.058-.292,2.081-.547,3.14-.84.511-.109,2.665-.986,3.066-.767a5.823,5.823,0,0,1-.183-.766c-.037.037-.036.073-.073.109a5.811,5.811,0,0,1,.766-.183c-.036,0-.073-.036-.109-.036-.62-.329-1.168.62-.547.949.037,0,.073.037.109.037a.585.585,0,0,0,.767-.183c.037-.037.037-.073.073-.109a.584.584,0,0,0-.182-.767c-.511-.292-1.059-.037-1.606.073-1.241.292-2.483.584-3.724.912-2.373.621-4.745,1.314-7.082,2.117a35.467,35.467,0,0,0-12.01,6.863c-.548.548.219,1.35.73.876Z" transform="translate(-172.826 -2.445)" fill="#e8e8e8"/>
    <path id="Path_588" data-name="Path 588" d="M687.348,553.429c8.286,2.008,16.72.511,25.006-.475a267.327,267.327,0,0,1,27.014-1.387.548.548,0,0,0,0-1.1c-8.8.146-17.6.329-26.32,1.314-8.433.986-17.012,2.628-25.408.584a.55.55,0,0,0-.292,1.058Z" transform="translate(-154.396 -56.577)" fill="#e8e8e8"/>
    <path id="Path_589" data-name="Path 589" d="M739.995,546.676q22.122-1.149,44.245-2.336c.694-.037.694-1.132,0-1.1q-22.122,1.15-44.245,2.337C739.3,545.617,739.3,546.712,739.995,546.676Z" transform="translate(-180.904 -52.927)" fill="#e8e8e8"/>
    <path id="Path_590" data-name="Path 590" d="M646.044,323.469c-3.614.694-8.031-2.775-7.922-6.608.146-4.344,5.585-5.8,9.053-5.841,3.833,0,6.535,2.957,5.622,6.827a7.669,7.669,0,0,1-2.774,4.125c-.767.621-3.87,2.884-4.746,1.789-.438-.548-1.2.219-.766.766,1.168,1.46,3.578-.037,4.819-.73a9.779,9.779,0,0,0,4.344-4.965,6.489,6.489,0,0,0-5.622-8.871c-4.271-.329-10.879,1.643-10.988,6.9-.073,4.6,4.965,8.469,9.309,7.666.657-.109.365-1.168-.329-1.058Z" transform="translate(-129.19 64.889)" fill="#e8e8e8"/>
    <path id="Path_591" data-name="Path 591" d="M85.511,371.828a27,27,0,0,0-3.5,5.038c-.329.621.621,1.168.949.548a27.165,27.165,0,0,1,3.359-4.819c.438-.511-.329-1.314-.8-.766Z" transform="translate(151.125 33.724)" fill="#fff"/>
    <path id="Path_592" data-name="Path 592" d="M88.966,372.087c1.1,1.533,2.19,3.03,3.285,4.563.4.584,1.351.037.949-.548-1.1-1.533-2.19-3.03-3.285-4.563C89.477,370.955,88.528,371.5,88.966,372.087Z" transform="translate(147.633 33.903)" fill="#fff"/>
    <path id="Path_593" data-name="Path 593" d="M88.933,376.705c.329,12.887.621,25.773.949,38.7a.548.548,0,0,0,1.1,0c-.329-12.887-.621-25.773-.949-38.7C89.992,376.012,88.9,375.975,88.933,376.705Z" transform="translate(147.593 31.438)" fill="#fff"/>
    <path id="Path_594" data-name="Path 594" d="M92.754,456.366H141.38a.548.548,0,0,0,0-1.1H92.754A.548.548,0,0,0,92.754,456.366Z" transform="translate(145.926 -8.504)" fill="#fff"/>
    <path id="Path_595" data-name="Path 595" d="M186.671,453.6c1.132.438,2.227.876,3.358,1.314-.146-.4-.328-.8-.474-1.2a6.319,6.319,0,0,1-2.263,2.336.827.827,0,0,0,.84,1.424,7.906,7.906,0,0,0,2.884-2.92.827.827,0,0,0-.474-1.2c-1.132-.438-2.227-.876-3.359-1.314C186.123,451.631,185.685,453.2,186.671,453.6Z" transform="translate(98.518 -6.836)" fill="#fff"/>
    <path id="Path_596" data-name="Path 596" d="M752.024,52c-1.46-1.935.621-4.892,2.884-3.432a2.19,2.19,0,0,1,.766,3.066,2.472,2.472,0,0,1-3.03.84c-.621-.329-1.168.621-.547.949a3.619,3.619,0,0,0,4.381-1.022,3.222,3.222,0,0,0-.657-4.527,3.189,3.189,0,0,0-4.417.256,3.625,3.625,0,0,0-.255,4.454c.329.511,1.278-.037.876-.584Z" transform="translate(-186.472 197.602)" fill="#f7f7f7"/>
    <path id="Path_597" data-name="Path 597" d="M813.775,324.714a2.721,2.721,0,0,1,2.884-2.884,4.157,4.157,0,0,0,1.9-.109c1.242-.547,1.716-1.971,2.117-3.176.584-1.752,1.022-2.592,3.213-2.045.584.146.949.438,1.57.219,1.825-.694.73-3.833,3.5-3.322.693.146.986-.913.292-1.059a3.01,3.01,0,0,0-2.483.438c-.694.511-.766,1.132-1.168,1.789-.037.694-.511.986-1.388.876a4.343,4.343,0,0,0-.584-.292c-.4-.037-.621-.256-1.059-.219a2.67,2.67,0,0,0-1.9,1.132,9.8,9.8,0,0,0-1.278,2.884c-.4,1.022-.8,1.752-2.044,1.862-.547.036-1.059-.146-1.606-.073a3.783,3.783,0,0,0-3.1,3.979c.073.73,1.168.73,1.132,0Z" transform="translate(-217.85 63.716)" fill="#f7f7f7"/>
    <path id="Path_598" data-name="Path 598" d="M114.319,408.595c5.147-10.331,5.95-21.976,8.981-32.965.766-2.811,1.533-6.1,3.65-8.214,3.1-3.1,7.045-.912,8.323,2.738.949,2.775.986,5.987,1.168,8.907.183,3.14.329,6.279.511,9.419.4,6.717,1.1,13.471,3.359,19.859.219.657,1.278.365,1.058-.292-2.373-6.717-3.03-13.835-3.395-20.918-.183-3.468-.329-6.972-.584-10.44-.219-2.774-.328-5.8-1.643-8.323a6.234,6.234,0,0,0-5.293-3.578c-2.373,0-4.344,1.643-5.549,3.541-1.57,2.519-2.3,5.695-3.03,8.542-.8,3.176-1.46,6.388-2.117,9.6-1.46,7.374-2.957,14.785-6.352,21.575-.365.621.584,1.168.913.548Z" transform="translate(135.276 37.186)" fill="#fff"/>
    <path id="Path_599" data-name="Path 599" d="M124.153,358.929c-.73-1.058.766-2.482,1.643-1.314.84,1.1-.73,2.191-1.606,1.351-.511-.475-1.278.292-.766.766a2.308,2.308,0,0,0,2.884.183,2.031,2.031,0,0,0,.365-2.957,2.057,2.057,0,0,0-2.994-.4,2.294,2.294,0,0,0-.438,2.92C123.6,360.061,124.554,359.513,124.153,358.929Z" transform="translate(130.443 41.585)" fill="#fff"/>
    <path id="Path_600" data-name="Path 600" d="M140.335,349.7c-.584-.84.694-1.606,1.241-.73.584.949-1.059,2.008-1.606.949-.328-.621-1.241-.073-.949.548a1.884,1.884,0,0,0,2.665.694,2.043,2.043,0,0,0,.8-2.775,1.954,1.954,0,0,0-2.592-.73,1.882,1.882,0,0,0-.584,2.555c.475.62,1.424.073,1.022-.511Z" transform="translate(122.329 45.961)" fill="#fff"/>
    <path id="Path_601" data-name="Path 601" d="M134.114,333.4c-.292-.84.621-1.716,1.351-1.022s-.11,1.679-.949,1.387c-.657-.219-.949.84-.292,1.059a1.95,1.95,0,0,0,2.008-3.213,1.889,1.889,0,0,0-2.446-.219,2.009,2.009,0,0,0-.73,2.3c.255.657,1.314.4,1.058-.292Z" transform="translate(125.374 54.226)" fill="#fff"/>
    <path id="Path_602" data-name="Path 602" d="M162.477,354.684c-.109-.073.037.037.073.109v.109h.146a1.061,1.061,0,0,1,.329.073,6.212,6.212,0,0,0-.4-.511v.876a6.306,6.306,0,0,1,.4-.511,1.449,1.449,0,0,1-.876.073c-.657-.219-.949.84-.292,1.059a2.014,2.014,0,0,0,1.46-.073c.219-.11.4-.256.4-.511V354.5a.515.515,0,0,0-.4-.511c-.584-.146-1.278-.219-1.643.365a.9.9,0,0,0,.292,1.351.584.584,0,0,0,.767-.183.668.668,0,0,0-.255-.84Z" transform="translate(110.956 42.69)" fill="#fff"/>
    <path id="Path_603" data-name="Path 603" d="M286.359,60.954c.183.073-.219,1.9-.036,2.336a1.8,1.8,0,0,0,1.424.949,3.061,3.061,0,0,0,2.153-.621c.657-.4,2.227-1.789,2.7-.256.11.329-.255,1.132-.146,1.57.329,1.46,1.716,1.387,2.7.621a7.352,7.352,0,0,0,.876-.986c.365-.876.913-1.059,1.606-.548a1.711,1.711,0,0,0,.109.621c.365.548.548.913,1.278.876.694-.036.694-1.132,0-1.1.037,0-.767-1.643-1.2-1.825a1.747,1.747,0,0,0-1.57.256,4.019,4.019,0,0,0-.84.73c-.548.548-.511,1.351-1.388.8-.84-.511-.146-1.314-.73-2.044-.949-1.2-2.555-.548-3.577.11-.584.365-2.045,1.679-2.409-.256-.037-.256.182-.657.182-.913a1.076,1.076,0,0,0-.62-1.168c-.584-.438-1.132.511-.511.84Z" transform="translate(48.039 191.091)" fill="#f7f7f7"/>
    <path id="Path_604" data-name="Path 604" d="M209.5,233.865c.438,5.111.876,10.222,1.351,15.332a.548.548,0,0,0,1.1,0c0-.109.037-.255.037-.365.073-.694-1.022-.694-1.1,0,0,.109-.037.255-.037.365h1.1c-.438-5.111-.876-10.222-1.35-15.332C210.522,233.172,209.427,233.135,209.5,233.865Z" transform="translate(86.713 103.566)" fill="#fff"/>
    <path id="Path_605" data-name="Path 605" d="M210.5,233.73c1.534-.329,4.2-.8,5.367.474,1.058,1.132.8,3.359.949,4.892.255,3.066.511,6.133.767,9.163.073.694,1.168.694,1.1,0-.365-4.381-.4-8.981-1.132-13.325-.548-3.395-4.782-2.774-7.337-2.263C209.511,232.817,209.8,233.876,210.5,233.73Z" transform="translate(86.556 104.104)" fill="#fff"/>
    <path id="Path_606" data-name="Path 606" d="M230.074,193.086a269.018,269.018,0,0,1,1.2,35.155c-.037.694,1.059.694,1.1,0a269.238,269.238,0,0,0-1.2-35.155C231.1,192.393,230,192.356,230.074,193.086Z" transform="translate(76.323 124.158)" fill="#fff"/>
    <path id="Path_607" data-name="Path 607" d="M232.327,192.513a24.837,24.837,0,0,1,4.709.73c1.862.657,1.57,4.016,1.752,5.732.329,3.322.548,6.644.766,9.966.4,6.9.548,13.8.694,20.7a.548.548,0,0,0,1.1,0c-.183-7.958-.365-15.953-.913-23.911-.255-3.723-.438-7.484-1.022-11.171-.511-3.14-4.271-2.738-6.79-3.14-.694-.036-.986.986-.292,1.1Z" transform="translate(75.385 124.731)" fill="#fff"/>
    <path id="Path_608" data-name="Path 608" d="M251.462,179.236q1.862,20.808,3.76,41.58c.073.694,1.168.694,1.1,0q-1.862-20.808-3.76-41.58c-.036-.694-1.132-.694-1.1,0Z" transform="translate(65.522 131.145)" fill="#fff"/>
    <path id="Path_609" data-name="Path 609" d="M252.162,179.384a18.615,18.615,0,0,1,5.33-.255c1.5.146,2.555.438,2.628,2.045.037.548.037,1.132.037,1.679.109,2.738.219,5.476.365,8.214.475,10.477,1.022,20.918,1.57,31.4.037.694,1.132.694,1.1,0-.584-11.317-1.168-22.6-1.68-33.914-.109-2.373-.219-4.709-.292-7.082-.036-1.059-.036-2.118-1.022-2.811a6.772,6.772,0,0,0-3.395-.694,18.715,18.715,0,0,0-4.929.365c-.694.146-.4,1.2.292,1.058Z" transform="translate(65.517 131.544)" fill="#fff"/>
    <path id="Path_610" data-name="Path 610" d="M189.93,172.393c-1.2,2.373-2.373,4.745-3.578,7.118-.329.621.621,1.168.949.548,1.2-2.373,2.373-4.746,3.578-7.119C191.171,172.32,190.222,171.735,189.93,172.393Z" transform="translate(98.434 134.484)" fill="#fff"/>
    <path id="Path_611" data-name="Path 611" d="M193.922,174.206a9.9,9.9,0,0,0,3.322,5.366c.548.438,1.314-.329.767-.767a9,9,0,0,1-3.03-4.855.544.544,0,1,0-1.058.255Z" transform="translate(94.588 133.766)" fill="#fff"/>
    <path id="Path_612" data-name="Path 612" d="M192.379,175.4a166.105,166.105,0,0,0,6.206,47.348.549.549,0,0,0,1.059-.292,165.179,165.179,0,0,1-6.17-47.056A.548.548,0,0,0,192.379,175.4Z" transform="translate(95.364 133.081)" fill="#fff"/>
    <path id="Path_613" data-name="Path 613" d="M207.055,271.065q12.814-1.2,25.7-1.278c7.995-.037,16.647-.365,24.021,3.249.62.292,1.168-.62.548-.949-7.155-3.5-15.332-3.432-23.145-3.4q-13.58,0-27.124,1.278c-.694.073-.694,1.168,0,1.1Z" transform="translate(88.208 85.715)" fill="#fff"/>
    <path id="Path_614" data-name="Path 614" d="M298.675,268.841a47.19,47.19,0,0,1,5.622,3.979q.055-.438.109-.876c-1.5,1.132-2.957,2.263-4.454,3.4-.548.438,0,1.387.548.949,1.5-1.132,2.957-2.263,4.453-3.395a.574.574,0,0,0,.109-.876,45.7,45.7,0,0,0-5.841-4.125.548.548,0,0,0-.548.949Z" transform="translate(41.819 86.15)" fill="#fff"/>
    <path id="Path_615" data-name="Path 615" d="M199.1,163.615l1.643,1.971a.576.576,0,0,0,.876-.109c2.921-5.732,5.8-11.5,8.762-17.231.766-1.46,1.825-.255,2.92.146a3.026,3.026,0,0,0,2.117-.073c1.752-.657,2.921-2.483,3.724-4.089,1.314-2.738,2.482-5.585,3.76-8.36.329-.766.694-1.533,1.022-2.263a5.407,5.407,0,0,1,.767-1.679c1.132-1.022,3.906,1.314,4.855,1.9,1.57.949,3.833,3.066,5.877,2.519,1.533-.4,2.263-2.081,2.7-3.468a53.791,53.791,0,0,1,8.981-17.048.552.552,0,0,0-.548-.913,17.332,17.332,0,0,0-5.184,2.774c-.511.4.146,1.132.657.876,1.424-.694,2.884-1.424,4.307-2.117-.255-.109-.548-.219-.8-.329.438,2.191.876,4.381,1.278,6.571.146.694,1.2.4,1.059-.292-.438-2.19-.876-4.381-1.278-6.571a.556.556,0,0,0-.8-.329c-1.424.694-2.884,1.424-4.307,2.117l.657.876a15.026,15.026,0,0,1,4.709-2.519c-.183-.292-.365-.62-.548-.912a51.34,51.34,0,0,0-7.7,13c-.511,1.242-.949,2.519-1.351,3.8-.328,1.022-.84,3.212-2.19,3.431-1.533.256-3.5-1.5-4.746-2.263s-2.446-1.606-3.723-2.3c-1.46-.767-2.373-.475-3.1.949-1.387,2.738-2.519,5.658-3.8,8.469-.949,2.117-2.154,6.389-4.745,7.082-1.752.475-3.359-2.153-4.929-.62-.912.876-1.387,2.519-1.935,3.614-.84,1.643-1.643,3.249-2.483,4.892-1.643,3.249-3.322,6.5-4.965,9.747l.876-.109-1.643-1.971c-.475-.475-1.241.292-.767.84Z" transform="translate(92.038 163.376)" fill="#fff"/>
    <path id="Path_616" data-name="Path 616" d="M515.215,14.885a7.673,7.673,0,0,1-2.847-10.514C514.668.758,520.253.1,523.5,2.765a7.831,7.831,0,0,1,.183,11.353c-3.286,3.03-8.8,2.227-11.5-1.132-.438-.548-1.2.219-.766.767a9.411,9.411,0,0,0,10.7,2.628,8.622,8.622,0,0,0,4.965-9.126A8.084,8.084,0,0,0,519.851.064c-3.687-.438-7.63,1.387-9.017,4.965A8.837,8.837,0,0,0,514.7,15.762c.584.4,1.132-.548.511-.876Z" transform="translate(-65.167 221.389)" fill="#f7f7f7"/>
    <path id="Path_617" data-name="Path 617" d="M550.771,38.229c-1.533-2.847,2.409-5.987,4.381-2.92,1.752,2.7-2.555,4.746-3.943,2.227-.329-.621-1.278-.073-.949.548a3.535,3.535,0,0,0,4.527,1.351,3.125,3.125,0,0,0,1.314-4.673,3.574,3.574,0,0,0-5.111-1.2,4.137,4.137,0,0,0-1.2,5.257c.4.584,1.351.036.986-.584Z" transform="translate(-84.879 204.763)" fill="#f7f7f7"/>
    <path id="Path_618" data-name="Path 618" d="M512.913,600.311h86.154a.548.548,0,0,0,0-1.1H512.913A.548.548,0,0,0,512.913,600.311Z" transform="translate(-66.224 -81.19)" fill="#f7f7f7"/>
    <path id="Path_619" data-name="Path 619" d="M723.4,600.311H739.83a.548.548,0,0,0,0-1.1H723.4A.548.548,0,0,0,723.4,600.311Z" transform="translate(-172.525 -81.19)" fill="#f7f7f7"/>
    <path id="Path_620" data-name="Path 620" d="M345.765,633.791H403.08a.548.548,0,0,0,0-1.1H345.765A.548.548,0,0,0,345.765,633.791Z" transform="translate(18.165 -98.096)" fill="#f7f7f7"/>
    <path id="Path_621" data-name="Path 621" d="M460.067,624.941h49.648a.548.548,0,0,0,0-1.1H460.067A.548.548,0,0,0,460.067,624.941Z" transform="translate(-39.553 -93.627)" fill="#f7f7f7"/>
    <path id="Path_622" data-name="Path 622" d="M190.258,618.336h14.967a.822.822,0,0,0,0-1.643H190.258A.822.822,0,0,0,190.258,618.336Z" transform="translate(96.828 -90.016)" fill="#f7f7f7"/>
    <path id="Path_623" data-name="Path 623" d="M230.816,618.336h5.111a.822.822,0,0,0,0-1.643h-5.111A.822.822,0,0,0,230.816,618.336Z" transform="translate(76.348 -90.016)" fill="#f7f7f7"/>
    <path id="Path_624" data-name="Path 624" d="M94.3,567.872a6.426,6.426,0,0,0,1.022-.547c-.073-.036-.073.547-.073.694a7.4,7.4,0,0,0-.11,1.57,1.217,1.217,0,0,0,1.424,1.022,1.884,1.884,0,0,0,1.022-.475,6.514,6.514,0,0,1,.621-.511,7.006,7.006,0,0,1,.109.913,1.36,1.36,0,0,0,1.424,1.2c.694-.073.694-1.168,0-1.1-.365.037-.475-1.935-1.1-2.154a.933.933,0,0,0-.767.073c-.548.255-1.351,1.424-1.606.4-.183-.767.584-1.862-.109-2.519-.657-.62-1.752.11-2.373.438a.555.555,0,0,0,.511.986Z" transform="translate(145.289 -64.522)" fill="#fff"/>
    <path id="Path_625" data-name="Path 625" d="M563.475,374.522q-18.125,21.958-36.214,43.88l56.84,14.127a75.311,75.311,0,0,0-7.958-40.631,72.3,72.3,0,0,0-12.668-17.376Z" transform="translate(-73.745 32.271)" fill="#fff"/>
    <path id="Path_626" data-name="Path 626" d="M520.108,469.134q28.694,7.228,57.424,14.42a41.99,41.99,0,0,1-10.039,24.459Q543.837,488.573,520.108,469.134Z" transform="translate(-70.133 -15.504)" fill="#f7f7f7"/>
    <path id="Path_627" data-name="Path 627" d="M449.526,467.237c35.142,0,63.63-29.207,63.63-65.236s-28.488-65.236-63.63-65.236S385.9,365.973,385.9,402,414.384,467.237,449.526,467.237Z" transform="translate(-2.361 51.336)" fill="none" stroke="#161616" stroke-width="0.834"/>
    <path id="Path_628" data-name="Path 628" d="M515.094,464.489a.422.422,0,0,1,.584.146c.037-.146.037-.292.073-.438-.037.073-.146.036-.183,0a.522.522,0,0,0-.657.365.551.551,0,0,0,.365.657,1.187,1.187,0,0,0,1.241-.292.829.829,0,0,0-.073-1.2,1.489,1.489,0,0,0-2.081-.073.556.556,0,1,0,.73.84Z" transform="translate(-67.127 -12.538)" fill="#161616"/>
    <path id="Path_629" data-name="Path 629" d="M555.4,365.492a600.94,600.94,0,0,1-37.491,48.955c-.438.548.329,1.314.767.766a600.286,600.286,0,0,0,37.674-49.173C556.752,365.455,555.8,364.908,555.4,365.492Z" transform="translate(-68.957 36.957)" fill="#161616"/>
    <path id="Path_630" data-name="Path 630" d="M516.532,466.055q30.884,5.531,61.33,13.069a.549.549,0,0,0,.292-1.059q-30.446-7.5-61.33-13.069c-.694-.109-.986.949-.292,1.059Z" transform="translate(-68.127 -13.411)" fill="#161616"/>
    <path id="Path_631" data-name="Path 631" d="M516.6,465.84q23.382,21.411,48.187,41.215c.547.438,1.314-.329.767-.767q-24.751-19.768-48.188-41.215C516.858,464.6,516.055,465.366,516.6,465.84Z" transform="translate(-68.271 -13.379)" fill="#161616"/>
    <path id="Path_632" data-name="Path 632" d="M449.376,337.033c1.35,6.023,2.263,14.6-1.059,23.363-7.557,20.005-29.5,20.991-37.82,37.309-4.527,8.871-5.658,23.546,8.652,49.1v12.886a63.739,63.739,0,0,1-13.4-9.966c-17.048-16.683-19.676-38.477-20.224-46.764.036-5.987.876-25.445,15.223-42.821,3.249-3.943,14.785-17.45,34.425-21.794A58.786,58.786,0,0,1,449.376,337.033Z" transform="translate(-2.176 51.215)" fill="#6d6c6c" opacity="0.21"/>
    <path id="Path_633" data-name="Path 633" d="M291.071,536.946c2.519,6.352,5.038,12.668,7.52,19.019-1.5,3.286-3.8,8.141-6.973,13.945-4.162,7.593-6.133,10.149-5.476,10.915.949,1.1,7.119-1.643,31.066-19.567-1.5-4.162-3.03-8.36-4.527-12.631q-3.286-9.309-6.389-18.435Q298.737,533.588,291.071,536.946Z" transform="translate(48.072 -46.337)" fill="#45462a"/>
    <path id="Path_634" data-name="Path 634" d="M352.624,304.1c4.052,6.9,1.168,17.6.766,19.02-2.227,8.287-7.264,9.09-25.736,26.576-11.573,10.988-14.894,15.442-17.34,20.334a51.84,51.84,0,0,0-5,17.194c1.5,8.761,2.994,17.486,4.49,26.248a63.192,63.192,0,0,1-16.062,7.52,64.489,64.489,0,0,1-12.814,2.519,86.533,86.533,0,0,1-5.586-43.3c3.14-23.254,15.442-38.477,25.663-51.108a154.686,154.686,0,0,1,48.955-39.682,20.383,20.383,0,0,0,.73,10.842,20.716,20.716,0,0,0,1.935,3.833Z" transform="translate(53.836 75.242)" fill="#fff"/>
    <path id="Path_635" data-name="Path 635" d="M361.841,299.083a130.688,130.688,0,0,1-15,29.1c-14.785,21.5-25.7,23.254-34.425,43.223a93.956,93.956,0,0,0-7.155,29.351,101.316,101.316,0,0,0,.913,25.189,31.042,31.042,0,0,0,7.41-1.168,30.643,30.643,0,0,0,11.536-6.243c-.62-2.957-1.533-7.338-2.555-12.7-1.387-7.447-2.081-11.207-2.081-13.872,0-7.849,3.322-13.763,6.023-18.472,1.278-2.227,5-8.4,18.034-20.334,13.763-12.594,19.494-14.785,23.109-24.021a28.924,28.924,0,0,0,2.081-11.317c-.329-9.6-5.732-16.318-7.886-18.727Z" transform="translate(38.559 70.364)" fill="#6d6c6c" opacity="0.2"/>
    <path id="Path_636" data-name="Path 636" d="M375.008,342.96c-7.484,6.279-18.18,15.661-30.482,27.708C326.675,388.19,323.5,393.3,322.112,395.857c-3.87,7.119-10.514,23.327-3.943,50.378,2.153-.876,4.381-1.861,6.717-2.993,1.862-.913,3.651-1.862,5.33-2.775q-2.191-12.814-4.381-25.663a39.406,39.406,0,0,1,2.774-13.4,44.732,44.732,0,0,1,5.768-9.93c2.993-4.089,5.878-6.863,11.536-12.229,4.527-4.307,8.907-8.834,13.617-12.923,8.47-7.337,12.266-9.345,14.31-15a21.059,21.059,0,0,0,1.168-8.36Z" transform="translate(33.24 48.208)" fill="#6d6c6c" opacity="0.27"/>
    <path id="Path_637" data-name="Path 637" d="M377.416,530.372l16.647,12.814c-7.374,18.691-7.374,23.546-5.878,24.386,2.483,1.424,10.623-7.374,15.26-13.836a63.923,63.923,0,0,0,7.922-15.04c-2.263-2.884-4.855-5.877-7.739-8.944a142.965,142.965,0,0,0-11.572-10.988c-4.855,3.906-9.747,7.739-14.639,11.609Z" transform="translate(1.921 -40.565)" fill="#45462a"/>
    <path id="Path_638" data-name="Path 638" d="M413.667,308.917c3.249,8.615,1.278,16.939.766,19.019-3.724,15.478-17.559,28.255-22.451,32.709-8.36,7.666-15.3,11.5-19.165,21.721a34.312,34.312,0,0,0-2.117,8.725q4,9.145,7.995,18.253a39.342,39.342,0,0,1-13.324,15.989,37.848,37.848,0,0,1-9.127,4.563c-5.549-5.658-20.589-22.451-19.786-44.9.62-17.23,10.368-31.1,17.888-41.872,7.556-10.769,9.491-9.455,19.019-23.217,8.25-11.937,9.82-17.377,16.756-19.786,6.972-2.446,17.194-1.1,21.684,4.965a16.957,16.957,0,0,1,1.862,3.833Z" transform="translate(22.618 70.424)" fill="#fff"/>
    <path id="Path_639" data-name="Path 639" d="M414.423,314.706a43.672,43.672,0,0,1-.986,15.771c-5.841,22.6-27.233,24.568-37.528,49.027-2.007,4.782-6.68,16.355-2.993,29.752a42.47,42.47,0,0,0,13.251,20.516,18.748,18.748,0,0,0,5.987-5,19.109,19.109,0,0,0,3.76-8.506c-2.592-6.242-5.184-12.522-7.739-18.764a35.984,35.984,0,0,1,3.76-11.025c3.139-5.914,6.936-8.907,11.828-13.325,8.981-8.068,14.712-13.178,19.677-20.224,3.14-4.49,7.7-11.1,9.2-20.771a43.075,43.075,0,0,0-1.2-18.472c-5.658.365-11.353.694-17.012,1.022Z" transform="translate(4.887 62.991)" fill="#6d6c6c" opacity="0.24"/>
    <path id="Path_640" data-name="Path 640" d="M443.684,339.42A126.456,126.456,0,0,1,428.169,361.8c-14.749,16.975-24.751,20.516-31.614,33.586-2.957,5.622-5.658,14.894-2.336,29.789,4.2,1.679,8.433,3.358,12.631,5.074-2.665-6.279-5.293-12.522-7.958-18.8A31.193,31.193,0,0,1,402.505,399c2.3-4.235,4.855-6.461,10.478-11.572,9.309-8.469,13.981-12.7,15.88-14.822,4.344-4.782,9.09-10.148,12.083-18.435a44.659,44.659,0,0,0,2.738-14.748Z" transform="translate(-5.793 49.996)" fill="#6d6c6c" opacity="0.25"/>
    <path id="Path_641" data-name="Path 641" d="M299.589,202.36a24.9,24.9,0,0,0-5.841-8.25c-3.943-3.614-9.455-6.206-10.331-5.147-.365.438-.329,1.971,6.316,9.309" transform="translate(49.448 126.092)" fill="#fff"/>
    <path id="Path_642" data-name="Path 642" d="M299.589,202.36a24.9,24.9,0,0,0-5.841-8.25c-3.943-3.614-9.455-6.206-10.331-5.147-.365.438-.329,1.971,6.316,9.309" transform="translate(49.448 126.092)" fill="none" stroke="#161616" stroke-width="0.556"/>
    <path id="Path_643" data-name="Path 643" d="M282.134,204.176a13.137,13.137,0,0,0-5.293,5.95,7.255,7.255,0,0,0-.694,4.709c.84,3.212,4.709,4.563,5.038,4.709,1.57.548,1.971.146,3.1.8,1.825,1.058,1.752,2.482,3.1,3.8,2.775,2.665,8.4,1.606,8.652.475.146-.621-1.46-1.2-1.314-2.337.073-.694.767-.986,1.242-1.424,2.555-2.336.109-9.163-.329-10.294a22.7,22.7,0,0,0-2.117-4.308" transform="translate(53.14 118.289)" fill="#fff"/>
    <path id="Path_644" data-name="Path 644" d="M282.134,204.176a13.137,13.137,0,0,0-5.293,5.95,7.255,7.255,0,0,0-.694,4.709c.84,3.212,4.709,4.563,5.038,4.709,1.57.548,1.971.146,3.1.8,1.825,1.058,1.752,2.482,3.1,3.8,2.775,2.665,8.4,1.606,8.652.475.146-.621-1.46-1.2-1.314-2.337.073-.694.767-.986,1.242-1.424,2.555-2.336.109-9.163-.329-10.294a22.7,22.7,0,0,0-2.117-4.308" transform="translate(53.14 118.289)" fill="none" stroke="#161616" stroke-width="0.556"/>
    <path id="Path_645" data-name="Path 645" d="M275.021,224.064a8.531,8.531,0,0,0,4.673,5c2.263.912,3.943-.292,3.979-2.7a.548.548,0,0,0-1.1,0c-.036,2.811-2.811,1.789-4.2.657a7.377,7.377,0,0,1-2.3-3.249c-.255-.657-1.314-.4-1.058.292Z" transform="translate(53.646 108.586)" fill="#161616"/>
    <path id="Path_646" data-name="Path 646" d="M280.522,217.866c1.1,2.045,2.555,4.308,4.819,5.147,2.044.766,3.249-.475,3.468-2.409.073-.694-1.022-.694-1.1,0-.329,2.884-3.14.876-4.125-.146a13.182,13.182,0,0,1-2.117-3.14c-.329-.621-1.278-.073-.949.548Z" transform="translate(50.882 111.79)" fill="#161616"/>
    <path id="Path_647" data-name="Path 647" d="M287.172,207.887a26.36,26.36,0,0,0,2.227,4.271c.62.912,1.5,2.227,2.7,2.445,2.847.548,1.935-3.5,1.387-4.891-.255-.657-1.314-.365-1.059.292a5,5,0,0,1,.584,2.592c-.62,2.007-2.3-.475-2.665-.986a26.339,26.339,0,0,1-2.227-4.271c-.292-.657-1.242-.11-.949.548Z" transform="translate(47.518 116.841)" fill="#161616"/>
    <path id="Path_648" data-name="Path 648" d="M287.5,204.132c1.1-.548,2.446.438,3.213,1.168a7.7,7.7,0,0,1,1.679,2.629c.255.657,1.314.365,1.059-.292-.913-2.263-3.614-5.878-6.5-4.454C286.329,203.475,286.877,204.424,287.5,204.132Z" transform="translate(47.74 118.953)" fill="#161616"/>
    <path id="Path_649" data-name="Path 649" d="M373.221,176.795c1.168,3.468-.621,6.425-5.622,15.332-8.4,14.822-8.907,16.136-10.842,18.472-4.563,5.476-7.155,8.579-11.937,10.587-8.725,3.65-18.4.438-23.692-1.57a43.66,43.66,0,0,1-10.952-6.17c-1.606-1.314-2.847-2.336-3.651-2.957-1.679-1.351-2.445-1.935-3.1-3.1a6.907,6.907,0,0,1-.8-2.446c-.547-3.906,3.322-7.191,3.614-7.447-4.637,1.1-6.242.876-6.535.219-.694-1.46,4.344-6.827,7.885-5.622,1.679.548,2.519,2.446,3.14,3.833a11.747,11.747,0,0,1,.912,6.315,33.589,33.589,0,0,0,9.455,4.527,34.542,34.542,0,0,0,18.253.219c1.862-2.994,4.892-7.886,8.579-13.982,10.076-16.72,12.74-22.013,17.376-22.086,3.468-.037,6.9,2.847,7.922,5.877Z" transform="translate(41.197 135.083)" fill="#fff" stroke="#161616" stroke-width="0.556"/>
    <path id="Path_650" data-name="Path 650" d="M308.238,226.516c-3.14-.146-3.578,3.906-3.979,6.206.365.037.73.109,1.059.146a8.4,8.4,0,0,1,1.387-3.906.548.548,0,0,0-.949-.548c-1.424,2.3-2.3,5.147-.328,7.41l.766-.766a2.514,2.514,0,0,1-.876-1.862c-.365.037-.73.109-1.059.146a9.582,9.582,0,0,0,.694,1.679c.329.621,1.278.073.949-.548a4.881,4.881,0,0,1-.584-1.424c-.146-.584-1.132-.475-1.059.146a4.01,4.01,0,0,0,1.2,2.628c.511.511,1.242-.255.767-.766-1.606-1.862-.584-4.235.511-6.1-.328-.183-.62-.365-.949-.548a9.306,9.306,0,0,0-1.533,4.453c-.037.621.986.767,1.059.146.292-1.679.548-5.476,2.92-5.4.694.037.694-1.058,0-1.1Z" transform="translate(38.863 107.01)" fill="#fff"/>
    <path id="Path_651" data-name="Path 651" d="M355.546,173.941a157.127,157.127,0,0,1-11.9,24.934c-5.987,10.185-9.127,15.3-14.529,18-11.1,5.512-28.219,1.789-33.987-8.761a18.506,18.506,0,0,1-2.154-8.908,8.3,8.3,0,0,1-5.622.183c-4.673-1.862-5.111-8.834-7.593-8.761-1.643.073-2.847,3.176-3.286,4.307a7.592,7.592,0,0,0-.657,4.454c.584,2.592,3.322,4.636,6.753,5.293a11.8,11.8,0,0,1,3.614,2.628c1.2,1.314,1.533,2.337,2.811,2.957a5.142,5.142,0,0,0,3.14.329,75.169,75.169,0,0,0,7.922,5.622c7.52,4.6,16.61,10.148,26.9,8.25a26.216,26.216,0,0,0,10.222-4.453c3.322-2.227,6.9-5.586,13.69-17.815a200.621,200.621,0,0,0,9.747-20.151,6.019,6.019,0,0,0-.182-4.782,6.15,6.15,0,0,0-4.892-3.322Z" transform="translate(53.287 133.556)" fill="#6d6c6c" opacity="0.21"/>
    <path id="Path_652" data-name="Path 652" d="M293.456,225.05c-.511.365-.292,1.1-.913,1.643a2.1,2.1,0,0,1-1.387.365,3.036,3.036,0,0,1-.475,1.752c-.511.73-1.2.876-1.168,1.168.073.329,1.1.146,2.081.694a3.784,3.784,0,0,1,1.387,1.5c2.409-2.3,4.819-4.563,7.228-6.863a6.726,6.726,0,0,1-4.089.365C294.952,225.305,294.076,224.612,293.456,225.05Z" transform="translate(46.309 107.819)" fill="#6d6c6c" opacity="0.22"/>
    <path id="Path_653" data-name="Path 653" d="M427.212,158.171c-2.446,2.446-3.724,5.22-6.206,10.769a67.78,67.78,0,0,0-6.206,21.283c-1.168,10.806,1.57,13.252-.621,21.283a34.417,34.417,0,0,1-7.045,13.215,43.517,43.517,0,0,0,16.318,10.331,44.809,44.809,0,0,0,29.35-.219c3.249-8.761,6.462-17.523,9.711-26.248.037-7.411.329-13.726.621-18.618.73-11.682,1.935-20.626,2.7-26.065.876-6.388,1.789-11.755,2.482-15.7a160.952,160.952,0,0,0-19.859,2.263C432.542,153.389,428.782,156.6,427.212,158.171Z" transform="translate(-13.086 146.552)" fill="#fff" stroke="#161616" stroke-width="0.556"/>
    <path id="Path_654" data-name="Path 654" d="M460.45,150.947a55.63,55.63,0,0,1,4.235,18.144c1.132,18.837-7.958,33.585-14.858,44.756A123,123,0,0,1,434.64,233.6,38.326,38.326,0,0,0,446.4,237.5a39.248,39.248,0,0,0,20.078-2.117c3.067-8.835,6.1-17.632,9.163-26.467.182-6.024.511-12.266.986-18.618,1.1-14.821,2.884-28.73,5.074-41.653l-21.246,2.3Z" transform="translate(-26.976 146.328)" fill="#6d6c6c" opacity="0.19"/>
    <path id="Path_655" data-name="Path 655" d="M471.676,150a69.062,69.062,0,0,1,4.746,28.584c-1.752,33.111-27.963,53.153-32.49,56.511a40.817,40.817,0,0,0,11.1,2.3,40.379,40.379,0,0,0,15.7-2.117c3.358-8.835,6.753-17.632,10.112-26.467,0-8.068.292-16.683.986-25.809a325.876,325.876,0,0,1,4.417-33.987L471.676,150Z" transform="translate(-31.667 146.142)" fill="#6d6c6c" opacity="0.11"/>
    <path id="Path_656" data-name="Path 656" d="M523.282,147.246a22.4,22.4,0,0,1,10.44,7.484c3.5,4.782,4.271,10.55,2.263,28.839-1.862,17.012-3.468,20.663-4.089,30.884a116.284,116.284,0,0,0,1.351,26.32l12.485-2.957a18.909,18.909,0,0,1,8.615,4.783c1.35,1.314,4.2,4.089,3.4,6.352-.511,1.46-2.373,2.19-3.176,2.482a8.9,8.9,0,0,1-4.307.438c-3.03-.182-6.06-.292-9.09-.438a64.662,64.662,0,0,0-6.571,0c3.687,3.176,5.257,5.221,4.782,6.133-.657,1.2-5.038.365-13.178-2.482-.365-2.957-.949-7.447-1.825-12.923-2.263-14.31-3.906-19.421-5-27.014-1.132-7.849-.766-14.931,0-29.058.584-10,1.643-23.218,3.906-38.842Z" transform="translate(-69.4 147.036)" fill="#fff" stroke="#161616" stroke-width="0.556"/>
    <path id="Path_657" data-name="Path 657" d="M561.557,346.181c2.811-1.168,5.732-.511,8.433.694a20.4,20.4,0,0,1,6.352,3.943c.511.475,1.278-.292.767-.767a22.152,22.152,0,0,0-7.338-4.417c-2.738-1.132-5.695-1.643-8.542-.474-.584.219-.292,1.278.328,1.022Z" transform="translate(-90.738 47.433)" fill="#161616"/>
    <path id="Path_658" data-name="Path 658" d="M568.74,339.858c4.855-.949,10.623,1.935,13.288,6.06.4.584,1.35.036.949-.548-2.993-4.563-9.127-7.593-14.529-6.535C567.754,338.945,568.046,340,568.74,339.858Z" transform="translate(-94.343 50.398)" fill="#161616"/>
    <path id="Path_659" data-name="Path 659" d="M577.392,334.91a12.085,12.085,0,0,1,5.293,1.825c.621.329,1.168-.62.548-.949a13.56,13.56,0,0,0-5.841-1.971C576.7,333.778,576.7,334.873,577.392,334.91Z" transform="translate(-98.797 52.827)" fill="#161616"/>
    <path id="Path_660" data-name="Path 660" d="M529.031,149.016a76.479,76.479,0,0,1,5.732,19.458c3.176,21.1-4.637,32.089-3.249,54.9a96.034,96.034,0,0,0,4.745,24.35,30.167,30.167,0,0,1,12.594-1.314c3.688.438,5.513.657,7.338,1.971s2.884,3.067,4.745,2.957a2.553,2.553,0,0,0,2.117-1.132c.986-1.789-1.715-4.855-2.628-5.877a18.809,18.809,0,0,0-8.981-5.549l-12.923,2.628a85.546,85.546,0,0,1-2.117-17.632c-.109-6.571,1.059-14.529,3.432-30.227.438-2.92.986-8.8,2.117-20.589.365-3.87.657-7.338-.657-11.609-.657-2.154-2.263-7.045-7.009-10.112a14.574,14.574,0,0,0-5.257-2.227Z" transform="translate(-74.639 146.142)" fill="#6d6c6c" opacity="0.16"/>
    <path id="Path_661" data-name="Path 661" d="M545.181,357.266a11.216,11.216,0,0,0,3.578,4.746c1.278.949,3.5,2.117,3.906,1.643.511-.584-1.424-3.8-4.417-5.4A8.921,8.921,0,0,0,545.181,357.266Z" transform="translate(-82.794 40.984)" fill="#6d6c6c" opacity="0.22"/>
    <path id="Path_662" data-name="Path 662" d="M546.921,209.706a103.823,103.823,0,0,0-6.206,41.836,102.608,102.608,0,0,0,4.235,23.509,81.875,81.875,0,0,1-2.629-17.3c-.438-10.915,1.534-17.048,3.578-37.09.511-4.636.84-8.469,1.022-10.952Z" transform="translate(-80.445 115.496)" fill="#6d6c6c" opacity="0.4"/>
    <path id="Path_663" data-name="Path 663" d="M568.335,337.861c-.073-.438,3.578-2.117,7.191-1.314,4.855,1.1,7.119,6.206,7.52,7.192a16.6,16.6,0,0,0-8.506-5.4c-3.4-.767-6.169-.109-6.206-.475Z" transform="translate(-94.485 51.555)" fill="#6d6c6c" opacity="0.22"/>
    <path id="Path_664" data-name="Path 664" d="M417.436,130.129c-1.533,1.424-1.862,4.417-2.482,10.295-.694,6.753-.986,10.185.438,11.9,2.592,3.14,8.177,3.322,12.047,1.606a12.824,12.824,0,0,0,6.535-7.556c1.789-5.075,1.2-12.741-4.2-16.245-3.65-2.409-9.309-2.811-12.339,0Z" transform="translate(-16.744 156.669)" fill="#212121"/>
    <path id="Path_665" data-name="Path 665" d="M436.39,130.754c.548,2.628,3.468,2.847,5.95,7.045a16.788,16.788,0,0,1,2.19,8.36,24.3,24.3,0,0,0,17.961-8.469c-.913-1.5-2.263-3.687-3.87-6.316-6.936-11.134-7.958-12.01-9.345-12.376-5.622-1.35-13.945,6.863-12.886,11.755Z" transform="translate(-27.813 161.374)" fill="#fff" stroke="#161616" stroke-width="0.556"/>
    <path id="Path_666" data-name="Path 666" d="M415.267,155.083a7.2,7.2,0,1,0,14.274-1.716,13.975,13.975,0,0,1,2.482,4.125,13.287,13.287,0,0,1,.876,3.833c-.547.913-3.395,5.44-8.725,6.316-.913.146-6.1.912-8.542-2.191a8.387,8.387,0,0,1-1.351-4.892A13.664,13.664,0,0,1,415.267,155.083Z" transform="translate(-16.692 143.945)" fill="#150aa6" opacity="0.74"/>
    <path id="Path_667" data-name="Path 667" d="M384.114,73.275c-1.679,3.1-1.278,6.242-.474,12.375.949,6.936,2.19,12.741,3.687,16.574a1.328,1.328,0,0,0,1.424.84,15.7,15.7,0,0,0,2.7-.913,21.361,21.361,0,0,0,4.6,5.658,21.756,21.756,0,0,0,11.609,5.038,22.2,22.2,0,0,0,11.025-8.871,20.932,20.932,0,0,0,2.92-8.1,19.683,19.683,0,0,0-1.971-11.025,7.728,7.728,0,0,0,2.738-2.738c1.679-2.957,1.606-7.849-1.059-9.163-2.008-.986-5.184.109-7.338,2.92a21.037,21.037,0,0,0-3.358-4.417A22.661,22.661,0,0,0,403.9,66.7C397.329,64.112,387.546,66.959,384.114,73.275Z" transform="translate(-0.872 188.188)" fill="#fff" stroke="#161616" stroke-width="0.556"/>
    <path id="Path_668" data-name="Path 668" d="M452.617,133.7a61.025,61.025,0,0,1-5.732,10.222l.876-.11c-.219-.255-.438-.548-.62-.8a4.008,4.008,0,0,0-.073.657,11.213,11.213,0,0,0,2.337-2.738c-.329-.183-.621-.365-.95-.548a4.157,4.157,0,0,1-2.227,2.227l.657.876a41.524,41.524,0,0,0,5.147-5.878c.4-.584-.548-1.132-.949-.548a38.558,38.558,0,0,1-4.965,5.658c-.475.438.146,1.1.657.876a5.131,5.131,0,0,0,2.629-2.629.548.548,0,0,0-.95-.548,12.885,12.885,0,0,1-2.154,2.519.5.5,0,0,0-.073.657,4.325,4.325,0,0,0,.8,1.022.577.577,0,0,0,.876-.11,59.576,59.576,0,0,0,5.732-10.222c.219-.657-.73-1.2-1.022-.584Z" transform="translate(-32.687 154.016)" fill="#161616"/>
    <path id="Path_669" data-name="Path 669" d="M403.556,139.194c2.519-.657,3.979-2.774,5.257-4.855.365-.584-.584-1.132-.949-.548-1.132,1.825-2.409,3.76-4.6,4.381-.694.146-.4,1.2.292,1.022Z" transform="translate(-10.932 153.956)" fill="#161616"/>
    <path id="Path_670" data-name="Path 670" d="M410.779,132.486a2.423,2.423,0,0,1,1.789.876c.475.548,1.241-.256.767-.767a3.472,3.472,0,0,0-2.555-1.2c-.73-.037-.73,1.058,0,1.1Z" transform="translate(-14.65 155.042)" fill="#161616"/>
    <path id="Path_671" data-name="Path 671" d="M407.418,119.66v-.109a.548.548,0,0,0-1.1,0v.109a.561.561,0,0,0,.548.548A.588.588,0,0,0,407.418,119.66Z" transform="translate(-12.677 161.297)" fill="#161616"/>
    <path id="Path_672" data-name="Path 672" d="M412.945,113.314a.548.548,0,0,0,0-1.1A.548.548,0,0,0,412.945,113.314Z" transform="translate(-15.758 164.723)" fill="#161616"/>
    <path id="Path_673" data-name="Path 673" d="M412.281,118.918a.548.548,0,0,0,0-1.1A.548.548,0,0,0,412.281,118.918Z" transform="translate(-15.423 161.894)" fill="#161616"/>
    <path id="Path_674" data-name="Path 674" d="M378.519,68.281a9.356,9.356,0,0,1-5.074-3.906c-2.227-3.8-.438-8.214.438-10.295.474-1.168,1.789-4.052,8.14-9.711,4.819-4.344,7.265-6.5,10-7.7,9.783-4.271,19.786-.183,21.611.584,2.7,1.1,4.673,2.446,8.579,5.074,4.746,3.212,7.776,5.293,10.149,8.871,2.884,4.381,1.132,5.512,3.614,10.441,3.943,7.812,9.382,6.863,12.631,14.091.548,1.2,3.578,7.922.146,13.8-2.811,4.782-8.542,6.5-12.777,6.389-7.7-.219-12.594-6.717-15.369-10.441a33.757,33.757,0,0,1-6.425-16.5c3.541-2.118,4.745-5.914,3.5-8.324-.912-1.716-3.432-3.432-5.658-2.482a4.545,4.545,0,0,0-2.482,4.344,16.294,16.294,0,0,1-16.683-7.557,26.848,26.848,0,0,0-8.579,5.366A26.064,26.064,0,0,0,378.519,68.281Z" transform="translate(4.468 203.842)" fill="#150aa6"/>
    <path id="Path_675" data-name="Path 675" d="M423.337,91.345a29.142,29.142,0,0,1,6.644,9.419,28.264,28.264,0,0,1,2.153,13.945c1.314-2.227,2.592-4.454,3.906-6.717,2.117,4.746,4.2,9.491,6.315,14.2a12.457,12.457,0,0,0,5.513-2.811,11.851,11.851,0,0,0,2.957-4.016,36.838,36.838,0,0,1-12.594-15.7,35.165,35.165,0,0,1-2.483-9.6c3.14-2.227,4.308-5.8,3.14-8.36-.73-1.643-2.847-3.724-5.22-3.066a4.547,4.547,0,0,0-3.067,5.111,17.4,17.4,0,0,1-16.61-7.52c.986,6.462,3.468,9.966,5.549,11.974,1.314,1.35,1.862,1.35,3.8,3.139Z" transform="translate(-16.549 182.895)" fill="#6d6c6c" opacity="0.24"/>
    <path id="Path_676" data-name="Path 676" d="M454.517,110.3c-.255,0-.109,3.76-.475,7.228a33.074,33.074,0,0,1-2.884,10.368,13.078,13.078,0,0,0,5.987-10.185,15.483,15.483,0,0,1-1.168-2.154c-1.278-2.92-1.241-5.257-1.46-5.257Z" transform="translate(-35.316 165.691)" fill="#6d6c6c" opacity="0.24"/>
    <path id="Path_677" data-name="Path 677" d="M460.524,131.489a42.241,42.241,0,0,0,3.724,3.432c4.855,4.052,9.564,6.352,9.71,6.1S469.5,138.9,466,133.862c-2.191-3.14-3.468-6.863-4.016-6.681-.255.073-.037.949-.475,2.373a8.527,8.527,0,0,1-.986,1.935Z" transform="translate(-40.046 157.171)" fill="#6d6c6c" opacity="0.19"/>
    <path id="Path_678" data-name="Path 678" d="M468.709,47.325a180.1,180.1,0,0,1,9.455,18.764c5.8,13.544,5.33,17.267,4.527,19.677-1.789,5.44-6.753,8.506-9.711,9.966a15.517,15.517,0,0,0,9.309,6.206c1.058.219,7.447,1.424,12.3-2.993a13.6,13.6,0,0,0,4.271-8.688c.657-6.133-2.993-10.732-4.162-12.047-1.533-1.716-1.862-1.314-5.439-4.782-2.775-2.665-4.125-4.016-4.929-5.695-1.278-2.738-.438-3.906-1.935-7.374a20.813,20.813,0,0,0-2.446-4.162c-3.541-4.782-8.652-7.593-11.244-8.871Z" transform="translate(-44.179 197.492)" fill="#150aa6" opacity="0.74"/>
    <path id="Path_679" data-name="Path 679" d="M400.405,109.742c.365.73.365,1.789-.11,2.008-.474.255-1.387-.438-1.715-1.241-.256-.694-.183-1.679.292-1.862C399.383,108.465,400.113,109.122,400.405,109.742Z" transform="translate(-8.693 166.542)" fill="#161616"/>
    <path id="Path_680" data-name="Path 680" d="M395.848,107.366a23.924,23.924,0,0,1,3.943-.986c.511-.073.292-.876-.219-.8a23.936,23.936,0,0,0-3.943.986A.416.416,0,0,0,395.848,107.366Z" transform="translate(-7.129 168.079)" fill="#161616"/>
  </g>
</svg>
