
const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
var _ = require('underscore');
var fs = require('fs');
var path = require('path');
const moment = require("moment");
const xlsxFile = require('read-excel-file/node');


exports.UploadStoryFile = async function (req, res) {
    try {
        console.log('storyy',req.body);
        var appDir = path.dirname(require.main.filename);
        var awsFilePath = '';
        var awsFileName = '';
        var pageId = '';
        if(req.body.pageId){
            pageId = req.body.pageId;
        }
        if(req.files){
        console.log('appdir',appDir,req.body,req.files);
        if (!req.files) {
            return res.status(500).send({ msg: "file is not found" })
        }
       
        // accessing the file
        const myFile = req.files.myFile;
        const today = moment(Date.now()).format('YYYY-MM-DD');
        var myFileName = myFile.name.split('.').join('-' + Date.now() + '.') ;//myFile.name;
        console.log("timestampfilename",myFileName);
        //let CopyFileTodayRes = CopyFileToday(myFile);
        //CopyFileTodayRes.then(function (resultedRes) {
            //console.log(resultedRes);
            //if (resultedRes) {           
                // Upload File to AWS
                var filePath = appDir+"\\public\\SampleExcelfiles\\"+today+"\\"+myFileName;
                console.log('filepath',filePath,"filename",myFileName);
                let timestamp =   Date.now();    
                let filename = "AgentStories/"+today+"/"+ myFileName;
                //let awsUploadResponse = uploadAWSfiles(filePath, filename, 'policystatic.policybazaar.com');
                let awsUploadResponse = Utility.uploadAWSfilesCommon(`${myFile.tempFilePath}`, filename, 'policystatic.policybazaar.com');

                console.log("awsresp",awsUploadResponse);
                awsUploadResponse.then(function (resultaws) {
                  console.log('resultedkey',resultaws.key);
                  var awsFileName = myFileName;
                  var awsFilePath = 'https://' + resultaws.Bucket + '/' + resultaws.Key ;
                  
                    let response = UploadStoryFileData(req.body, awsFilePath, pageId);
                    response.then(function (result) {
                        console.log("recordset",result.recordset) // "Some User token"
                        res.send({
                            status: 200,
                            data: result.recordset
                        });
                    })
                 

                })
            }else {
                let response = UploadStoryFileData(req.body, awsFilePath, pageId);
                response.then(function (result) {
                    console.log("recordset",result.recordset) // "Some User token"
                    res.send({
                        status: 200,
                        data: result.recordset
                    });
                })
            }

            //}
          //})

    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}

async function UploadStoryFileData(body, awsFilePath, pageId) {
    try {
        console.log(body.userId, awsFilePath, pageId);
        let sqlparam = [];

        sqlparam.push({ key: "ProductID", value: body.ProductId });
        sqlparam.push({ key: "RoleID", value: 13 });
        sqlparam.push({ key: "GroupID", value: body.groupId });
        
        sqlparam.push({ key: "TemplateType", value: body.ContentType });
        sqlparam.push({ key: "CreatedBy", value: body.userId });
        //sqlparam.push({ key: "IsRequired", value: body.IsRequired });
        sqlparam.push({ key: "IsActive", value: body.IsActive });
        sqlparam.push({ key: "Title", value: body.StoryName });
        sqlparam.push({ key: "StartDate", value: body.StartDate });
        sqlparam.push({ key: "EndDate", value: body.Endtime });       
        //sqlparam.push({ key: "ContentType", value: body.ContentType });    
        sqlparam.push({ key: "EnableReaction", value: body.EnableReaction });    
        sqlparam.push({ key: "Url", value: awsFilePath });    
        sqlparam.push({ key: "Story", value: body.Story });    
        sqlparam.push({ key: "StoryId", value: body.StoryId });
        sqlparam.push({ key: "Message", value: "", type: "out" });
        console.log('params',sqlparam);
        let result = await sqlHelper.sqlProcedure("L", "[MTX].[InsertUpdateAgentStory]", sqlparam);
        console.log(result);
        return result;

    }
    catch (e) {
        console.log(e);
        return e;

    }
    finally {

    }
}





