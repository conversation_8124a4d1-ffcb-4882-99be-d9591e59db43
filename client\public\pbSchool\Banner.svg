<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1366" height="182" viewBox="0 0 1366 182">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_28795" data-name="Rectangle 28795" width="1366" height="182" transform="translate(0 65)" fill="#253858"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <rect id="Rectangle_28788" data-name="Rectangle 28788" width="405.935" height="251.621" fill="none"/>
    </clipPath>
  </defs>
  <g id="Mask_Group_20" data-name="Mask Group 20" transform="translate(0 -65)" opacity="0.3" clip-path="url(#clip-path)">
    <g id="Group_227785" data-name="Group 227785" transform="translate(888 56)">
      <rect id="Rectangle_28768" data-name="Rectangle 28768" width="27.743" height="10.693" transform="translate(80.19 211.436)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
      <g id="Group_227784" data-name="Group 227784">
        <g id="Group_227783" data-name="Group 227783" clip-path="url(#clip-path-2)">
          <path id="Path_220715" data-name="Path 220715" d="M163.174,375.321H135.716a1.278,1.278,0,0,1,0-2.555h27.457a.774.774,0,0,0,.773-.773v-7.139a.774.774,0,0,0-.773-.773H135.716a1.278,1.278,0,0,1,0-2.555h27.457a3.332,3.332,0,0,1,3.328,3.328v7.139a3.332,3.332,0,0,1-3.328,3.328" transform="translate(-56.415 -151.706)" fill="#1f1f1f"/>
          <path id="Path_220716" data-name="Path 220716" d="M163.174,375.321H135.716a1.278,1.278,0,0,1,0-2.555h27.457a.774.774,0,0,0,.773-.773v-7.139a.774.774,0,0,0-.773-.773H135.716a1.278,1.278,0,0,1,0-2.555h27.457a3.332,3.332,0,0,1,3.328,3.328v7.139A3.332,3.332,0,0,1,163.174,375.321Z" transform="translate(-56.415 -151.706)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <line id="Line_73" data-name="Line 73" x1="15.568" transform="translate(92.951 214.705)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <line id="Line_74" data-name="Line 74" x1="17.534" transform="translate(90.985 217.96)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <rect id="Rectangle_28769" data-name="Rectangle 28769" width="58.497" height="19.247" transform="translate(50.022 226.335)" fill="#fff"/>
          <rect id="Rectangle_28770" data-name="Rectangle 28770" width="58.497" height="19.247" transform="translate(50.022 226.335)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <line id="Line_75" data-name="Line 75" x2="25.38" transform="translate(50.022 232.929)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <line id="Line_76" data-name="Line 76" x2="32.457" transform="translate(50.022 238.999)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220717" data-name="Path 220717" d="M145.146,409.453H87.238a4.343,4.343,0,0,1-4.338-4.339V390.3a4.343,4.343,0,0,1,4.338-4.338h57.908a1.909,1.909,0,1,1,0,3.818H87.238a.522.522,0,0,0-.521.521v14.813a.521.521,0,0,0,.521.521h57.908a1.909,1.909,0,0,1,0,3.818" transform="translate(-34.787 -161.961)" fill="#0065ff"/>
          <path id="Path_220718" data-name="Path 220718" d="M145.146,409.453H87.238a4.343,4.343,0,0,1-4.338-4.339V390.3a4.343,4.343,0,0,1,4.338-4.338h57.908a1.909,1.909,0,1,1,0,3.818H87.238a.522.522,0,0,0-.521.521v14.813a.521.521,0,0,0,.521.521h57.908a1.909,1.909,0,0,1,0,3.818Z" transform="translate(-34.787 -161.961)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220719" data-name="Path 220719" d="M374.21,427.1H65.542" transform="translate(-27.503 -179.225)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220720" data-name="Path 220720" d="M208.392,314.293v2.127a4.405,4.405,0,0,1-1.044,2.85h-23.13v-4.977Z" transform="translate(-77.303 -131.886)" fill="#fff"/>
          <path id="Path_220721" data-name="Path 220721" d="M208.392,314.293v2.127a4.405,4.405,0,0,1-1.044,2.85h-23.13v-4.977Z" transform="translate(-77.303 -131.886)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220722" data-name="Path 220722" d="M348.354,372.841l-61.379-1.624L225.6,372.841c-9.765-16.59-1.486-39.879,16.983-58.772,9.368-9.583,21.084-13.6,30.355-15.224a68.023,68.023,0,0,1,14.041-1.01,67.344,67.344,0,0,1,12.58.77c9.478,1.457,21.95,5.37,31.816,15.464,18.469,18.893,26.748,42.182,16.983,58.772" transform="translate(-92.932 -124.963)" fill="#0065ff"/>
          <path id="Path_220723" data-name="Path 220723" d="M348.354,372.841l-61.379-1.624L225.6,372.841c-9.765-16.59-1.486-39.879,16.983-58.772,9.368-9.583,21.084-13.6,30.355-15.224a68.023,68.023,0,0,1,14.041-1.01,67.344,67.344,0,0,1,12.58.77c9.478,1.457,21.95,5.37,31.816,15.464C349.84,332.962,358.119,356.251,348.354,372.841Z" transform="translate(-92.932 -124.963)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220724" data-name="Path 220724" d="M225.48,373.044s6.076-6.275,13.784-3.95c7.478,2.255,12.408,11.715,11.647,16.955-.4,2.731-4.4,4.223-8.644.963s-16.787-13.968-16.787-13.968" transform="translate(-94.618 -154.666)" fill="#1f1f1f"/>
          <path id="Path_220725" data-name="Path 220725" d="M225.48,373.044s6.076-6.275,13.784-3.95c7.478,2.255,12.408,11.715,11.647,16.955-.4,2.731-4.4,4.223-8.644.963S225.48,373.044,225.48,373.044Z" transform="translate(-94.618 -154.666)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220726" data-name="Path 220726" d="M424.734,373.044s-6.077-6.275-13.784-3.95c-7.478,2.255-12.408,11.715-11.647,16.955.4,2.731,4.4,4.223,8.645.963s16.787-13.968,16.787-13.968" transform="translate(-167.527 -154.666)" fill="#1f1f1f"/>
          <path id="Path_220727" data-name="Path 220727" d="M424.734,373.044s-6.077-6.275-13.784-3.95c-7.478,2.255-12.408,11.715-11.647,16.955.4,2.731,4.4,4.223,8.645.963S424.734,373.044,424.734,373.044Z" transform="translate(-167.527 -154.666)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220728" data-name="Path 220728" d="M325.86,308.606c-.588.041-1.2.061-1.855.061q-.773,0-1.486-.037c-12.953-.649-12.369-9.784-12.369-9.784v0a68.184,68.184,0,0,1,14.04-1.01,67.414,67.414,0,0,1,12.58.771s1.566,9.178-10.909,10" transform="translate(-130.146 -124.963)" fill="#1f1f1f"/>
          <path id="Path_220729" data-name="Path 220729" d="M335.353,288.079V297.7s-2.265,3.114-8.016,3.114-8.024-3.114-8.024-3.114v-9.624Z" transform="translate(-133.993 -120.886)" fill="#fff"/>
          <path id="Path_220730" data-name="Path 220730" d="M335.353,288.079V297.7s-2.265,3.114-8.016,3.114-8.024-3.114-8.024-3.114v-9.624Z" transform="translate(-133.993 -120.886)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220731" data-name="Path 220731" d="M336.95,245.971c0,11.412-7.223,20.073-16.134,20.073s-16.134-8.661-16.134-20.073,7.223-22.432,16.134-22.432,16.134,11.02,16.134,22.432" transform="translate(-127.853 -93.803)" fill="#fff"/>
          <path id="Path_220732" data-name="Path 220732" d="M336.95,245.971c0,11.412-7.223,20.073-16.134,20.073s-16.134-8.661-16.134-20.073,7.223-22.432,16.134-22.432S336.95,234.559,336.95,245.971Z" transform="translate(-127.853 -93.803)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220733" data-name="Path 220733" d="M336.237,234.889q-.159.442-.319.814a10.641,10.641,0,0,1-.9,1.732,4.332,4.332,0,0,1-.532.694c-.128.063-.254.124-.381.18q-.3.145-.608.271c-.146.063-.289.122-.432.177s-.259.1-.389.151c-15.1,5.612-25.834-12.5-25.834-12.5a20.834,20.834,0,0,1-5.484,14.477c-.22.226-.387.379-.478.458-.049.045-.077.067-.077.067-.289-.344-.564-.69-.828-1.034-.057-.077-.114-.151-.171-.226q-.475-.64-.894-1.276a23.637,23.637,0,0,1-1.223-2.074q-.483-.926-.855-1.848a18.68,18.68,0,0,1-1.358-5.751c-.782-11.161,9.165-20.474,18.443-21.38,12.549-1.226,15,5.1,15,5.1l.026-.008c1.649-.446,8.474,1.3,8.663,11.629a29.239,29.239,0,0,1-1.366,10.355" transform="translate(-123.97 -87.14)" fill="#0065ff"/>
          <path id="Path_220734" data-name="Path 220734" d="M300.717,246.358l-2.1-.515.646-2.636c-.247-1.837-1.584-13.955,4.8-21.4,3.227-3.758,7.866-5.663,13.79-5.663,6.673,0,11.745,2.1,15.075,6.255,6.57,8.19,3.893,21.328,3.566,22.8l-2.11-.47c.3-1.367,2.8-13.573-3.142-20.977-2.9-3.614-7.4-5.446-13.388-5.446-5.261,0-9.347,1.65-12.144,4.9-6.134,7.134-4.3,19.758-4.282,19.885l.032.212Z" transform="translate(-125.308 -90.702)" fill="#fff"/>
          <path id="Path_220735" data-name="Path 220735" d="M300.717,246.358l-2.1-.515.646-2.636c-.247-1.837-1.584-13.955,4.8-21.4,3.227-3.758,7.866-5.663,13.79-5.663,6.673,0,11.745,2.1,15.075,6.255,6.57,8.19,3.893,21.328,3.566,22.8l-2.11-.47c.3-1.367,2.8-13.573-3.142-20.977-2.9-3.614-7.4-5.446-13.388-5.446-5.261,0-9.347,1.65-12.144,4.9-6.134,7.134-4.3,19.758-4.282,19.885l.032.212Z" transform="translate(-125.308 -90.702)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220736" data-name="Path 220736" d="M339.9,257.094c-.277.478,3.185,1.911,3.68,1.416s-3.29-2.088-3.68-1.416" transform="translate(-142.625 -107.816)"/>
          <path id="Path_220737" data-name="Path 220737" d="M341.133,266.727h0a.637.637,0,0,1-.637-.637v-1.557a.637.637,0,0,1,1.273,0v1.557a.637.637,0,0,1-.637.637" transform="translate(-142.882 -110.739)"/>
          <path id="Path_220738" data-name="Path 220738" d="M324.341,266.727h0a.637.637,0,0,1-.637-.637v-1.557a.637.637,0,1,1,1.273,0v1.557a.637.637,0,0,1-.637.637" transform="translate(-135.836 -110.739)"/>
          <path id="Path_220739" data-name="Path 220739" d="M340.082,268.584a1.576,1.576,0,0,1,1.754,0" transform="translate(-142.708 -112.596)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220740" data-name="Path 220740" d="M330.287,281.15s4.765-.425,7.218-3.963" transform="translate(-138.598 -116.316)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220741" data-name="Path 220741" d="M323.791,257.094c.277.478-3.184,1.911-3.68,1.416s3.29-2.088,3.68-1.416" transform="translate(-134.309 -107.816)"/>
          <path id="Path_220742" data-name="Path 220742" d="M325.044,268.584a1.575,1.575,0,0,0-1.755,0" transform="translate(-135.662 -112.596)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220743" data-name="Path 220743" d="M330.59,269.659s-.371,1.887,1.465,1.7,1.884-2.17.563-2.217" transform="translate(-138.712 -112.939)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220744" data-name="Path 220744" d="M231.8,369.227c11.334,3.65,10.1,28.243,3.491,33.416H210.948s-1.05-14.466,3.29-22.77,12-12.439,17.561-10.646" transform="translate(-88.473 -154.765)" fill="#fff"/>
          <path id="Path_220745" data-name="Path 220745" d="M231.8,369.227c11.334,3.65,10.1,28.243,3.491,33.416H210.948s-1.05-14.466,3.29-22.77S226.233,367.435,231.8,369.227Z" transform="translate(-88.473 -154.765)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220746" data-name="Path 220746" d="M224.6,415.873h13.659c1.056,2.7,6.213,2.866,11.318,0l.085-.048a4.988,4.988,0,0,0,2.514-4.345v-.553a4.99,4.99,0,0,0-2.513-4.344l-.034-.019c-1.132-.637-4.67.212-6.581-.283a18.424,18.424,0,0,1-5.975-3.709l-.039-.041a6.418,6.418,0,0,0-8.124-1.26c-2.811,1.531-3.9,9.415-4.309,14.6" transform="translate(-94.249 -167.995)" fill="#fff"/>
          <path id="Path_220747" data-name="Path 220747" d="M224.6,415.873h13.659c1.056,2.7,6.213,2.866,11.318,0l.085-.048a4.988,4.988,0,0,0,2.514-4.345v-.553a4.99,4.99,0,0,0-2.513-4.344l-.034-.019c-1.132-.637-4.67.212-6.581-.283a18.424,18.424,0,0,1-5.975-3.709l-.039-.041a6.418,6.418,0,0,0-8.124-1.26C226.1,402.8,225.013,410.686,224.6,415.873Z" transform="translate(-94.249 -167.995)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220748" data-name="Path 220748" d="M259.548,421.411c-.078.047-.159.091-.238.136-5.1,2.866-10.263,2.7-11.318,0a3.6,3.6,0,0,1,0-2.375c1.657-5.59,7.4-5.307,7.4-5.307" transform="translate(-103.981 -173.669)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220749" data-name="Path 220749" d="M415.587,369.227c-11.333,3.65-10.1,28.243-3.491,33.416h24.343s1.05-14.466-3.29-22.77-11.995-12.439-17.561-10.646" transform="translate(-170.826 -154.765)" fill="#fff"/>
          <path id="Path_220750" data-name="Path 220750" d="M415.587,369.227c-11.333,3.65-10.1,28.243-3.491,33.416h24.343s1.05-14.466-3.29-22.77S421.154,367.435,415.587,369.227Z" transform="translate(-170.826 -154.765)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220751" data-name="Path 220751" d="M424.151,415.873H410.492c-1.056,2.7-6.213,2.866-11.318,0l-.085-.048a4.988,4.988,0,0,1-2.514-4.345v-.553a4.99,4.99,0,0,1,2.513-4.344l.034-.019c1.132-.637,4.67.212,6.581-.283a18.424,18.424,0,0,0,5.976-3.709l.039-.041a6.418,6.418,0,0,1,8.124-1.26c2.811,1.531,3.9,9.415,4.309,14.6" transform="translate(-166.414 -167.995)" fill="#fff"/>
          <path id="Path_220752" data-name="Path 220752" d="M424.151,415.873H410.492c-1.056,2.7-6.213,2.866-11.318,0l-.085-.048a4.988,4.988,0,0,1-2.514-4.345v-.553a4.99,4.99,0,0,1,2.513-4.344l.034-.019c1.132-.637,4.67.212,6.581-.283a18.424,18.424,0,0,0,5.976-3.709l.039-.041a6.418,6.418,0,0,1,8.124-1.26C422.653,402.8,423.738,410.686,424.151,415.873Z" transform="translate(-166.414 -167.995)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220753" data-name="Path 220753" d="M404.792,413.865s5.746-.283,7.4,5.307a3.606,3.606,0,0,1,0,2.374c-1.056,2.7-6.213,2.866-11.318,0-.078-.045-.159-.089-.238-.136" transform="translate(-168.121 -173.669)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220754" data-name="Path 220754" d="M450.8,416.626s2.879,1.6,4.028-2.217c1.325-4.406-.883-13.068-3.242-13.539s-3.68,2.925-3.68,2.925Z" transform="translate(-187.954 -168.198)" fill="#fff"/>
          <path id="Path_220755" data-name="Path 220755" d="M450.8,416.626s2.879,1.6,4.028-2.217c1.325-4.406-.883-13.068-3.242-13.539s-3.68,2.925-3.68,2.925Z" transform="translate(-187.954 -168.198)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220756" data-name="Path 220756" d="M434.982,402.611a5.857,5.857,0,0,1,5.732-2.9c4.175.283,5.779,15.088,5.233,16.928s-6.365,3.027-7.143-.44-3.821-13.586-3.821-13.586" transform="translate(-182.531 -167.723)" fill="#fff"/>
          <path id="Path_220757" data-name="Path 220757" d="M434.982,402.611a5.857,5.857,0,0,1,5.732-2.9c4.175.283,5.779,15.088,5.233,16.928s-6.365,3.027-7.143-.44S434.982,402.611,434.982,402.611Z" transform="translate(-182.531 -167.723)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220758" data-name="Path 220758" d="M434.286,415.873a3.939,3.939,0,0,1-.364,1.409c-1.494,2.821-7.615,2.744-9.028-1.409-.022-.069-.044-.139-.065-.21a112.6,112.6,0,0,1-2.231-13.091l.039-.041a6.417,6.417,0,0,1,8.123-1.26c1.406,1.663,3.947,10.514,3.525,14.6" transform="translate(-177.335 -167.995)" fill="#fff"/>
          <path id="Path_220759" data-name="Path 220759" d="M422.6,402.572l.039-.041a6.418,6.418,0,0,1,8.124-1.26c1.405,1.663,3.947,10.514,3.524,14.6a3.94,3.94,0,0,1-.363,1.409c-1.494,2.821-7.615,2.744-9.028-1.409l-.065-.21a76.14,76.14,0,0,1-1.635-8.684" transform="translate(-177.335 -167.995)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220760" data-name="Path 220760" d="M212.6,416.626s-2.879,1.6-4.028-2.217c-1.325-4.406.883-13.068,3.242-13.539s3.68,2.925,3.68,2.925Z" transform="translate(-87.354 -168.198)" fill="#fff"/>
          <path id="Path_220761" data-name="Path 220761" d="M212.6,416.626s-2.879,1.6-4.028-2.217c-1.325-4.406.883-13.068,3.242-13.539s3.68,2.925,3.68,2.925Z" transform="translate(-87.354 -168.198)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220762" data-name="Path 220762" d="M225.7,402.611a5.857,5.857,0,0,0-5.732-2.9c-4.175.283-5.779,15.088-5.233,16.928s6.365,3.027,7.143-.44,3.821-13.586,3.821-13.586" transform="translate(-90.068 -167.723)" fill="#fff"/>
          <path id="Path_220763" data-name="Path 220763" d="M225.7,402.611a5.857,5.857,0,0,0-5.732-2.9c-4.175.283-5.779,15.088-5.233,16.928s6.365,3.027,7.143-.44S225.7,402.611,225.7,402.611Z" transform="translate(-90.068 -167.723)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220764" data-name="Path 220764" d="M225.917,415.873a3.939,3.939,0,0,0,.364,1.409c1.494,2.821,7.614,2.744,9.028-1.409.021-.069.043-.139.064-.21a112.593,112.593,0,0,0,2.231-13.091l-.039-.041a6.418,6.418,0,0,0-8.124-1.26c-1.406,1.663-3.947,10.514-3.525,14.6" transform="translate(-94.782 -167.995)" fill="#fff"/>
          <path id="Path_220765" data-name="Path 220765" d="M237.6,402.572l-.039-.041a6.418,6.418,0,0,0-8.124-1.26c-1.405,1.663-3.947,10.514-3.524,14.6a3.94,3.94,0,0,0,.363,1.409c1.494,2.821,7.615,2.744,9.028-1.409l.065-.21a71.274,71.274,0,0,0,1.54-8.038" transform="translate(-94.782 -167.995)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220766" data-name="Path 220766" d="M328.915,212.913c-8.491-6.6-29.066-6.51-33.445,16.285-.782-11.161,9.165-20.474,18.443-21.38,12.549-1.226,15,5.1,15,5.1" transform="translate(-123.97 -87.14)" fill="#fff"/>
          <path id="Path_220767" data-name="Path 220767" d="M336.237,234.889q-.159.442-.319.814a10.641,10.641,0,0,1-.9,1.732,4.332,4.332,0,0,1-.532.694c-.128.063-.254.124-.381.18q-.3.145-.608.271c-.146.063-.289.122-.432.177s-.259.1-.389.151c-15.1,5.612-25.834-12.5-25.834-12.5a20.834,20.834,0,0,1-5.484,14.477c-.22.226-.387.379-.478.458-.049.045-.077.067-.077.067-.289-.344-.564-.69-.828-1.034-.057-.077-.114-.151-.171-.226q-.475-.64-.894-1.276a23.637,23.637,0,0,1-1.223-2.074q-.483-.926-.855-1.848a18.68,18.68,0,0,1-1.358-5.751c-.782-11.161,9.165-20.474,18.443-21.38,12.549-1.226,15,5.1,15,5.1l.026-.008c1.649-.446,8.474,1.3,8.663,11.629A29.239,29.239,0,0,1,336.237,234.889Z" transform="translate(-123.97 -87.14)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220768" data-name="Path 220768" d="M361.729,266.987,360.7,266.9,361.8,254.3l3.242.286A2.594,2.594,0,0,1,367.4,257.4l-.459,5.216a4.812,4.812,0,0,1-5.215,4.371" transform="translate(-151.358 -106.713)" fill="#fff"/>
          <path id="Path_220769" data-name="Path 220769" d="M361.021,266.987l-.832-.073a1.294,1.294,0,0,1-1.18-1.3l.028-4.382A13,13,0,0,1,361.1,254.3l1.034.091a66.272,66.272,0,0,0-1.108,12.594" transform="translate(-150.651 -106.713)"/>
          <path id="Path_220770" data-name="Path 220770" d="M298.17,266.98l1.034-.093L298.062,254.3l-3.242.294a2.6,2.6,0,0,0-2.35,2.818l.473,5.215a4.813,4.813,0,0,0,5.226,4.358" transform="translate(-122.725 -106.71)" fill="#fff"/>
          <path id="Path_220771" data-name="Path 220771" d="M361.729,266.987,360.7,266.9,361.8,254.3l3.242.286A2.594,2.594,0,0,1,367.4,257.4l-.459,5.216A4.812,4.812,0,0,1,361.729,266.987Z" transform="translate(-151.358 -106.713)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220772" data-name="Path 220772" d="M298.17,266.98l1.034-.093L298.062,254.3l-3.242.294a2.6,2.6,0,0,0-2.35,2.818l.473,5.215A4.813,4.813,0,0,0,298.17,266.98Z" transform="translate(-122.725 -106.71)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220773" data-name="Path 220773" d="M301.473,266.98l.832-.076a1.292,1.292,0,0,0,1.176-1.3l-.039-4.382a13,13,0,0,0-2.077-6.927l-1.034.094a66.256,66.256,0,0,1,1.141,12.591" transform="translate(-126.028 -106.71)"/>
          <path id="Path_220774" data-name="Path 220774" d="M361.021,266.987l-.832-.073a1.294,1.294,0,0,1-1.18-1.3l.028-4.382A13,13,0,0,1,361.1,254.3l1.034.091A66.272,66.272,0,0,0,361.021,266.987Z" transform="translate(-150.651 -106.713)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220775" data-name="Path 220775" d="M301.473,266.98l.832-.076a1.292,1.292,0,0,0,1.176-1.3l-.039-4.382a13,13,0,0,0-2.077-6.927l-1.034.094A66.256,66.256,0,0,1,301.473,266.98Z" transform="translate(-126.028 -106.71)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220776" data-name="Path 220776" d="M569.136,83.445V218.461a5.975,5.975,0,0,1-5.976,5.976H432.91a5.975,5.975,0,0,1-5.975-5.976V83.445a5.975,5.975,0,0,1,5.975-5.975H563.161a5.975,5.975,0,0,1,5.976,5.975" transform="translate(-179.154 -32.509)" fill="#fff"/>
          <path id="Path_220777" data-name="Path 220777" d="M475.625,267.812H432.91a5.976,5.976,0,0,1-5.976-5.976v-81" transform="translate(-179.154 -75.884)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220778" data-name="Path 220778" d="M426.934,95.436V83.445a5.976,5.976,0,0,1,5.976-5.975H563.16a5.976,5.976,0,0,1,5.976,5.975V207.383" transform="translate(-179.154 -32.509)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220779" data-name="Path 220779" d="M569.136,83.445v5.913h-142.2V83.445a5.975,5.975,0,0,1,5.975-5.975H563.161a5.975,5.975,0,0,1,5.976,5.975" transform="translate(-179.154 -32.509)" fill="#0065ff"/>
          <path id="Path_220780" data-name="Path 220780" d="M569.136,83.445v5.913h-142.2V83.445a5.975,5.975,0,0,1,5.975-5.975H563.161A5.975,5.975,0,0,1,569.136,83.445Z" transform="translate(-179.154 -32.509)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220781" data-name="Path 220781" d="M638.194,86.057a2.07,2.07,0,1,1-2.069-2.07,2.07,2.07,0,0,1,2.069,2.07" transform="translate(-266.068 -35.243)" fill="#fff"/>
          <path id="Path_220782" data-name="Path 220782" d="M660.507,86.057a2.07,2.07,0,1,1-2.07-2.07,2.07,2.07,0,0,1,2.07,2.07" transform="translate(-275.431 -35.243)"/>
          <path id="Path_220783" data-name="Path 220783" d="M615.882,86.057a2.07,2.07,0,1,1-2.07-2.07,2.07,2.07,0,0,1,2.07,2.07" transform="translate(-256.705 -35.243)" fill="#fff"/>
          <path id="Path_220784" data-name="Path 220784" d="M339.552,389.763H270.2l-5.623-42.238a1.82,1.82,0,0,1,1.8-2.06h76.985a1.82,1.82,0,0,1,1.8,2.06Z" transform="translate(-111.019 -144.968)" fill="#1f1f1f"/>
          <path id="Path_220785" data-name="Path 220785" d="M339.552,389.763H270.2l-5.623-42.238a1.82,1.82,0,0,1,1.8-2.06h76.985a1.82,1.82,0,0,1,1.8,2.06Z" transform="translate(-111.019 -144.968)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <rect id="Rectangle_28771" data-name="Rectangle 28771" width="69.347" height="4.497" transform="translate(159.185 243.381)" fill="#fff"/>
          <rect id="Rectangle_28772" data-name="Rectangle 28772" width="69.347" height="4.497" transform="translate(159.185 243.381)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220786" data-name="Path 220786" d="M333.543,379.087a7.5,7.5,0,1,1-7.5-7.5,7.5,7.5,0,0,1,7.5,7.5" transform="translate(-133.669 -155.928)" fill="#fff"/>
          <path id="Path_220787" data-name="Path 220787" d="M440.007,115.877H381.2a4.746,4.746,0,0,0-4.747,4.747v27.232A4.746,4.746,0,0,0,381.2,152.6h52.683l7.041,9.828a4.707,4.707,0,0,0,3.827,1.966V120.624a4.746,4.746,0,0,0-4.747-4.747M394.176,136a2.07,2.07,0,1,1,2.07-2.07,2.069,2.069,0,0,1-2.07,2.07m15.337,0a2.07,2.07,0,1,1,2.07-2.07,2.068,2.068,0,0,1-2.07,2.07m15.338,0a2.07,2.07,0,1,1,2.07-2.07,2.068,2.068,0,0,1-2.07,2.07" transform="translate(-157.972 -48.625)" fill="#0065ff"/>
          <path id="Path_220788" data-name="Path 220788" d="M433.234,108.426h-58.8a4.746,4.746,0,0,0-4.747,4.747v27.232a4.746,4.746,0,0,0,4.747,4.747h52.683l7.041,9.828a4.707,4.707,0,0,0,3.827,1.966V113.173A4.746,4.746,0,0,0,433.234,108.426ZM387.4,128.546a2.07,2.07,0,1,1,2.07-2.07A2.069,2.069,0,0,1,387.4,128.546Zm15.337,0a2.07,2.07,0,1,1,2.07-2.07A2.068,2.068,0,0,1,402.741,128.546Zm15.338,0a2.07,2.07,0,1,1,2.07-2.07A2.068,2.068,0,0,1,418.079,128.546Z" transform="translate(-155.13 -45.499)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220789" data-name="Path 220789" d="M634.448,128.921a2.07,2.07,0,1,1-2.07-2.07,2.07,2.07,0,0,1,2.07,2.07" transform="translate(-264.496 -53.23)" fill="#0065ff"/>
          <path id="Path_220790" data-name="Path 220790" d="M660.876,128.921a2.07,2.07,0,1,1-2.07-2.07,2.07,2.07,0,0,1,2.07,2.07" transform="translate(-275.586 -53.23)" fill="#0065ff"/>
          <path id="Path_220791" data-name="Path 220791" d="M687.3,128.921a2.07,2.07,0,1,1-2.069-2.07,2.07,2.07,0,0,1,2.069,2.07" transform="translate(-286.676 -53.23)" fill="#0065ff"/>
          <path id="Path_220792" data-name="Path 220792" d="M634.448,152.616a2.07,2.07,0,1,1-2.07-2.07,2.07,2.07,0,0,1,2.07,2.07" transform="translate(-264.496 -63.173)" fill="#0065ff"/>
          <path id="Path_220793" data-name="Path 220793" d="M660.876,152.616a2.07,2.07,0,1,1-2.07-2.07,2.07,2.07,0,0,1,2.07,2.07" transform="translate(-275.586 -63.173)" fill="#0065ff"/>
          <path id="Path_220794" data-name="Path 220794" d="M687.3,152.616a2.07,2.07,0,1,1-2.069-2.07,2.07,2.07,0,0,1,2.069,2.07" transform="translate(-286.676 -63.173)" fill="#0065ff"/>
          <path id="Path_220795" data-name="Path 220795" d="M634.448,176.31a2.07,2.07,0,1,1-2.07-2.07,2.07,2.07,0,0,1,2.07,2.07" transform="translate(-264.496 -73.116)" fill="#0065ff"/>
          <path id="Path_220796" data-name="Path 220796" d="M660.876,176.31a2.07,2.07,0,1,1-2.07-2.07,2.07,2.07,0,0,1,2.07,2.07" transform="translate(-275.586 -73.116)" fill="#0065ff"/>
          <path id="Path_220797" data-name="Path 220797" d="M687.3,176.31a2.07,2.07,0,1,1-2.069-2.07,2.07,2.07,0,0,1,2.069,2.07" transform="translate(-286.676 -73.116)" fill="#0065ff"/>
          <path id="Path_220798" data-name="Path 220798" d="M368.308,46.03S347.061,18.008,282.2,20.98s-86.774,34.657-49.9,41.99c36.513,7.261,59.356-10.147,26.44-17.365S184.212,56.22,167.229,84.4" transform="translate(-70.174 -8.711)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.693" stroke-dasharray="13.547 13.547"/>
          <path id="Path_220799" data-name="Path 220799" d="M296.523,145.506H263.69a2.357,2.357,0,0,0-2.359,2.359v17.171a2.358,2.358,0,0,0,2.359,2.359h25.192l6.581,7.638a.6.6,0,0,0,1.061-.4V167.4a2.358,2.358,0,0,0,2.359-2.359V147.865a2.358,2.358,0,0,0-2.359-2.359m-16.417,16.983c-3.208,0-8.576-9.417-5.378-11.416,3.019-1.887,5.378,1.7,5.378,1.7s2.359-3.585,5.378-1.7c3.2,2-2.17,11.416-5.378,11.416" transform="translate(-109.662 -61.059)" fill="#0065ff"/>
          <path id="Path_220800" data-name="Path 220800" d="M291.1,149.57H258.271a2.357,2.357,0,0,0-2.359,2.359V169.1a2.358,2.358,0,0,0,2.359,2.359h25.192l6.581,7.638a.6.6,0,0,0,1.061-.4v-7.243a2.358,2.358,0,0,0,2.359-2.359V151.929A2.358,2.358,0,0,0,291.1,149.57Zm-16.417,16.983c-3.208,0-8.576-9.418-5.378-11.416,3.019-1.887,5.378,1.7,5.378,1.7s2.359-3.585,5.378-1.7C283.264,157.135,277.9,166.553,274.688,166.553Z" transform="translate(-107.388 -62.764)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220801" data-name="Path 220801" d="M530.945,11.622V13l8.385,7.213a1.328,1.328,0,0,0,2.194-1.006V4.04a1.328,1.328,0,0,0-2.194-1.007l-8.385,7.213Z" transform="translate(-222.8 -1.137)" fill="#0065ff"/>
          <rect id="Rectangle_28773" data-name="Rectangle 28773" width="3.75" height="5.157" transform="translate(306.27 7.907)"/>
          <path id="Path_220802" data-name="Path 220802" d="M530.268,9.59v1.376l8.385,7.213a1.328,1.328,0,0,0,2.194-1.006V2.008A1.328,1.328,0,0,0,538.653,1l-8.385,7.213Z" transform="translate(-222.516 -0.284)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <rect id="Rectangle_28774" data-name="Rectangle 28774" width="3.75" height="5.157" transform="translate(305.876 6.728)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220803" data-name="Path 220803" d="M511.787,20H499.451a3.723,3.723,0,0,1-3.723-3.723V9.779a3.723,3.723,0,0,1,3.723-3.723h12.336a3.723,3.723,0,0,1,3.723,3.723v6.494A3.723,3.723,0,0,1,511.787,20" transform="translate(-208.022 -2.541)" fill="#0065ff"/>
          <path id="Path_220804" data-name="Path 220804" d="M513.485,16.2a2.579,2.579,0,1,1-2.579-2.579,2.578,2.578,0,0,1,2.579,2.579" transform="translate(-213.309 -5.717)" fill="#fff"/>
          <rect id="Rectangle_28775" data-name="Rectangle 28775" width="19.783" height="13.941" rx="6.415" transform="translate(287.313 2.336)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <circle id="Ellipse_60" data-name="Ellipse 60" cx="2.579" cy="2.579" r="2.579" transform="translate(294.626 6.727)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220805" data-name="Path 220805" d="M149.854,148.864v96.283a4.421,4.421,0,0,1-4.417,4.418h-96.3a4.419,4.419,0,0,1-4.419-4.418V148.864a4.419,4.419,0,0,1,4.419-4.419h96.3A4.419,4.419,0,0,1,149.854,148.864Z" transform="translate(-18.766 -60.613)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220806" data-name="Path 220806" d="M149.855,148.865v8.106H44.721v-8.106a4.417,4.417,0,0,1,4.418-4.418h96.3a4.418,4.418,0,0,1,4.418,4.418" transform="translate(-18.766 -60.614)" fill="#1f1f1f"/>
          <path id="Path_220807" data-name="Path 220807" d="M172.721,154.089a2.07,2.07,0,1,1-2.07-2.07,2.07,2.07,0,0,1,2.07,2.07" transform="translate(-70.742 -63.792)" fill="#fff"/>
          <path id="Path_220808" data-name="Path 220808" d="M195.034,154.089a2.07,2.07,0,1,1-2.07-2.07,2.07,2.07,0,0,1,2.07,2.07" transform="translate(-80.105 -63.792)" fill="#fff"/>
          <path id="Path_220809" data-name="Path 220809" d="M217.346,154.089a2.07,2.07,0,1,1-2.07-2.07,2.07,2.07,0,0,1,2.07,2.07" transform="translate(-89.467 -63.792)" fill="#fff"/>
          <path id="Path_220810" data-name="Path 220810" d="M217.346,154.089a2.07,2.07,0,1,1-2.07-2.07,2.07,2.07,0,0,1,2.07,2.07" transform="translate(-89.467 -63.792)" fill="#0065ff"/>
          <path id="Path_220811" data-name="Path 220811" d="M149.855,148.865v8.106H44.721v-8.106a4.417,4.417,0,0,1,4.418-4.418h96.3A4.418,4.418,0,0,1,149.855,148.865Z" transform="translate(-18.766 -60.614)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220812" data-name="Path 220812" d="M129.965,80.963a8.74,8.74,0,0,1-.951.529h-9.962v-4.73h2.341l-5.691-6.5-5.692,6.5h2.343v4.73H101.746a11.463,11.463,0,0,1,17.044-15.332,4.772,4.772,0,0,1,3.229-1.218,4.321,4.321,0,0,1,4.533,4.077v.028a6.252,6.252,0,0,1,6.274,2.575,5.415,5.415,0,0,1,.737,1.785,7.419,7.419,0,0,1-3.6,7.558" transform="translate(-41.488 -26.198)" fill="#0065ff"/>
          <path id="Path_220813" data-name="Path 220813" d="M133.352,83.333a8.744,8.744,0,0,1-.951.529h-9.962V79.132h2.341l-5.691-6.5-5.692,6.5h2.343v4.729H105.133A11.463,11.463,0,0,1,122.178,68.53a4.769,4.769,0,0,1,3.229-1.219,4.322,4.322,0,0,1,4.533,4.077v.027a6.253,6.253,0,0,1,6.274,2.575,5.412,5.412,0,0,1,.737,1.785A7.419,7.419,0,0,1,133.352,83.333Z" transform="translate(-42.91 -27.193)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220814" data-name="Path 220814" d="M245.995,12.517l2.17,4.812L263.119,3.176Z" transform="translate(-103.226 -1.333)" fill="#0065ff"/>
          <path id="Path_220815" data-name="Path 220815" d="M245.995,12.517l2.17,4.812L263.119,3.176Z" transform="translate(-103.226 -1.333)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.016"/>
          <path id="Path_220816" data-name="Path 220816" d="M246.628,19.04l1.366,4.779-2.17-4.812-.236-.122,1.05-.243Z" transform="translate(-103.056 -7.822)"/>
          <path id="Path_220817" data-name="Path 220817" d="M259.606,3.176l-7.076,9.765-9.244-.391-.8-.034-.236-.122-4.623-2.378Z" transform="translate(-99.713 -1.333)" fill="#0065ff"/>
          <path id="Path_220818" data-name="Path 220818" d="M259.606,3.176l-7.076,9.765-9.244-.391-.8-.034-.236-.122-4.623-2.378Z" transform="translate(-99.713 -1.333)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.016"/>
          <path id="Path_220819" data-name="Path 220819" d="M262.949,3.176,246.628,12.55l-.8-.034-.236-.122Z" transform="translate(-103.056 -1.333)"/>
          <path id="Path_220820" data-name="Path 220820" d="M222.75,223.127h-2.3V210.249h.648a1.651,1.651,0,0,1,1.651,1.651Z" transform="translate(-92.508 -88.227)" fill="#fff"/>
          <path id="Path_220821" data-name="Path 220821" d="M193.533,213.378l-9.152,11.275h5.284a5.691,5.691,0,0,1,.892-3.217l2.976-4.662Z" transform="translate(-77.372 -89.54)" fill="#fff"/>
          <path id="Path_220822" data-name="Path 220822" d="M222.75,223.127h-2.3V210.249h.648a1.651,1.651,0,0,1,1.651,1.651Z" transform="translate(-92.508 -88.227)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220823" data-name="Path 220823" d="M193.533,213.378l-9.152,11.275h5.284a5.691,5.691,0,0,1,.892-3.217l2.976-4.662Z" transform="translate(-77.372 -89.54)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <rect id="Rectangle_28776" data-name="Rectangle 28776" width="5.944" height="16.063" transform="translate(118.992 118.838)" fill="#1f1f1f"/>
          <rect id="Rectangle_28777" data-name="Rectangle 28777" width="5.944" height="16.063" transform="translate(118.992 118.838)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <rect id="Rectangle_28778" data-name="Rectangle 28778" width="4.599" height="17.903" transform="translate(115.1 116.998)" fill="#0065ff"/>
          <rect id="Rectangle_28779" data-name="Rectangle 28779" width="4.599" height="17.903" transform="translate(115.1 116.998)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <rect id="Rectangle_28780" data-name="Rectangle 28780" width="4.599" height="17.903" transform="translate(124.158 116.998)" fill="#0065ff"/>
          <rect id="Rectangle_28781" data-name="Rectangle 28781" width="4.599" height="17.903" transform="translate(124.158 116.998)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <rect id="Rectangle_28782" data-name="Rectangle 28782" width="35.378" height="1.987" transform="translate(95.711 134.406)" fill="#fff"/>
          <rect id="Rectangle_28783" data-name="Rectangle 28783" width="35.378" height="1.987" transform="translate(95.711 134.406)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220824" data-name="Path 220824" d="M72.136,314.293v35.641H57.983" transform="translate(-24.332 -131.887)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <line id="Line_77" data-name="Line 77" x1="14.152" transform="translate(33.652 201.733)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <line id="Line_78" data-name="Line 78" x1="20.38" transform="translate(0.393 203.895)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <line id="Line_79" data-name="Line 79" x1="7.359" transform="translate(15.112 198.8)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <line id="Line_80" data-name="Line 80" x1="20.38" transform="translate(0.393 220.28)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <line id="Line_81" data-name="Line 81" x1="7.359" transform="translate(15.112 215.186)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <rect id="Rectangle_28784" data-name="Rectangle 28784" width="9.368" height="9.368" transform="translate(25.954 196.96)" fill="#0065ff"/>
          <rect id="Rectangle_28785" data-name="Rectangle 28785" width="9.368" height="9.368" transform="translate(25.954 213.363)" fill="#0065ff"/>
          <rect id="Rectangle_28786" data-name="Rectangle 28786" width="9.368" height="9.368" transform="translate(24.578 195.191)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <rect id="Rectangle_28787" data-name="Rectangle 28787" width="9.368" height="9.368" transform="translate(24.578 211.594)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220825" data-name="Path 220825" d="M466.621,215.466s-6.152-17.679-6.157-19.531c-.005-2.524,3.562-4.034,5.732-2.83,3.156,1.749,8,23.493,8,23.493Z" transform="translate(-193.224 -80.856)" fill="#fff"/>
          <path id="Path_220826" data-name="Path 220826" d="M466.621,215.466s-6.152-17.679-6.157-19.531c-.005-2.524,3.562-4.034,5.732-2.83,3.156,1.749,8,23.493,8,23.493Z" transform="translate(-193.224 -80.856)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220827" data-name="Path 220827" d="M477.09,234.827c-7.609,6.28-17.048,6.993-21.029,6.986-1.5-7.609-2.547-13.657-2.547-13.657L472.2,224.1s1.983,4.241,4.894,10.722" transform="translate(-190.308 -94.041)" fill="#fff"/>
          <path id="Path_220828" data-name="Path 220828" d="M477.09,234.827c-7.609,6.28-17.048,6.993-21.029,6.986-1.5-7.609-2.547-13.657-2.547-13.657L472.2,224.1S474.179,228.346,477.09,234.827Z" transform="translate(-190.308 -94.041)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220829" data-name="Path 220829" d="M455.831,220.295a5.21,5.21,0,0,1,3.538-3.538c2.9-.849,5.591,4.67,6.227,7.713s.142,5.661-2.689,5.732-3.609-3.326-4.175-5.307a15.9,15.9,0,0,0-2.9-4.6" transform="translate(-191.28 -90.921)" fill="#fff"/>
          <path id="Path_220830" data-name="Path 220830" d="M455.831,220.295a5.21,5.21,0,0,1,3.538-3.538c2.9-.849,5.591,4.67,6.227,7.713s.142,5.661-2.689,5.732-3.609-3.326-4.175-5.307A15.9,15.9,0,0,0,455.831,220.295Z" transform="translate(-191.28 -90.921)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220831" data-name="Path 220831" d="M527.825,250l-27.819,25.757,0,0-1.627,2.052c-2.182,2.241-6.727,5.49-14.646,5.3-11.808-.277-16.934-10.508-19.93-22.931l5.635-4.384a38.615,38.615,0,0,1,13.516-15.2c16.176-10.9,37.77-10.978,37.77-10.978Z" transform="translate(-194.624 -96.356)" fill="#1f1f1f"/>
          <path id="Path_220832" data-name="Path 220832" d="M527.825,250l-27.819,25.757,0,0-1.627,2.052c-2.182,2.241-6.727,5.49-14.646,5.3-11.808-.277-16.934-10.508-19.93-22.931l5.635-4.384a38.615,38.615,0,0,1,13.516-15.2c16.176-10.9,37.77-10.978,37.77-10.978Z" transform="translate(-194.624 -96.356)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220833" data-name="Path 220833" d="M495.338,279.984c-2.182,2.241-6.727,5.49-14.646,5.3-11.808-.277-16.934-10.508-19.93-22.931-.057-.234-.112-.468-.167-.7-.234-1-.465-2.023-.7-3.059-.128-.572-.254-1.148-.379-1.724-.1-.468-.2-.938-.3-1.407q-.162-.761-.324-1.525c-.207-.979-.409-1.956-.605-2.921l-.189-.922c-.026-.134-.053-.267-.081-.4-.039-.2-.079-.4-.12-.6q-.112-.557-.22-1.1c-.051-.261-.1-.519-.153-.777-.063-.31-.124-.617-.183-.922-.053-.269-.106-.539-.157-.8-.041-.2-.081-.4-.118-.6l-.03-.147c-.034-.169-.065-.338-.1-.5a.1.1,0,0,0-.006-.035c-.043-.228-.086-.452-.129-.676-.039-.2-.079-.4-.114-.6-.043-.22-.084-.436-.126-.652,4.187.253,11.362-.678,19.763-7.486h0c.051.114.1.23.157.346.077.169.156.344.236.521.064.142.128.283.194.428.45,1,.938,2.076,1.454,3.226.49,1.091,1.006,2.249,1.545,3.458,4.639,10.432,10.86,24.879,15.424,37.215" transform="translate(-191.587 -98.525)" fill="#1f1f1f"/>
          <path id="Path_220834" data-name="Path 220834" d="M495.338,279.984c-2.182,2.241-6.727,5.49-14.646,5.3-11.808-.277-16.934-10.508-19.93-22.931-.057-.234-.112-.468-.167-.7-.234-1-.465-2.023-.7-3.059-.128-.572-.254-1.148-.379-1.724-.1-.468-.2-.938-.3-1.407q-.162-.761-.324-1.525c-.207-.979-.409-1.956-.605-2.921l-.189-.922c-.026-.134-.053-.267-.081-.4-.039-.2-.079-.4-.12-.6q-.112-.557-.22-1.1c-.051-.261-.1-.519-.153-.777-.063-.31-.124-.617-.183-.922-.277-1.4-.537-2.752-.779-4.021,4.187.253,11.364-.678,19.765-7.486,1,2.212,2.229,4.924,3.587,7.979,1.572,3.534,3.326,7.53,5.135,11.748" transform="translate(-191.587 -98.525)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220835" data-name="Path 220835" d="M478.369,239.311c-7.609,6.28-17.048,6.993-21.029,6.986-.053-.269-.106-.539-.157-.8q-.058-.3-.118-.6l-.03-.147c-.031-.169-.064-.338-.1-.505a.109.109,0,0,0-.006-.035c-.044-.228-.086-.452-.13-.676-.039-.2-.077-.4-.114-.6-.043-.221-.085-.436-.126-.653,4.187.254,11.361-.678,19.763-7.485.053.114.1.23.159.346l.236.521c.063.142.128.283.195.428.45,1,.937,2.076,1.454,3.226" transform="translate(-191.587 -98.525)" fill="#0065ff"/>
          <path id="Path_220836" data-name="Path 220836" d="M478.369,239.311c-7.609,6.28-17.048,6.993-21.029,6.986-.053-.269-.106-.539-.157-.8q-.058-.3-.118-.6l-.03-.147c-.031-.169-.064-.338-.1-.505a.109.109,0,0,0-.006-.035c-.044-.228-.086-.452-.13-.676-.039-.2-.077-.4-.114-.6-.043-.221-.085-.436-.126-.653,4.187.254,11.361-.678,19.763-7.485.053.114.1.23.159.346l.236.521c.063.142.128.283.195.428C477.364,237.082,477.852,238.161,478.369,239.311Z" transform="translate(-191.587 -98.525)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220837" data-name="Path 220837" d="M449.9,224.043c-.591,2.01,1.415,9.694,5.8,8.775s.511-9.63-1.911-10.9c-1.486-.778-3.538.92-3.892,2.123" transform="translate(-188.751 -93.041)" fill="#fff"/>
          <path id="Path_220838" data-name="Path 220838" d="M449.9,224.043c-.591,2.01,1.415,9.694,5.8,8.775s.511-9.63-1.911-10.9C452.31,221.141,450.259,222.839,449.9,224.043Z" transform="translate(-188.751 -93.041)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220839" data-name="Path 220839" d="M477.178,222.681s-.62-3.758-2.955-4.042-10.3,4.458-9.783,6.44,2.075,3.633,5.006,2.76,7.732-5.158,7.732-5.158" transform="translate(-194.883 -91.742)" fill="#fff"/>
          <path id="Path_220840" data-name="Path 220840" d="M477.178,222.681s-.62-3.758-2.955-4.042-10.3,4.458-9.783,6.44,2.075,3.633,5.006,2.76a18.4,18.4,0,0,0,4.864-2.83" transform="translate(-194.883 -91.742)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220841" data-name="Path 220841" d="M540.164,229.4s21.459-3.4,34.62,12.6,14.341,37.67,14.625,43.755l-78.17-.024a53.77,53.77,0,0,1,1.981-28.871c5.494-15.308,26.943-27.456,26.943-27.456" transform="translate(-214.067 -96.138)" fill="#1f1f1f"/>
          <path id="Path_220842" data-name="Path 220842" d="M540.164,229.4s21.459-3.4,34.62,12.6,14.341,37.67,14.625,43.755l-78.17-.024a53.77,53.77,0,0,1,1.981-28.871" transform="translate(-214.067 -96.138)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220843" data-name="Path 220843" d="M608.087,278.3s2.282,10.172,1.433,28.641" transform="translate(-255.171 -116.783)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220844" data-name="Path 220844" d="M564.239,215.654v10.682a8.48,8.48,0,0,1-13.664,0V215.654Z" transform="translate(-231.037 -90.495)" fill="#0065ff"/>
          <path id="Path_220845" data-name="Path 220845" d="M564.239,215.654v10.682a8.48,8.48,0,0,1-13.664,0V215.654Z" transform="translate(-231.037 -90.495)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220846" data-name="Path 220846" d="M563.949,213.789v9.046a7.181,7.181,0,0,1-11.571,0v-9.046Z" transform="translate(-231.794 -89.712)" fill="#fff"/>
          <path id="Path_220847" data-name="Path 220847" d="M563.949,213.789v9.046a7.181,7.181,0,0,1-11.571,0v-9.046Z" transform="translate(-231.794 -89.712)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220848" data-name="Path 220848" d="M567.9,205.232c-4.812,5.472-15.285,5.661-19.2-1.981s.262-25.475.262-25.475l-15.122.095s8.209,11.133,3.55,22.455c-3.546,8.624-11.3,11.554-16.3,8.291l0,0a9.5,9.5,0,0,1-3.664-4.893c-3.4-9.435,5.567-13.681,8.869-17.172s1.6-10.85,1.415-17.077,3.786-17.791,14.393-18.3c9.767-.472,17.215,8.68,16.177,21.889s3.208,15.379,5.849,17.455,8.586,9.246,3.774,14.719" transform="translate(-216.81 -63.428)" fill="#1f1f1f"/>
          <path id="Path_220849" data-name="Path 220849" d="M528.938,181.567c.7,6.75-6.6,10.194-8.869,13.484s-2.642,8.682,1.022,13.577a9.5,9.5,0,0,1-3.664-4.893c-3.4-9.435,5.566-13.681,8.869-17.172s1.6-10.85,1.416-17.077,3.786-17.791,14.392-18.3c-14.622,3.012-13.863,23.635-13.166,30.385" transform="translate(-216.811 -63.44)" fill="#fff"/>
          <path id="Path_220850" data-name="Path 220850" d="M542.1,151.169c-10.605.512-14.58,12.077-14.391,18.3s1.887,13.587-1.416,17.078-12.266,7.736-8.868,17.171,15.309,7.926,19.967-3.4-3.551-22.456-3.551-22.456l15.122-.094s-4.176,17.832-.261,25.474,14.388,7.454,19.2,1.981-1.133-12.643-3.775-14.719-6.887-4.245-5.85-17.455S551.871,150.7,542.1,151.169Z" transform="translate(-216.81 -63.428)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220851" data-name="Path 220851" d="M565.1,183.411c0,8.233-5.211,14.481-11.639,14.481s-11.639-6.248-11.639-14.481,5.211-16.182,11.639-16.182,11.639,7.95,11.639,16.182" transform="translate(-227.365 -70.174)" fill="#fff"/>
          <path id="Path_220852" data-name="Path 220852" d="M565.1,183.411c0,8.233-5.211,14.481-11.639,14.481s-11.639-6.248-11.639-14.481,5.211-16.182,11.639-16.182S565.1,175.179,565.1,183.411Z" transform="translate(-227.365 -70.174)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220853" data-name="Path 220853" d="M553.441,192.42H552.16v-1.992a.64.64,0,1,1,1.281,0Z" transform="translate(-231.702 -79.64)" fill="#1f1f1f"/>
          <path id="Path_220854" data-name="Path 220854" d="M566.887,184.75c.062.725,3.847,1.176,3.957.341s-4.044-1.36-3.957-.341" transform="translate(-237.882 -77.291)" fill="#1f1f1f"/>
          <path id="Path_220855" data-name="Path 220855" d="M569.058,192.42h-1.281v-1.992a.64.64,0,1,1,1.281,0Z" transform="translate(-238.256 -79.64)" fill="#1f1f1f"/>
          <path id="Path_220856" data-name="Path 220856" d="M567.475,194.147a1.465,1.465,0,0,1,1.632,0" transform="translate(-238.129 -81.367)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220857" data-name="Path 220857" d="M552.391,184.552c-.005.728-3.743,1.472-3.918.648S552.4,183.528,552.391,184.552Z" transform="translate(-230.153 -77.246)" fill="none" stroke="#000" stroke-width="0.425"/>
          <path id="Path_220858" data-name="Path 220858" d="M553.489,194.147a1.465,1.465,0,0,0-1.632,0" transform="translate(-231.575 -81.367)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220859" data-name="Path 220859" d="M560.174,200.5s-.268,1.362,1.057,1.225,1.359-1.565.406-1.6" transform="translate(-235.056 -83.979)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220860" data-name="Path 220860" d="M556.578,164.569S552,177.542,540.4,178.938c0,0-2.919-19.267,11.561-20.3S565.4,178.8,565.4,178.8s-4.151-.464-8.822-14.231" transform="translate(-226.665 -66.552)" fill="#1f1f1f"/>
          <path id="Path_220861" data-name="Path 220861" d="M556.578,164.569S552,177.542,540.4,178.938c0,0-2.919-19.267,11.561-20.3S565.4,178.8,565.4,178.8,561.248,178.336,556.578,164.569Z" transform="translate(-226.665 -66.552)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220862" data-name="Path 220862" d="M580.02,192.594s1.038-1.768,4.01-.872,2.123,8.492-4.01,6.841Z" transform="translate(-243.393 -80.346)" fill="#fff"/>
          <path id="Path_220863" data-name="Path 220863" d="M581.074,191.893a3.932,3.932,0,0,1,3.247-.171c2.906.876,2.159,8.156-3.608,6.937" transform="translate(-243.684 -80.346)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220864" data-name="Path 220864" d="M583,196.1a1.706,1.706,0,0,1,2.642.835" transform="translate(-244.644 -82.143)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <line id="Line_82" data-name="Line 82" y1="2.196" transform="translate(339.395 113.609)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220865" data-name="Path 220865" d="M565.49,208.115a3.634,3.634,0,0,1-1.154.124c-1.246-.053-2.5-.654-2.422-1.594.124-1.51,2.848-1.038,3.576-2.171s2.2-1.368,2.459,0l.012.069a3.085,3.085,0,0,1-2.471,3.572" transform="translate(-235.794 -85.409)" fill="#1f1f1f"/>
          <path id="Path_220866" data-name="Path 220866" d="M565.49,208.115a3.634,3.634,0,0,1-1.154.124c-1.246-.053-2.5-.654-2.422-1.594.124-1.51,2.848-1.038,3.576-2.171s2.2-1.368,2.459,0l.012.069A3.085,3.085,0,0,1,565.49,208.115Z" transform="translate(-235.794 -85.409)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220867" data-name="Path 220867" d="M567.16,208.728a3.635,3.635,0,0,1-1.154.124c-.187-1.075-.283-2.443.8-2.774.857-.261,1.333-1.523,2.821-.922a3.085,3.085,0,0,1-2.471,3.572" transform="translate(-237.464 -86.022)" fill="#fff"/>
          <path id="Path_220868" data-name="Path 220868" d="M539.417,192.594s-1.038-1.768-4.01-.872-2.123,8.492,4.01,6.841Z" transform="translate(-223.978 -80.346)" fill="#fff"/>
          <path id="Path_220869" data-name="Path 220869" d="M539.078,192.594s-1.038-1.768-4.01-.872-2.123,8.492,4.01,6.841" transform="translate(-223.835 -80.346)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220870" data-name="Path 220870" d="M538.624,196.1a1.706,1.706,0,0,0-2.642.835" transform="translate(-224.914 -82.143)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <line id="Line_83" data-name="Line 83" y1="2.196" transform="translate(312.672 113.609)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220871" data-name="Path 220871" d="M105.322,264.974V296.44H54.664V266.35a6.463,6.463,0,0,1,6.463-6.463H99.056a6.464,6.464,0,0,1,6.262,4.863c0,.075,0,.149,0,.224" transform="translate(-22.939 -109.056)" fill="#0065ff"/>
          <path id="Path_220872" data-name="Path 220872" d="M105.18,263.979v31.466H54.325V263.979a6.464,6.464,0,0,1,6.463-6.463H98.717a6.463,6.463,0,0,1,6.459,6.239C105.178,263.83,105.18,263.9,105.18,263.979Z" transform="translate(-22.796 -108.061)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220873" data-name="Path 220873" d="M78.42,301.858H54.326V288.145C59.364,274.561,70.082,272.8,70.082,272.8a36.272,36.272,0,0,0,4.66,7.736c2.963,3.68,1.472,7.737,2.982,15.474a32,32,0,0,1,.7,5.85" transform="translate(-22.797 -114.474)"/>
          <path id="Path_220874" data-name="Path 220874" d="M93.378,259.887v6.738a10.949,10.949,0,0,1-6.583-6.738Z" transform="translate(-36.422 -109.056)"/>
          <path id="Path_220875" data-name="Path 220875" d="M78.139,291.168v18.4H72.28a5.089,5.089,0,0,1-4.584-2.8,6.792,6.792,0,0,1-.685-2.789c0-.084,0-.171,0-.257V291.168Z" transform="translate(-28.118 -122.183)" fill="#fff"/>
          <path id="Path_220876" data-name="Path 220876" d="M78.139,312.726H72.28a5.089,5.089,0,0,1-4.584-2.8,6.8,6.8,0,0,1-.685-2.789c0-.084,0-.171,0-.257V298.7" transform="translate(-28.118 -125.342)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220877" data-name="Path 220877" d="M78.83,310.222H72.275a5.116,5.116,0,0,1-4.289-2.3l-.033-.05a5.055,5.055,0,0,1-.786-2.735V292.956l8.4.071,2.924.023Z" transform="translate(-28.186 -122.933)" fill="#fff"/>
          <path id="Path_220878" data-name="Path 220878" d="M208.279,310.6s2.312-.849,3.727-.189a10.491,10.491,0,0,1,3.68,4.387c.048.66-1.6,1.368-2.971.236s-4.435-4.435-4.435-4.435" transform="translate(-87.4 -130.153)" fill="#fff"/>
          <path id="Path_220879" data-name="Path 220879" d="M208.279,310.6s2.312-.849,3.727-.189a10.491,10.491,0,0,1,3.68,4.387c.048.66-1.6,1.368-2.971.236S208.279,310.6,208.279,310.6Z" transform="translate(-87.4 -130.153)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220880" data-name="Path 220880" d="M201.125,309.9s1.085-1.887,2.925-1.6,5.826,4.1,6.284,4.8c.792,1.2-.937,2.981-2.754,2.745s-6.03-4.072-6.03-4.072Z" transform="translate(-84.398 -129.358)" fill="#fff"/>
          <path id="Path_220881" data-name="Path 220881" d="M201.125,309.9s1.085-1.887,2.925-1.6,5.826,4.1,6.284,4.8c.792,1.2-.937,2.981-2.754,2.745s-6.03-4.072-6.03-4.072Z" transform="translate(-84.398 -129.358)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220882" data-name="Path 220882" d="M122.119,298.22H77.3l-.283-7.265a18.652,18.652,0,0,0-12.454-6.982c-.095-1.887,2.264-10.379,9.152-15.662,3.583-2.75,9.4-3.736,14.152-4.051a59.644,59.644,0,0,1,7.867-.006,41.341,41.341,0,0,1,7.984,1.626c23.037,7.265,18.4,32.341,18.4,32.341" transform="translate(-27.092 -110.837)" fill="#0065ff"/>
          <path id="Path_220883" data-name="Path 220883" d="M122.119,298.22H77.3l-.283-7.265a18.652,18.652,0,0,0-12.454-6.982c-.095-1.887,2.264-10.379,9.152-15.662,3.583-2.75,9.4-3.736,14.152-4.051a59.644,59.644,0,0,1,7.867-.006,41.341,41.341,0,0,1,7.984,1.626C126.758,273.145,122.119,298.22,122.119,298.22Z" transform="translate(-27.092 -110.837)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220884" data-name="Path 220884" d="M141.336,309.821s-.849,4.812-.849,5.85h25.569l7.831-3.868s3.963,2.642,5.378,3.019a1.9,1.9,0,0,0,2.265-2.547s-7.029-5.52-8.444-6.086-6.039.9-9.011,2.689L153.7,305.717Z" transform="translate(-58.952 -128.288)" fill="#fff"/>
          <path id="Path_220885" data-name="Path 220885" d="M141.336,309.821s-.849,4.812-.849,5.85h25.569l7.831-3.868s3.963,2.642,5.378,3.019a1.9,1.9,0,0,0,2.265-2.547s-7.029-5.52-8.444-6.086-6.039.9-9.011,2.689L153.7,305.717Z" transform="translate(-58.952 -128.288)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <line id="Line_84" data-name="Line 84" x2="0.849" y2="8.114" transform="translate(81.535 173.42)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220886" data-name="Path 220886" d="M120.572,265.88v3.273a5.573,5.573,0,0,1-1.572,3.9,8.679,8.679,0,0,1-6.412,2.616,8.355,8.355,0,0,1-6.446-2.459,5.506,5.506,0,0,1-1.421-3.739V264.26a59.644,59.644,0,0,1,7.867-.006,41.342,41.342,0,0,1,7.984,1.626" transform="translate(-43.944 -110.837)"/>
          <path id="Path_220887" data-name="Path 220887" d="M176.415,317.283s3.114,2.725,5.8,3.3,5.467-1.332,5.284-2.689-7.5-2.83-7.5-2.83Z" transform="translate(-74.029 -132.211)" fill="#fff"/>
          <path id="Path_220888" data-name="Path 220888" d="M178.382,318.632a13.689,13.689,0,0,0,4.661,2.4c2.689.577,5.467-1.332,5.284-2.689-.109-.809-2.755-1.66-4.873-2.215" transform="translate(-74.854 -132.656)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220889" data-name="Path 220889" d="M119.4,257.516v8.158a2.667,2.667,0,0,1-1.13,2.193,6.982,6.982,0,0,1-8.246-.042,2.7,2.7,0,0,1-1.234-2.265v-8.044Z" transform="translate(-45.653 -108.061)" fill="#fff"/>
          <path id="Path_220890" data-name="Path 220890" d="M119.4,257.516v8.158a2.667,2.667,0,0,1-1.13,2.193,6.982,6.982,0,0,1-8.246-.042,2.7,2.7,0,0,1-1.234-2.265v-8.044Z" transform="translate(-45.653 -108.061)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.016"/>
          <path id="Path_220891" data-name="Path 220891" d="M119.89,234.138c0,6.595-4.174,11.6-9.323,11.6s-9.324-5.005-9.324-11.6,4.174-12.963,9.324-12.963,9.323,6.368,9.323,12.963" transform="translate(-42.485 -92.812)" fill="#fff"/>
          <path id="Path_220892" data-name="Path 220892" d="M119.89,234.138c0,6.595-4.174,11.6-9.323,11.6s-9.324-5.005-9.324-11.6,4.174-12.963,9.324-12.963S119.89,227.543,119.89,234.138Z" transform="translate(-42.485 -92.812)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220893" data-name="Path 220893" d="M121.927,238.675c.446.376,2.844-1.512,2.434-2.048s-3.061,1.519-2.434,2.048" transform="translate(-51.124 -99.259)"/>
          <path id="Path_220894" data-name="Path 220894" d="M123.059,244.063h-1.026v-1.6a.513.513,0,0,1,1.026,0Z" transform="translate(-51.209 -101.531)"/>
          <path id="Path_220895" data-name="Path 220895" d="M110.55,244.063h-1.026v-1.6a.513.513,0,0,1,1.026,0Z" transform="translate(-45.959 -101.531)"/>
          <path id="Path_220896" data-name="Path 220896" d="M121.791,245.447a1.173,1.173,0,0,1,1.307,0" transform="translate(-51.107 -102.915)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.016"/>
          <path id="Path_220897" data-name="Path 220897" d="M109.567,238.638c-.416.41-2.954-1.286-2.586-1.853s3.17,1.277,2.586,1.853" transform="translate(-44.877 -99.318)"/>
          <path id="Path_220898" data-name="Path 220898" d="M110.589,245.447a1.173,1.173,0,0,0-1.307,0" transform="translate(-45.858 -102.915)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.016"/>
          <path id="Path_220899" data-name="Path 220899" d="M115.943,247.826s-.215,1.091.847.981,1.089-1.254.326-1.281" transform="translate(-48.646 -103.869)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.016"/>
          <path id="Path_220900" data-name="Path 220900" d="M135.42,239.415c1.94.975,1.378,5.908-2.534,5.551" transform="translate(-55.763 -100.466)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220901" data-name="Path 220901" d="M133.56,244.473a1.174,1.174,0,0,1,1.891.235" transform="translate(-56.046 -102.426)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220902" data-name="Path 220902" d="M110.842,251.155a4.763,4.763,0,0,0,3.927,1.946s-1.84,2.158-3.666.955c-1.159-.764-.262-2.9-.262-2.9" transform="translate(-46.374 -105.392)"/>
          <path id="Path_220903" data-name="Path 220903" d="M110.842,251.155a4.763,4.763,0,0,0,3.927,1.946s-1.84,2.158-3.666.955C109.945,253.292,110.842,251.155,110.842,251.155Z" transform="translate(-46.374 -105.392)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <circle id="Ellipse_61" data-name="Ellipse 61" cx="4.381" cy="4.381" r="4.381" transform="translate(68.644 136.043)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <circle id="Ellipse_62" data-name="Ellipse 62" cx="4.381" cy="4.381" r="4.381" transform="translate(58.759 136.043)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220904" data-name="Path 220904" d="M92.926,256.334c-.472,3.774-3.3,13.492-7.17,21.984s-6.888,9.057-6.888,9.057H72.276a5.114,5.114,0,0,1-4.289-2.3l-.033-.051a5.057,5.057,0,0,1-.785-2.734v-.939c2.522-3.451,5.6-7.513,8.4-11.173,4.311-5.634,7.973-10.308,7.973-10.308s.283-4.482,0-5.236-2.924-4.718-3.428-5.85-.959-7.265-.346-8.539a1.22,1.22,0,0,1,1.7-.707l1.934,3.4s2.028-2.5,2.924-3.68,2.595-1.038,2.972.991-1.226,3.349-1.179,4.01.189,2.076,2.312,4.482,2.972,3.821,2.5,7.6" transform="translate(-28.186 -100.086)" fill="#fff"/>
          <path id="Path_220905" data-name="Path 220905" d="M69.444,278.271c1.979-2.656,4.119-5.475,6.121-8.09,4.31-5.634,7.973-10.308,7.973-10.308s.283-4.482,0-5.236-2.925-4.718-3.428-5.85-.959-7.265-.346-8.538a1.22,1.22,0,0,1,1.7-.707l1.934,3.4s2.028-2.5,2.925-3.68,2.594-1.038,2.972.991-1.227,3.349-1.18,4.01.189,2.076,2.312,4.482,2.972,3.821,2.5,7.6-3.3,13.492-7.17,21.983-6.887,9.058-6.887,9.058H72.275a5.117,5.117,0,0,1-4.289-2.3l-.033-.051a5.052,5.052,0,0,1-.786-2.734v-.939" transform="translate(-28.186 -100.086)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220906" data-name="Path 220906" d="M116.51,228.851l-.5-1.84c-4.033-2.406-4.741-5.024-4.741-5.024s-1.981,3.75-4.529,3.75-2.76-1.9-2.76-1.9c-5.1,3.662-10.652,3.145-11.969-.068h0a4.245,4.245,0,0,1-.271-1.99,5.844,5.844,0,0,1,5.094-5.378,7.489,7.489,0,0,1-1.782-3.7c-.087-1.173.432-2.359,2.349-3.1,4.893-1.887,9.716,1.4,9.834,1.484v0c-.033-.116-.655-2.272,1.2-2.685,1.911-.425,3.821,4.3,3.821,4.3s1.628-2.319,2.548-2.036-.849,2.406-.849,2.406c3.4-.5,5.024.707,5.661,7.076s-3.114,8.7-3.114,8.7" transform="translate(-38.49 -87.44)"/>
          <path id="Path_220907" data-name="Path 220907" d="M109.642,211.557c-8.491-2.476-12.185,1.612-12.185,1.612-.086-1.173.432-2.359,2.349-3.1,4.893-1.887,9.716,1.4,9.834,1.485Z" transform="translate(-40.892 -87.907)" fill="#fff"/>
          <path id="Path_220908" data-name="Path 220908" d="M92.016,229.565h0a4.237,4.237,0,0,1-.272-1.99,5.846,5.846,0,0,1,5.1-5.378l.411.212a7.068,7.068,0,0,0-5.233,7.155" transform="translate(-38.49 -93.241)" fill="#fff"/>
          <path id="Path_220909" data-name="Path 220909" d="M116.51,228.851s3.75-2.335,3.114-8.7-2.265-7.572-5.661-7.076c0,0,1.769-2.123.849-2.406s-2.548,2.036-2.548,2.036-1.911-4.725-3.821-4.3-1.2,2.689-1.2,2.689-4.883-3.4-9.836-1.486-.566,6.793-.566,6.793a5.844,5.844,0,0,0-5.094,5.378c-.425,4.883,6.227,6.379,12.242,2.057,0,0,.212,1.905,2.76,1.905s4.529-3.75,4.529-3.75.708,2.618,4.741,5.024Z" transform="translate(-38.49 -87.44)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220910" data-name="Path 220910" d="M93.042,246.465a11.113,11.113,0,0,1-1.7-3.68c-.189-1.51-.059-5.956.366-6.876a1.743,1.743,0,0,1,2.712-.568,1.366,1.366,0,0,1,.385,1.015,61.487,61.487,0,0,0,.016,6.947,3.713,3.713,0,0,0,1.168,1.557Z" transform="translate(-38.295 -98.568)" fill="#fff"/>
          <path id="Path_220911" data-name="Path 220911" d="M93.042,246.465a11.113,11.113,0,0,1-1.7-3.68c-.189-1.51-.059-5.956.366-6.876a1.743,1.743,0,0,1,2.712-.568,1.366,1.366,0,0,1,.385,1.015,61.487,61.487,0,0,0,.016,6.947,3.713,3.713,0,0,0,1.168,1.557" transform="translate(-38.295 -98.568)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220912" data-name="Path 220912" d="M78.83,312.631H72.275a5.115,5.115,0,0,1-4.289-2.3l-.033-.05a5.055,5.055,0,0,1-.786-2.735V298.7" transform="translate(-28.186 -125.342)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <path id="Path_220913" data-name="Path 220913" d="M619.628,363.673H523.037a4.078,4.078,0,0,1-4.078-4.078V309.8a4.077,4.077,0,0,1,4.078-4.078h96.591a4.078,4.078,0,0,1,4.078,4.078v49.8a4.078,4.078,0,0,1-4.078,4.078" transform="translate(-217.77 -128.288)" fill="#0065ff"/>
          <path id="Path_220914" data-name="Path 220914" d="M611.5,359.27H514.908a4.078,4.078,0,0,1-4.078-4.078v-49.8a4.078,4.078,0,0,1,4.078-4.078H611.5a4.078,4.078,0,0,1,4.078,4.078v49.8A4.078,4.078,0,0,1,611.5,359.27Z" transform="translate(-214.359 -126.441)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <line id="Line_85" data-name="Line 85" x2="62.201" transform="translate(322.506 193.318)" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <line id="Line_86" data-name="Line 86" x2="62.201" transform="translate(322.506 207.743)" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
          <line id="Line_87" data-name="Line 87" x2="62.201" transform="translate(322.506 222.169)" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.355"/>
        </g>
      </g>
    </g>
  </g>
</svg>
