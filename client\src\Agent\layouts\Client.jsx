
import React from "react";
import FooterVersion from "../components/Footer/FooterVersion";

import "../assets/scss/Agent.scss";

class Client extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      backgroundColor: "black",
      activeColor: "info",
      mainclass: "mainLayout",
      inMatrix: true,
      user: {},
      location: '',
    };
    this.mainPanel = React.createRef();
  }

  render() {
    const {element} = this.props;
    return (
      <div className="wrapper">
        <div className='mainLayout' ref={this.mainPanel}>
         {element}
         <FooterVersion/>
        </div>
      </div>
    );
  }
}

export default Client;
