const sqlHelper = require("../../Libs/sqlHelper");
const conf = require('../../env_config');
const { Base64Encode } = require('../../auth');

async function VerifyRequestSource({
  ApplicationId,
  SourceApp,
  ClientKey
}) {

  try {
    let sqlParams = [];
    sqlParams.push({ key: 'ApplicationId', value: parseInt(ApplicationId) })
    sqlParams.push({ key: 'ApplicationName', value: SourceApp })
    sqlParams.push({ key: 'ClientKey', value: ClientKey });

    const query = `SELECT * from PBT.ApplicationMaster where ApplicationId = @ApplicationId
                   and ApplicationName = @ApplicationName
                   and ClientKey = @ClientKey and IsActive = 1`;

    let result = await sqlHelper.sqlquery("R", query, sqlParams);
    return result && result.recordset && result.recordset || [];

  } catch (err) {
    console.log('VerifyRequestSource', err);
    return [];
  }
}

async function CheckQuizAgent(req, res) {
  try {

    const { employeeid, clientkey, sourceapp, applicationid } = req.headers;

    const VerifySource = await VerifyRequestSource({
      ApplicationId: parseInt(applicationid),
      SourceApp: sourceapp,
      ClientKey: clientkey
    })

    if (VerifySource && VerifySource.length > 0) {

      let sqlParams = [];
      sqlParams.push({ key: 'ApplicationId', value: parseInt(applicationid) })
      sqlParams.push({ key: 'EmployeeId', value: employeeid })

      let response = await sqlHelper.sqlProcedure("R", "[PBT].[CheckQuizAgent]", sqlParams);

      let result = response && response.recordset && response.recordset[0] || {};

      const UserInfo = {
        "EmployeeId": employeeid,
        "SourceApp": sourceapp,
        "ApplicationId": applicationid,
        "ClientKey": clientkey
      };

      const Info = Base64Encode(JSON.stringify(UserInfo));

      if (result && result.Status ===200 && !result.IsComplete) {
        result["QuizUrl"] = conf.LMS_BASE_URL + "client/PBQuiz?qid=" + result.QuizId + "&info=" + Info;
      }

      res.cookie(`PBLMSToken`, Info);

      return res.status(200).json({
        status: 200,
        message: 'success',
        data: result
      });
    } else {
      return res.status(401).json({
        status: 401,
        message: 'Unauthorized Source'
      })
    }

  } catch (err) {
    return res.send({
      status: 500,
      message: err.toString(),
    });
  }

}

module.exports = {
  CheckQuizAgent: CheckQuizAgent
}