const tblList = require("../constants");
const methods = require("./RmsMethods");
let fs = require('fs');
const path = require("path");
const moment = require("moment");
const plannedLeaveRules = require("./plannedLeaveRules");


async function CreateRoster(req, res) {
    try {
       await methods.CreateRoster(req, res);
    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function CreateRosterv2(req, res) {
    try {
       await methods.CreateRosterv2(req, res);
    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function RmsRedirectionToUrl(req, res) {
    try {
        await methods.RmsRedirectionToUrl(req, res);
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
         // res.send({
         //     status: 200,
         //     message: 'Records are in process.'
         // });
         //await sql.close();
     }

}

async function DownloadRotaData(req, res) {
    try {
        await methods.DownloadRotaData(req, res);
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
         // res.send({
         //     status: 200,
         //     message: 'Records are in process.'
         // });
         //await sql.close();
     }

}

async function InsertRosterHistory(req, res) {
    try {
        await methods.InsertRosterHistory(req, res);
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
         // res.send({
         //     status: 200,
         //     message: 'Records are in process.'
         // });
         //await sql.close();
     }

}

async function InsertLeaveRequest(req, res) {
    try {
        await methods.InsertLeaveRequest(req, res);
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
         // res.send({
         //     status: 200,
         //     message: 'Records are in process.'
         // });
         //await sql.close();
     }

}

async function ApplyAutoLeave(req, res) {
    try {
        await methods.ApplyAutoLeave(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
         // res.send({
         //     status: 200,
         //     message: 'Records are in process.'
         // });
         //await sql.close();
     }

}

async function InsertTransferRequest(req, res) {
    try {
        await methods.InsertTransferRequest(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
         // res.send({
         //     status: 200,
         //     message: 'Records are in process.'
         // });
         //await sql.close();
     }

}


async function updateAdvisorTransferData(req, res) {
    try {
        await methods.updateAdvisorTransferData(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
         // res.send({
         //     status: 200,
         //     message: 'Records are in process.'
         // });
         //await sql.close();
     }

}


async function updateAdvisorRemoveRequest(req, res) {
    try {
        await methods.updateAdvisorRemoveRequest(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
         // res.send({
         //     status: 200,
         //     message: 'Records are in process.'
         // });
         //await sql.close();
     }

}

async function addAdvisorsProcess(req, res) {
    try {
        await methods.addAdvisorsProcess(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
         // res.send({
         //     status: 200,
         //     message: 'Records are in process.'
         // });
         //await sql.close();
     }

}

async function FreezedTlData(req, res) {
    try {
        await methods.FreezedTlData(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
         // res.send({
         //     status: 200,
         //     message: 'Records are in process.'
         // });
         //await sql.close();
     }

}

async function getTLRosterData(req, res) {
    try {
        await methods.getTLRosterData(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
         // res.send({
         //     status: 200,
         //     message: 'Records are in process.'
         // });
         //await sql.close();
     }

}

async function MenuAuthentication(req, res) {
    try {
        await methods.MenuAuthentication(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
         // res.send({
         //     status: 200,
         //     message: 'Records are in process.'
         // });
         //await sql.close();
     }
}

async function GetUserMenus(req, res) {
    try {
        await methods.GetUserMenus(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
         // res.send({
         //     status: 200,
         //     message: 'Records are in process.'
         // });
         //await sql.close();
     }
}

async function plannedLeave(req, res) {
    try {
        await methods.plannedLeave(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
}

async function GetRosterData(req, res) {
    try {
        await methods.GetRosterData(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
     }

}

async function updateLeaveApproval(req, res) {
    try {
        await methods.updateLeaveApproval(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
     }

}

async function updateLeaveRejection(req, res) {
    try {
        await methods.updateLeaveRejection(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
     }

}

async function updateLeaveCancellation(req, res) {
    try {
        await methods.updateLeaveCancellation(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
     }

}

async function leaveAppliedByManager(req, res) {
    try {
        await methods.leaveAppliedByManager(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
     }

}

async function getAdvisorData(req, res) {
    try {
        await methods.getAdvisorData(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
     }

}

async function validationPlannedLeave(req, res) {
    try {
        await methods.validationPlannedLeave(req, res);
        
     } catch (err) {
         console.log(err);
         res.send({
             status: 500,
             error: err
         });
     }
     finally {
     }

}

module.exports = {
    CreateRoster: CreateRoster,
    CreateRosterv2: CreateRosterv2,
    RmsRedirectionToUrl: RmsRedirectionToUrl,
    DownloadRotaData: DownloadRotaData,
    InsertRosterHistory: InsertRosterHistory,
    InsertLeaveRequest: InsertLeaveRequest,
    ApplyAutoLeave: ApplyAutoLeave,
    InsertTransferRequest: InsertTransferRequest,
    updateAdvisorTransferData: updateAdvisorTransferData,
    addAdvisorsProcess: addAdvisorsProcess,
    FreezedTlData : FreezedTlData,
    getTLRosterData : getTLRosterData,
    MenuAuthentication : MenuAuthentication,
    GetUserMenus: GetUserMenus,
    updateAdvisorRemoveRequest: updateAdvisorRemoveRequest,
    plannedLeave: plannedLeave,
    GetRosterData: GetRosterData,
    updateLeaveApproval: updateLeaveApproval,
    updateLeaveRejection: updateLeaveRejection,
    updateLeaveCancellation: updateLeaveCancellation,
    leaveAppliedByManager: leaveAppliedByManager,
    getAdvisorData:getAdvisorData,
    validationPlannedLeave: validationPlannedLeave,
};