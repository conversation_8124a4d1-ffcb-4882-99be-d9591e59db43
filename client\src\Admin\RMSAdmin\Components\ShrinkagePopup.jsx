import React,{useState,  useContext, useEffect} from "react";
import { connect } from "react-redux";
import { GetCommonData, InsertData, UpdateData, GetCommonspData, DeleteData} from "../../store/actions/CommonAction";

import { Box } from '@mui/material';

import moment from 'moment';

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import TextField from '@mui/material/TextField';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import { ShrinkageContext } from "../Context/ConfigContext";
import Alert from '@mui/material/Alert';
import AlertTitle from '@mui/material/AlertTitle';


const ShrinkagePopup=(props)=>{

let data= props.data;

// const [open, setOpen] = useState(true);
const handleClose = () => shrinkagePopup.setPopup();
const shrinkagePopup= useContext(ShrinkageContext);
const [shrinkageValue, setShrinkageValue]= useState(data?.Shrinkage);
const [success,setSuccess]= useState(false);
const [alertMsg,setAlertMsg]= useState({});
const [failure,setFailure]= useState(false);


useEffect(()=>{

  if(success)
  {
  shrinkagePopup.handleSave(data, shrinkageValue, alertMsg);
  }
  

},[success])

useEffect(()=>{

  if(failure)
  {
  shrinkagePopup.handleSave(data, shrinkageValue, alertMsg);
  }
  

},[failure])

const handleSave=()=>{
     
    props.GetCommonspData({
      root: "ShrinkageEditCheck",
      c: "L",
      params: [{ ShrinkageChange: shrinkageValue, Id: data.Id }],
    },(data)=>{
           if(Array.isArray(data?.data?.data[0]))
           {
            // console.log("The data on proc exec is ", data.data.data[0][0]);
            let response= data.data.data[0][0];
            if(response.ProcStatus==200)
            {
             setSuccess(true);
             let obj={};
             obj.success=1;
              obj.Msg="Shrinkage Edited";
             setAlertMsg(obj);
            }
            else{
              setFailure(true);
              let obj={};
              obj.success=0;
              obj.Msg=response.ProcMessage;
              setAlertMsg(obj);
            }
           }
    })
    // console.log("sucess is", success);
    // shrinkagePopup.handleSave(data, shrinkageValue);
    
}

const handleShrinkageChange=(e)=>{
    setShrinkageValue(e.target.value);
}


return(
    <Box>
      <Dialog open={shrinkagePopup.popup} onClose={handleClose} aria-labelledby="form-dialog-title">
        <DialogTitle id="form-dialog-title">Shrinkage Data</DialogTitle>

        <DialogContent>
          <DialogContentText>
            {"Roster Date "+ moment(data?.RosterDate).format('DD/MM/YY')}
          </DialogContentText>
            <br/>
          <TextField
            autoFocus
            id="name"
            type="number"
            margin="dense"
            label="Shrinkage Amount"
            onChange={handleShrinkageChange}
            value={shrinkageValue}
          />
        </DialogContent>

        <DialogActions>
          <Button variant="outlined" color="secondary" onClick={handleClose}>
            Cancel
          </Button>

          <Button onClick={handleSave} color="primary">
             SAVE 
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
)

}

function mapStateToProps(state) {
    return {
      CommonData: state.CommonData
    };
  }
  
  export default connect(
    mapStateToProps,
    {
      GetCommonData,
      GetCommonspData,
      InsertData,
      UpdateData,
      DeleteData
    }
  )(ShrinkagePopup);