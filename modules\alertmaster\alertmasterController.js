async function firstListingOfData(req, res) {
  try {
    console.log("inside firstListingOfData", req.query);

    let skip = req.query.skip ? parseInt(req.query.skip) : 0,
      limit = req.query.limit ? parseInt(req.query.limit) : 10;

    let sqlQuery = `declare @skipRows int =${skip},
    @takeRows int = ${limit},
    @count int = 0`;

    if (req.query.search && req.query.search !== "") {
      console.log("=====within search veereeee===============");
      sqlQuery =
        sqlQuery +
        `,@SearchLetter varchar(256),
  @SearchLetter2 varchar(256)
  SET @SearchLetter = '${req.query.search}'
  SET @SearchLetter2 = '%'+@SearchLetter+'%'`;
    }

    sqlQuery =
      sqlQuery +
      `;WITH Orders_cte AS (
    SELECT *
    FROM dbo.alertmaster)
    SELECT 
    *
    FROM dbo.alertmaster
    CROSS JOIN (`;

    if (req.query.search && req.query.search !== "")
      sqlQuery =
        sqlQuery +
        `SELECT Count(*) AS TotalRows FROM Orders_cte WHERE  alerttitle LIKE @SearchLetter2 ) AS tCountOrders `;
    else
      sqlQuery =
        sqlQuery +
        `SELECT Count(*) AS TotalRows FROM Orders_cte ) AS tCountOrders `;

    if (req.query.search && req.query.search !== "")
      sqlQuery = sqlQuery + `WHERE  alerttitle LIKE @SearchLetter2 `;

    sqlQuery =
      sqlQuery +
      `ORDER BY id
OFFSET @skipRows ROWS
FETCH NEXT @takeRows ROWS ONLY`;

    const result = await sql.query(sqlQuery);

    console.log(
      "===============result===========result===",
      result.recordsets[0].length
    );
    res.send({
      status: 200,
      data: result.recordsets[0],
      message: "Success"
    });
  } catch (err) {
    console.log(err);
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
}

// { Id: 3,
//   type: null,
//   alerttitle: 'Mongoquery123',
//   : 'MongoQuery',
//   : 'testdatabase',
//   : 'mycoll',
//   : 'Replica',
//   tomail: '',
//   tomobile: '**********',
//   : 1,
//   operation: 'lt',
//   starttime: null,
//   frequency: 120,
//   IsActive: false,
//   reporttype: null,
//   endtime: null,
//   nextruntime: '2018-06-23T00:00:00.000Z',
//   createddate: null,
//   createdby: null,
//   updateddate: null,
//   updatedby: null,
//   ExcelSheetNames: 'BaseData,Health,Term,Investment,Motor,EMAILBODY,EMAILBODY',
//   querysource: '',
//   ccmail: null,
//   bccmail: null,
//   frommail: null }

async function updateData(req, res) {
  try {
    console.log("======updateData====req.body======", req.body);

    let result = `UPDATE alertmaster SET alerttitle = '${req.body.alerttitle}'`;

    if (req.body.type) result = result + `,type = '${req.body.type}'`;

    if (req.body.querytype)
      result = result + `,querytype = '${req.body.querytype}'`;

    if (req.body.databasename)
      result = result + `,databasename = '${req.body.databasename}'`;

    if (req.body.collection)
      result = result + `,collection = '${req.body.collection}'`;

    if (req.body.dbserver)
      result = result + `,dbserver = '${req.body.dbserver}'`;

    if (req.body.tomail) result = result + `,tomail = '${req.body.tomail}'`;

    if (req.body.tomobile)
      result = result + `,tomobile = '${req.body.tomobile}'`;

    if (req.body.count) result = result + `,count = '${req.body.count}'`;

    if (req.body.operation)
      result = result + `,operation = '${req.body.operation}'`;

    if (req.body.starttime)
      result = result + `,starttime = '${req.body.starttime}'`;

    if (req.body.frequency)
      result = result + `,frequency = '${req.body.frequency}'`;

    if (req.body.IsActive)
      result = result + `,IsActive = '${req.body.IsActive}'`;

    if (req.body.reporttype)
      result = result + `,reporttype = '${req.body.reporttype}'`;

    if (req.body.endtime) result = result + `,endtime = '${req.body.endtime}'`;

    if (req.body.nextruntime)
      result = result + `,nextruntime = '${req.body.nextruntime}'`;

    if (req.body.createddate)
      result = result + `,createddate = '${req.body.createddate}'`;

    if (req.body.createdby)
      result = result + `,createdby = '${req.body.createdby}'`;

    if (req.body.updateddate)
      result = result + `,updateddate = '${req.body.updateddate}'`;

    if (req.body.updatedby)
      result = result + `,updatedby = '${req.body.updatedby}'`;

    if (req.body.ExcelSheetNames)
      result = result + `,ExcelSheetNames = '${req.body.ExcelSheetNames}'`;

    if (req.body.querysource)
      result = result + `,querysource = '${req.body.querysource}'`;

    if (req.body.ccmail) result = result + `,ccmail = '${req.body.ccmail}'`;

    if (req.body.bccmail) result = result + `,bccmail = '${req.body.bccmail}'`;

    if (req.body.frommail)
      result = result + `,frommail = '${req.body.frommail}'`;

    result = result + ` WHERE Id = ${req.body.Id} `;

    console.log("======updateData=========result", result);

    await sql.query(result);

    result = await sql.query`SELECT  * FROM alertmaster WHERE Id = ${req.body.Id} `;

    console.log("===result=====edit=====", result);
    res.send({
      status: 200,
      data: result,
      message: "Success"
    });
  } catch (err) {
    console.log(err);
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
}

async function getItemById(req, res) {
  try {
    console.log(
      "==getItemById getItemById getItemById getItemById getItemById getItemById==",
      req.query
    );
    let Id = parseInt(req.query.Id);
    res.send({
      status: 200,
      data: (await sql.query`SELECT  * FROM alertmaster WHERE Id = ${Id}`)
        .recordsets[0][0],
      message: "Success"
    });
  } catch (err) {
    console.log(err);
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
}

async function addItem(req, res) {
  try {
    console.log(
      "==addItem addItem addItem addItem addItem addItem==",
      req.body
    );
    let maxId = await sql.query`SELECT max(Id) AS maxID FROM alertmaster`;
    maxId = maxId.recordset[0].maxID + 1;
    console.log("========maxId=================", maxId);
    let result = await sql.query`INSERT INTO  alertmaster (Id,querytype, alerttitle,type,dbserver,starttime,endtime,nextruntime,frequency,
      IsActive,reporttype,createdby,tomail,ExcelSheetNames,querysource)
       VALUES (${maxId},${req.body.querytype},${req.body.alerttitle},${req.body.type},${req.body.dbserver},${req.body.starttime},${req.body.endtime},${req.body.nextruntime},
        ${req.body.frequency},${req.body.IsActive},${req.body.reporttype},${req.body.createdby},${req.body.tomail},${req.body.ExcelSheetNames},${req.body.querysource})`;

    console.log(
      "===++++++addItemaddItemaddItemaddItemaddItemaddItemaddItemaddItem#++#+#+#+#+#+#+================",
      result
    );
    res.send({
      status: 200,
      data: result,
      message: "Success"
    });
  } catch (err) {
    console.log(err);
    res.send({
      status: 500,
      error: "Please enter correct inputs."
    });
  }
}

module.exports = {
  firstListingOfData: firstListingOfData,
  updateData: updateData,
  getItemById: getItemById,
  addItem: addItem
};
