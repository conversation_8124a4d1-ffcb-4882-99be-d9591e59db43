import React, { useState, useEffect } from 'react';
import "../../../Admin/RMSAdmin/Confirmation/RosterConfirmation.scss"
import { Container, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography, Select, MenuItem, FormControl } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import CheckIcon from '@mui/icons-material/Check';
import HistoryIcon from '@mui/icons-material/History';
import { GetCommonData, GetCommonspData, GetCommonspDataV2, updateAdvisorTransferData, updateAdvisorRemoveRequest } from '../../store/actions/CommonAction';
// import {  FormControl, Grid, MenuItem, Select, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tooltip, Typography } from '@mui/material';

import ApproveRequestPopup from './ApproveRequestPopup';
import RejectedPopup from './RejectedPopup';
import { connect } from "react-redux";
import { useNavigate } from "react-router-dom";
import _ from 'underscore';
import moment from 'moment';
import { FilterBAndW } from '@mui/icons-material';

const Mis = (props) => {

    const navigate = useNavigate();
    const [open, setOpen] = useState(false);
    const [openreject, setOpenReject] = useState(false);
    const [userData, setUserData] = useState([]);
    const [activeClass, setActiveClass] = useState('Pending');
    const [filterData, setFilterData] = useState([]);
    const [user, setUser] = useState([]);
    const [product, setProduct] = useState([]);
    const [statemaster, setStateMaster] = useState([]);
    const [selectedProduct, setSelectedProduct] = useState(0);
    const [selectedState, setSelectedState] = useState(0);
    const [eventDate, setEventDate] = useState();

    useEffect(() => {
        GetProductData();
        GetStateData();
    }, [])

    useEffect(() => {
        GetTLRosterStatusData();
    }, [selectedProduct, selectedState])

    useEffect(() => {
        if(activeClass == 'Pending'){
            handlePendingData();
        }
        else if(activeClass == 'Approved'){
            handleApprovedData();
        }
        else{
            handleRejectedData();
        }
    }, [userData]) 

    const GetTLRosterStatusData = () => {
        // setActiveClass('Pending');
        if (selectedProduct > 0 && selectedState > 0) {
            // props.GetCommonspDataV2({
            //     root: 'GetTLRosterStatusDataMis',
            //     params: [{ ShrinkageChange: shrinkageValue, Id: data.Id }],
            //     ProductId: selectedProduct || 0, StateId: selectedState                 c: "R",
            // }, 
            props.GetCommonspData({
                root: "GetTLRosterStatusDataMis",
                params: [{ProductId: selectedProduct || 0, StateId: selectedState,}],
                c: "R",
            }, function (response) {
                if (response?.data?.data && response?.data?.data[0].length > 0) {
                    setUserData(response?.data?.data[0]);

                    let data = response?.data?.data[0];
                    let PendingData = [];
                    // for (let i = 0; i < data.length; i++) {
                    //     if (data[i].Status == 3) {
                    //         PendingData.push(data[i]);
                    //     }
                    // }
                    // setFilterData(PendingData);
                    setEventDate(response?.data?.data[0][0].EventDate);
                    console.log(activeClass);
                }
                else {
                    setFilterData([]);
                    setUserData([]);
                }
            })
        }
    }

    const GetData = (userid) => {
        for (let i = 0; i < filterData.length; i++) {
            if (filterData[i].UserId == userid) {
                setUser(filterData[i]);
            }
        }
    }

    const handleClickOpen = (e) => {
        setOpen(true);
        GetData(e.target.value);
    };

    const handleClose = () => {
        setOpen(false);
        GetData([]);
    };

    const handleRejectClickOpen = (e) => {
        setOpenReject(true);
        GetData(e.target.value);
    };

    const handleRejectClickClose = () => {
        setOpenReject(false);
        GetData([]);
    };

    const handleAllData = () => {
        setActiveClass('All');
        setFilterData(userData);
    }

    const HandleChangeHistory = (e) => {
        console.log(e)
        navigate("/admin/rms/History");
    }

    const handlePendingData = () => {
        setActiveClass('Pending');
        let PendingData = [];
        for (let i = 0; i < userData.length; i++) {
            if (userData[i].Status == 3) {
                PendingData.push(userData[i]);
            }
        }
        setFilterData(PendingData);
    }

    const handleApprovedData = () => {
        setActiveClass('Approved');
        let PendingData = [];
        for (let i = 0; i < userData.length; i++) {
            if (userData[i].Status == 2) {
                PendingData.push(userData[i]);
            }
        }
        setFilterData(PendingData);
    }

    const handleRejectedData = () => {
        setActiveClass('Rejected');
        let PendingData = [];
        for (let i = 0; i < userData.length; i++) {
            if (userData[i].Status == 0) {
                PendingData.push(userData[i]);
            }
        }
        setFilterData(PendingData);
    }

    const handleApprove = (TransferId, userId, status, comment = '') => {
        // console.log(userId, status)
        // updateAdvisorData(userId, status);
        const transferData = {
            TransferId: TransferId,
            userId: userId,
            status: status,
            comment: comment
        }
        updateAdvisorTransferData(transferData, function (result) {
            if (result && result.data && result.data.status == 200) {
                console.log(result);
                GetTLRosterStatusData();
                setOpen(false);
                setOpenReject(false);
            }
        })
    }

    const handleRemovalRequest = (TransferId, userId, status, processChange, ManagerEmployeeId) => {
        const transferData = {
            TransferId: TransferId,
            UserId: userId,
            Status: status,
            ProcessId: processChange,
            ManagerEmployeeId: ManagerEmployeeId
        }
        updateAdvisorRemoveRequest(transferData, function (result) {
            if (result && result.data && result.data.status == 200) {
                console.log(result);
                GetTLRosterStatusData();
                setOpen(false);
                setOpenReject(false);
            }
        })
    }

    const GetProductData = () => {
        props.GetCommonData({
            limit: 10,
            skip: 0,
            root: 'ProcessMaster',
            cols: ["ProcessId", "ProcessName", "ProductId", "ProductName"],
            order: ["ProcessName"],
            direction: ['ASC'],
            con: [{ "IsActive": 1 }],
            c: "R",
        }, function (data) {
            debugger;
            let ProductList = _.uniq(data, (item) => {
                return item.ProductId
            })
            setProduct(ProductList);
        })
    }

    const GetStateData = () => {
        props.GetCommonData({
            root: 'HRMS_State',
            c: "R"
        }, function (data) {
            debugger;

            let StateList = _.uniq(data, (item) => {
                return item.StateId
            })
            setStateMaster(StateList);
        })

    }


    const handleProductChange = (e) => {
        console.log(e);
        setSelectedProduct(e.target.value);
    }
    const handleStateChange = (e) => {
        console.log(e);
        setSelectedState(e.target.value);
    }

    return (
        <div className="RoasterConfirmation">
            <Container maxWidth="xl">
                <header>
                    <Typography variant="h2" className="Heading">MIS</Typography>
                    {eventDate && <p>Approve or decline requests from supervisors of roster before <b> {moment(eventDate).format('DD/MM/YY')} </b></p>}
                </header>


                <Grid container spacing={2} mt={2}>
                    <Grid item md={12} xs={12} sm={12}>
                        <label className="marginBotton0">Filter by</label>
                    </Grid>
                    <Grid item md={2.5} sm={6} xs={12}>
                        <label>Select Product</label>
                        <FormControl fullWidth>
                            {/* {rosterDates && <div className="BlueBgColor bgFrame">{rosterDates}</div>} */}
                            <Select className="BlueBgColor" onChange={handleProductChange}>
                                {product && product.map((product) =>
                                    <MenuItem value={product.ProductId} >{product.ProductName}</MenuItem>
                                )}
                                {/* <MenuItem value={2}>02 Oct 2023 - 15 Oct 2023</MenuItem>
                                <MenuItem value={3}>03 Oct 2023 - 15 Oct 2023</MenuItem>
                                <MenuItem value={4}>04 Oct 2023 - 15 Oct 2023</MenuItem> */}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item md={2.5} sm={6} xs={12}>
                        <label>Select State</label>
                        <FormControl fullWidth>
                            {/* {rosterDates && <div className="BlueBgColor bgFrame">{rosterDates}</div>} */}
                            <Select className="BlueBgColor" onChange={handleStateChange} >
                                {statemaster && statemaster.map((state) =>
                                    <MenuItem value={state.StateId} >{state.StateName}</MenuItem>
                                )}
                                {/* <MenuItem value={2}>02 Oct 2023 - 15 Oct 2023</MenuItem>
                                <MenuItem value={3}>03 Oct 2023 - 15 Oct 2023</MenuItem>
                                <MenuItem value={4}>04 Oct 2023 - 15 Oct 2023</MenuItem> */}
                            </Select>
                        </FormControl>
                    </Grid>

                    <Grid item md={1} sm={3} xs={3}>
                        <button className={"BlueBgColor bgFrame mt-1 " + (activeClass == 'Pending' ? "Active" : "")} onClick={handlePendingData}>Pending </button>
                    </Grid>
                    <Grid item md={1} sm={3} xs={3}>
                        <button className={"BlueBgColor bgFrame mt-1 " + (activeClass == 'Approved' ? "Active" : "")} onClick={handleApprovedData}>Approved</button>
                    </Grid>
                    <Grid item md={1} sm={3} xs={3}>
                        <button className={"BlueBgColor bgFrame mt-1 " + (activeClass == 'Rejected' ? "Active" : "")} onClick={handleRejectedData} >Rejected</button>
                    </Grid>

                    {/* <Grid item md={9} sm={6} xs={12}>
                        <div className="BlueBgColor bgFrame HistoryButton mt-0" onClick={HandleChangeHistory}> <HistoryIcon /> History</div>
                    </Grid> */}
                    <Grid item xs={12} md={12} sm={12}>

                        <TableContainer className="Rostertable MisTable" sx={{ maxHeight: 320 }}>
                            <Table stickyHeader aria-label="sticky table">
                                <TableHead>
                                    <TableRow>
                                        <TableCell align="left">Sr. no</TableCell>
                                        <TableCell align="left">TL name</TableCell>
                                        <TableCell align="left">Request E-Code</TableCell>
                                        <TableCell align="left">Request E-Name</TableCell>
                                        {/* <TableCell align="left">Usergroup</TableCell> */}
                                        <TableCell align="left">Request type</TableCell>
                                        {activeClass === 'Approved' ? < TableCell align="left">Old Process</TableCell>
                                            :
                                            <TableCell align="left">Current process</TableCell>}

                                        {activeClass === 'Approved' ? < TableCell align="left">New Process</TableCell>
                                            :
                                            activeClass !== 'Rejected' ? <TableCell align="left">Move to process</TableCell> : ""}
                                        {/* <TableCell align="left">Move to process</TableCell> */}
                                        {activeClass == 'Pending' && <TableCell align="left">Action</TableCell>}
                                    </TableRow>
                                </TableHead>

                                {filterData && filterData.map((users, index) =>
                                    <TableBody>
                                        <TableRow>
                                            <TableCell align="left">{index + 1}</TableCell>
                                            <TableCell align="left">{users.ManagerName}</TableCell>
                                            <TableCell align="left">{users.EmployeeId}</TableCell>
                                            <TableCell align="left">{users.EmployeeName}</TableCell>
                                            <TableCell align="left">{users.RequestName}</TableCell>
                                            <TableCell align="left">{users.ProcessName}</TableCell>

                                            {activeClass !== 'Rejected' && <TableCell align="left" className="Null">{users.TransferProcessTo}</TableCell>}

                                            {/* <TableCell align="left"><button className="StatusButton Approved">{users.StatusName}</button></TableCell> */}
                                            {users.Status == 3 && activeClass == 'Pending' && <TableCell align="left">
                                                <button className="TransferBtn" value={users.UserId} onClick={handleClickOpen}>  <CheckIcon /> Approve</button>
                                                <button className="RemoveBtn" value={users.UserId} onClick={handleRejectClickOpen} > <CloseIcon /> Reject</button>
                                            </TableCell>}
                                        </TableRow>
                                    </TableBody>
                                )}
                            </Table>

                        </TableContainer>

                    </Grid>
                </Grid>
            </Container>
            <ApproveRequestPopup open={open} onClose={handleClose} user={user} handleApprove={handleApprove} handleRemovalRequest={handleRemovalRequest} />
            <RejectedPopup open={openreject} onClose={handleRejectClickClose} user={user} handleApprove={handleApprove} />
        </div>

    );
};

// export default Mis;
export default connect(
    null,
    {
        GetCommonData,
        GetCommonspData,
        GetCommonspDataV2
    }
)(Mis);
