const tblList = require("../constants");
const methods = require("./SQLdbMethods");
const IncFileUpload = require("./IncFileUpload");
let fs = require('fs');
const path = require("path");
const moment = require("moment");
const sqlHelper = require("../../Libs/sqlHelper");
const sqlHelperPbCroma = require("../../LibsPbCroma/sqlHelper");
const Utility = require("../../Libs/Utility");
const UploadMatrixFiles = require("./UploadMatrixFiles");
const UploadStoryFiles = require("./UploadStoryFile");
const { Base64Encode } = require('../../auth');
const conf = require('../../env_config');

async function insert(req, res) {
    try {


        let endpoint = req.body.data.root;
        let inputdata = req.body.data.body;
        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        var query = 'INSERT INTO ' + tblList[endpoint] + '(';
        var columns = [];
        var VALUES = [];
        let sqlparams = [];
        // for (var key in inputdata) {
        //     columns.push(key);
        //     VALUES.push(inputdata[key]);
        // }
        for (var key in inputdata) {
            columns.push(`${key}`);
            VALUES.push(`@${key}`);
            sqlparams.push({ key: key, value: inputdata[key] });
        }
        query = query + columns.join() + ") VALUES ( ";
        query = query + VALUES.join() + ")";

        if (req.body.data.scope)
            query = query + "; SELECT SCOPE_IDENTITY()";
        console.log(inputdata);
        console.log(query);
        console.log(sqlparams);
        let result = await sqlHelper.sqlquery("L", query, sqlparams);
        console.log(result);
        let e = JSON.stringify(result);
        var myArr = JSON.parse(e);

        if (typeof (myArr[0]) !== 'undefined') {
            if (typeof (myArr[0].error) !== 'undefined') {
                return res.send({
                    status: 500,
                    error: myArr[0].error
                });
            }
        }

        return res.send({
            status: 200,
            data: result,
            message: "Success"
        });
    } catch (err) {
        console.log(err);
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}

async function update(req, res) {
    try {

        let endpoint = req.body.data.root;
        let inputdata = req.body.data.body;
        let querydata = req.body.data.querydata;
        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        var query = 'UPDATE ' + tblList[endpoint] + ' SET ';
        var updatedata = [];
        var updatequery = [];
        let sqlparams = [];

        for (var key in inputdata) {
            updatedata.push(`${key} = @${key}`);
            sqlparams.push({ key: key, value: inputdata[key] });
        }
        query = query + updatedata.join();
        query = query + ` WHERE `;

        for (var key in querydata) {
            updatequery.push(`${key} = @${key}`);
            sqlparams.push({ key: key, value: querydata[key] });
        }
        query = query + updatequery.join(' and ');
        console.log(query);
        let result = await sqlHelper.sqlquery("L", query, sqlparams);

        return res.send({
            status: 200,
            data: result,
            message: "Success"
        });

    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        await sql.close();
    }
}

async function getdata(req, res) {
    try {

        var endpoint = req.query.root;
        var c = req.query.c ? req.query.c : "R";
        if (methods.FindRoot(req, res)) {
            return;
        }

        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }
        console.log(endpoint);
        // let skip = req.query.skip ? parseInt(req.query.skip) : 0,
        //     limit = req.query.limit ? parseInt(req.query.limit) : 10;

        let tablename = tblList[endpoint];
        if (tablename && tablename.toUpperCase().indexOf("NOLOCK") == -1) {
            tablename = tablename + " (NOLOCK) "
        }

        var query = 'SELECT  * FROM ' + tablename;// + ' (NOLOCK) ';


        if (req.query.cols && req.query.cols.length > 0) {
            query = 'SELECT  ' + req.query.cols.join() + ' FROM ' + tablename;// + ' (NOLOCK) ';
        }
        var whereCondition = [];
        let sqlparams = [];
        if (req.query.con) {
            query = query + ` WHERE `;
            for (var key in req.query.con) {
                console.log(req.query.con[key])
                // let json = JSON.parse(req.query.con[key]);
                let json = (req.query.con[key]);
                for (var obj in json) {
                    // whereCondition.push(`${obj} = '${json[obj]}'`);
                    var objparam = obj;
                    if (obj.indexOf('.') !== -1) {
                        var objparam = obj.replace('.', '')
                    }
                    //console.log(objparam);
                    if (obj.toLowerCase().indexOf('lastmessage') > -1 || obj.toLowerCase().indexOf('date') > -1 || obj.toLowerCase().indexOf('time') > -1) {
                        whereCondition.push(`CAST(${obj} AS DATE) = @${objparam}`);
                    }
                    else if (obj.toLowerCase().indexOf('likesearch') > -1) {
                        whereCondition.push(`${obj.substr(0, obj.indexOf('_'))} like @${objparam.substr(0, objparam.indexOf('_'))}`);
                    }
                    else {
                        whereCondition.push(`${obj} = @${objparam}`);
                    }
                    if (objparam.toLowerCase().indexOf('likesearch') > -1) {
                        sqlparams.push({ key: objparam.substr(0, objparam.indexOf('_')), value: '%' + json[obj] + '%' });

                    } else {
                        sqlparams.push({ key: objparam, value: json[obj] });
                    }
                }
            }
            query = query + whereCondition.join(' and ');
        }
        if (req.query.order) {
            if (!req.query.direction)
                query = query + " ORDER BY " + req.query.order + " DESC";
            else
                query = query + " ORDER BY " + req.query.order + " " + req.query.direction;
        } else {
            query = query + " ORDER BY 2 DESC";
        }
        // query =
        //     query +
        //     ` ORDER BY 1
        //     OFFSET ${skip} ROWS
        //     FETCH NEXT ${limit} ROWS ONLY`;

        console.log(query);
        //console.log(sqlparams);

        // let result = await sql.query(query);
        // await sql.close();
        let result = await sqlHelper.sqlquery(c, query, sqlparams);
        //console.log(result.recordsets);
        return res.send({
            status: 200,
            data: result.recordsets && result.recordsets.length > 0 && result.recordsets[0],
            message: "Success"
        });
    } catch (err) {
        console.log(err);
        return res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
    finally {
        //await sql.close();
    }
}


async function getdatasp(req, res) {
    try {
        console.log(req.query);
        var endpoint = req.query.root;
        var c = req.query.c ? req.query.c : "R";
        let params = req.query.params;
        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }
        var query = tblList[endpoint];
        let sqlparam = [];
        //const request = new sql.Request();
        let sqlparams = [];
        if (params) {
            for (var key in params) {
                // const obj = JSON.parse(params[key])
                let obj = params[key]
                for (var k in obj) {
                    //request.input(k, obj[k]);
                    sqlparam.push({
                        key: k,
                        value: obj[k]
                    });
                    sqlparams.push("@" + k);
                }
            }
        }
        console.log(sqlparam);
        let result = await sqlHelper.sqlProcedure(c, query, sqlparam);

        return res.send({
            status: 200,
            data: result.recordsets,
            message: "Success"
        });



    } catch (err) {
        console.log(err);
        return res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
    finally {
        //await sql.close();
    }
}



async function deleteRow(req, res) {
    try {
        console.log('---------');
        let endpoint = req.body && req.body.data && req.body.data.root || "";
        // var patharray = req.url.split("/");
        // var endpoint = patharray[patharray.length - 1];
        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        var query = 'DELETE FROM ' + tblList[endpoint];
        var deleteQuery = [];
        var sqlparams = [];
        if (req.body && req.body.data && req.body.data.query) {
            query = query + ` WHERE `;
            for (var key in req.body.data.query) {
                deleteQuery.push(`${key} = @${key}`)
                sqlparams.push({ key: key, value: req.body.data.query[key] })
            }
            query = query + deleteQuery.join(' and ');
        }
        console.log(query, '-------');
        let result = null;
        if (query.indexOf("WHERE") > -1) {
            result = await sqlHelper.sqlquery("L", query, sqlparams);
        }
        //let result = await sql.query(query);
        return res.send({
            status: 200,
            data: result,
            message: "Success"
        });
    } catch (err) {
        console.log(err);
        return res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
    finally {
        await sql.close();
    }
}

async function GetAgentLoginData(req, res) {
    try {

        var query = ""


        res.send({
            status: 200,
            data: (await sql.query`SELECT  * FROM alertmaster WHERE Id = ${Id}`)
                .recordsets[0][0],
            message: "Success"
        });
    } catch (err) {
        console.log(err);
        return res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
}


async function agentidletime(req, response) {
    try {

        var query = "[MTX].[GetAgentIdleTime]  " + req.query.u;

        let result = await sqlHelper.sqlquery("R", query);

        let idletime = 0;
        if (result && result.recordset) {
            result.recordset.forEach(element => {
                idletime = element.IdleTime;
            });
        }




        fs.readFile(path.join(appRoot, "client", "agentwidget.html")

            , null, function (error, data) {
                if (error) {
                    response.writeHead(404);
                    response.write('Whoops! File not found!');
                } else {
                    try {
                        data = data.toString();
                        data = data.replace("{data}", Math.round(idletime / 60));
                        data = data.replace("{u}", req.query.u);
                        response.write(data);

                    }
                    catch (e) {
                        response.write(e.toString());
                    }
                }
                response.end();
            });
    } catch (err) {
        console.log(err);
        response.write(err.toString());
        response.end();
    }
    finally {

    }
}
function secondsToHms(d) {
    d = Number(d);
    var h = Math.floor(d / 3600);
    var m = Math.floor(d % 3600 / 60);
    var s = Math.floor(d % 3600 % 60);

    var hDisplay = h > 0 ? h + (h == 1 ? ":" : ":") : "";
    var mDisplay = m > 0 ? m + (m == 1 ? ":" : ":") : "";
    var sDisplay = s > 0 ? s + (s == 1 ? "" : "") : "";
    return hDisplay + mDisplay + sDisplay;
}
async function agentstats(req, response) {
    try {

        var query = "[MTX].[AgentLoginTracker]";
        let sqlparams = [];
        sqlparams.push({ key: "UserId", value: req.query.u });
        //console.log(sqlparams);
        let result = await sqlHelper.sqlProcedure("R", query, sqlparams);
        let AgentStats = {};
        console.log(result.recordset);
        if (result && result.recordset) {
            result.recordset.forEach(element => {
                AgentStats = element;
            });
        }
        fs.readFile(path.join(appRoot, "client", "agentstats.html")

            , null, function (error, data) {
                if (error) {
                    response.writeHead(404);
                    response.write('Whoops! File not found!');
                } else {
                    try {
                        data = data.toString();
                        data = data.replace("{FLT}", moment(AgentStats.FIRSTLOGIN).utc().format("HH:mm:ss"));
                        data = data.replace("{talktime}", secondsToHms(AgentStats.TOTALTALKTIME));
                        data = data.replace("{totalbreaks}", AgentStats.SUM_DISPOSITIONMINS);
                        data = data.replace("{UniqueDailed}", AgentStats.UNIQUEDIAL);
                        data = data.replace("{MissedCallbacks}", AgentStats.MISSEDCALL);
                        data = data.replace("{APE}", AgentStats.APE);
                        data = data.replace("{BKGS}", AgentStats.BKGS);
                        data = data.replace("{IdleTime}", AgentStats.IDLETIME);
                        data = data.replace("{u}", req.query.u);
                        response.write(data);

                    }
                    catch (e) {
                        response.write(e.toString());
                    }
                }
                response.end();
            });
    } catch (err) {
        console.log(err);
        response.write(err.toString());
        response.end();
    }
    finally {

    }
}

async function login(req, res) {
    try {

        let inputdata = req.body;

        let query = "Select userid from CRM.UserDetails (NOLOCK) where EmployeeId = @EmployeeId and CAST(Password AS Varchar) = @Password";
        let sqlparams = [];
        sqlparams.push({ key: "EmployeeId", value: inputdata.EmpId });
        sqlparams.push({ key: "Password", value: inputdata.password });
        let result = await sqlHelper.sqlquery("R", query, sqlparams);

        if (result && result.recordset.length > 0) {
            res.cookie("userid", result.recordset[0].userid);
            res.cookie("AgentId", result.recordset[0].userid);
            res.redirect(req.headers.origin + "/admin/Users");
        } else {
            res.redirect(req.headers.origin + "?m=User Not found.");
        }
    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}
async function awsfile(req, res) {
    try {

        let key = req.query.key;
        let bucket = (req.query.bucket) ? req.query.bucket : 'newcctecbuckt'
        let result = await Utility.fetchAWSfiles(key, bucket);

        return res.send({
            status: 200,
            data: result
        });
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}

async function getawsfile(req, res) {
    try {

        let key = req.query.key;
        let bucket = req.query.bucket;
        let result = await Utility.getAWSfiles(key, bucket);

        return res.send({
            status: 200,
            data: result
        });
    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}

async function UploadIncentiveFile(req, res) {
    try {
        methods.UploadIncentiveFile(req, res);

    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function UploadVideoFile(req, res) {
    try {
        methods.UploadVideoFile(req, res);

    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function UploadStoryFile(req, res) {
    try {
        UploadStoryFiles.UploadStoryFile(req, res);

    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}
async function UploadChatAgentFile(req, res) {
    try {
        await UploadMatrixFiles.UploadChatAgentFile(req, res);

    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function HealthCheck(req, res) {
    try {
        methods.HealthCheck(req, res);
    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}


async function rejectLeads(req, res) {
    try {
        methods.uploadLeadRejectionFile(req, res);

    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function uploadUserGrades(req, res) {
    try {
        methods.uploadUserGradesFile(req, res);

    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function ProcessUploadIncentivefile(req, res) {
    try {
        IncFileUpload.ProcessUploadIncentivefile(req, res);

    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function UploadAllocationFile(req, res) {
    try {
        UploadMatrixFiles.UploadChatAgentFile(req, res);
    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}


async function AgentSurveyExcel(req, res) {
    try {
        let data = req.body;
        if (data.length > 0) {
            let response = await InsertSurvey({ data });

            return res.status(200).json({
                status: 200,
                error: 'Success'
            });
        } else {
            return res.status(404).json({
                status: 404,
                error: 'No data to insert'
            });
        }
    }
    catch (err) {
        console.log(err);
        return res.status(500).json({
            status: 500,
            error: err
        });

    }
    finally {

    }
}


async function InsertSurvey({ data }) {
    const results = [];
    try {
        for (let index = 0; index < data.length; index++) {
            let sqlparam = [];
            sqlparam.push({ key: "EmployeeId", value: data[index].EmployeeId });
            sqlparam.push({ key: "SurveyId", value: data[index].SurveyId });
            sqlparam.push({ key: "UserName", value: data[index].UserName });
            sqlparam.push({ key: "optionId", value: data[index].optionId });
            let result = await sqlHelper.sqlProcedure("L", "[PBT].[InsertSurveyAgentMapping]", sqlparam);
            results.push(result.recordset[0]);

        }
        return results;
    }
    catch (err) {
        console.log('err', err);
        return err;

    }
    finally {

    }
}



async function InsertQuiz({ data }) {
    const results = [];
    try {
        for (let index = 0; index < data.length; index++) {
            let sqlparam = [];
            sqlparam.push({ key: "EmployeeId", value: data[index].EmployeeId });
            sqlparam.push({ key: "QuizId", value: data[index].SurveyId });
            sqlparam.push({ key: "UserName", value: data[index].UserName });
            sqlparam.push({ key: "optionId", value: data[index].optionId });
            let result = await sqlHelper.sqlProcedure("L", "[PBT].[InsertQuizAgentMapping]", sqlparam);
            results.push(result.recordset[0]);

        }
        return results;
    }
    catch (err) {
        console.log('err', err);
        return err;

    }
    finally {

    }
}

async function InsertSurveyMapping(req, res) {
    try {
        var endpoint = req.query.root;
        var c = req.query.c ? req.query.c : "R";
        let params = req.query.params;
        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }
        var query = tblList[endpoint];
        let sqlparam = [];
        //const request = new sql.Request();
        let sqlparams = [];
        if (params) {
            for (var key in params) {
                const obj = JSON.parse(params[key])
                for (var k in obj) {
                    //request.input(k, obj[k]);
                    sqlparam.push({
                        key: k,
                        value: obj[k]
                    });
                    sqlparams.push("@" + k);
                }
            }
        }
        let result = await sqlHelperPbCroma.sqlProcedure(c, query, sqlparam);
        const response = await InsertSurvey({ data: result && result.recordset || [] });
        return res.status(200).json({
            status: 200,
            data: response
        })

    } catch (err) {
        console.log(err);
        return res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
    finally {
        //await sql.close();
    }
}


async function AgentQuizExcel(req, res) {
    try {
        let data = req.body;
        if (data.length > 0) {
            console.log('inside')
            let response = await InsertQuiz({ data });

            return res.status(200).json({
                status: 200,
                error: 'Success'
            });
        } else {
            return res.status(404).json({
                status: 404,
                error: 'No data to insert'
            });
        }
    }
    catch (err) {
        console.log(err);
        return res.status(500).json({
            status: 500,
            error: err
        });

    }
    finally {

    }
}


async function InsertQuizMapping(req, res) {
    try {
        var endpoint = req.query.root;
        var c = req.query.c ? req.query.c : "R";
        let params = req.query.params;
        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }
        var query = tblList[endpoint];
        let sqlparam = [];
        let sqlparams = [];
        if (params) {
            for (var key in params) {
                const obj = JSON.parse(params[key])
                for (var k in obj) {
                    //request.input(k, obj[k]);
                    sqlparam.push({
                        key: k,
                        value: obj[k]
                    });
                    sqlparams.push("@" + k);
                }
            }
        }
        let result = await sqlHelperPbCroma.sqlProcedure(c, query, sqlparam);
        const response = await InsertQuiz({ data: result && result.recordset || [] });
        return res.status(200).json({
            status: 200,
            data: response
        })

    } catch (err) {
        console.log(err);
        return res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
    finally {
        //await sql.close();
    }
}

async function GetAnswerQuizDetails(req, res) {
    try {
        methods.GetAnswerQuizDetails(req, res)

    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function VerifyRequestSource({
    ApplicationId,
    SourceApp,
    ClientKey
}) {

    try {
        let sqlParams = [];
        sqlParams.push({ key: 'ApplicationId', value: parseInt(ApplicationId) })
        sqlParams.push({ key: 'ApplicationName', value: SourceApp })
        sqlParams.push({ key: 'ClientKey', value: ClientKey });

        const query = `SELECT * from PBT.ApplicationMaster where ApplicationId = @ApplicationId
                     and ApplicationName = @ApplicationName
                     and ClientKey = @ClientKey and IsActive = 1`;

        let result = await sqlHelper.sqlquery("R", query, sqlParams);
        return result && result.recordset && result.recordset || [];

    } catch (err) {
        console.log('VerifyRequestSource', err);
        return [];
    }
}

async function QuizRedirection(req, res) {
    try {

        const { employeeid, clientkey, sourceapp, applicationid } = req.headers;

        const VerifySource = await VerifyRequestSource({
            ApplicationId: parseInt(applicationid),
            SourceApp: sourceapp,
            ClientKey: clientkey
        })

        if (VerifySource && VerifySource.length > 0) {

            let sqlParams = [];
            sqlParams.push({ key: 'ApplicationId', value: parseInt(applicationid) })
            sqlParams.push({ key: 'EmployeeId', value: employeeid })

            //let response = await sqlHelper.sqlProcedure("R", "[PBT].[CheckSurveyAgent]", sqlParams);

            //let result = response && response.recordset && response.recordset[0] || {};
            let result = "";
            let UserInfo = {
                "EmployeeId": employeeid,
                "SourceApp": sourceapp,
                "ApplicationId": applicationid,
                "ClientKey": clientkey
            };

            let Info = ""

            // if (result && result.Status ===200 && !result.IsComplete) {

            // UserInfo = { ...UserInfo, QuizMappingId: result.QuizMappingId || null }
            UserInfo = { ...UserInfo || null }
            Info = Base64Encode(JSON.stringify(UserInfo));

            //   if(result.Type=="Survey"){
            //     result["SurveyUrl"] = conf.LMS_BASE_URL + "client/PBSurvey?sid=" + result.SurveyId + "&info=" + Info;
            //   }
            //   if(result.Type=="Quiz"){
            //     result["SurveyUrl"] = conf.LMS_BASE_URL + "client/PBQuiz?qid=" + result.SurveyId + "&info=" + Info;
            //   }

            // }

            res.cookie(`PBLMSToken`, Info, { domain: '.policybazaar.com' });
            quizUrl = conf.LMS_BASE_URL + "client/QuizDashboard?info=" + Info;

            res.send({
                status: 200,
                message: 'success',
                data: quizUrl
            });
        } else {
            res.send({
                status: 401,
                message: 'Unauthorized Source'
            })
        }

    } catch (err) {
        return res.send({
            status: 500,
            message: err.toString(),
        });
    }

}

async function DownloadSurveyData(req, res) {
    try {
        methods.DownloadSurveyData(req, res)

    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function DownloadQuizData(req, res) {
    try {
        methods.DownloadQuizData(req, res)

    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}


async function QuizInsertResponsesData(req, res) {
    try {
        methods.QuizInsertResponsesData(req, res)

    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}


async function AddUpdateCourseContentData(req, res) {
    try {
        methods.PostCourseContentData(req, res)

    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {

    }
}


async function insertCourseData(req, res) {
    try {
        // let endpoint = req.body.data.root;
        console.log("Body of the request is ", req.body.data);
        let inputdata = req.body?.data?.formData;
        let endpoint = "CourseMaster";
        let scope = true;
        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        let query = 'INSERT INTO ' + tblList[endpoint] + '(';
        let columns = [];
        let VALUES = [];
        let sqlparams = [];
        // for (var key in inputdata) {
        //     columns.push(key);
        //     VALUES.push(inputdata[key]);
        // }
        for (let key in inputdata) {
            columns.push(`${key}`);
            VALUES.push(`@${key}`);
            sqlparams.push({ key: key, value: inputdata[key] });
        }
        query = query + columns.join() + ") VALUES ( ";
        query = query + VALUES.join() + ")";

        if (scope)
            query = query + "; SELECT SCOPE_IDENTITY()";
        
        let result = await sqlHelper.sqlquery("L", query, sqlparams);
        console.log(result);
        let e = JSON.stringify(result);
        let myArr = JSON.parse(e);

        let mappingData = req.body?.data?.mappingData || [];
      
        let courseid = result.recordset[0][""];

        if (typeof (myArr[0]) !== 'undefined') {
            if (typeof (myArr[0].error) !== 'undefined') {
                return res.send({
                    status: 500,
                    error: myArr[0].error
                });
            }
        } else {
            let query = 'INSERT INTO [PBT].[CourseCategoryProductMapping](CourseId, ProductId, CategoryId, IsActive) VALUES(@CourseId, @ProductId, @CategoryId, @IsActive)'
            let promises = [];

            for (let i = 0; i < mappingData.length; i++) {
                let ProductId = mappingData[i].ProductId;
                let CategoryId = mappingData[i].CategoryId;

                let sqlParams = [];
                sqlParams.push({ key: 'CourseId', value: courseid });
                sqlParams.push({ key: 'ProductId', value: ProductId });
                sqlParams.push({ key: 'CategoryId', value: CategoryId });
                sqlParams.push({key:'IsActive', value:1});

                promises.push(sqlHelper.sqlquery("L", query, sqlParams))
                // await sqlHelper.sqlquery("L",exec_query);
            }
            await Promise.allSettled(promises);

        }
        return res.send({
            status: 200,
            data: result,
            message: "Success"
        });
    } catch (err) {
        console.log(err);
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}

const GetSqlParams = ({ required, user, params }) => {
    try {
        const parameters = params && params.length > 0 && params[0] || {};
        // const obj = JSON.parse(parameters);
        const obj = parameters;
        let sqlParams = [];

        for (let itr = 0; itr < required.length; itr++) {
            const item = required[itr];
            const { element, from, userKey } = item;

            if (from != 'user') {
                if (!(element in obj)) {
                    return [];
                } else {
                    sqlParams.push({
                        key: element,
                        value: obj[element]
                    });
                }

            } else if (from == 'user' && userKey) {
                const info = user[userKey] || null;
                sqlParams.push({
                    key: element,
                    value: info
                });
            }
        }
        return sqlParams;
    } catch (err) {
        console.log('Inside GetSqlParams : ', err);
        return [];
    }
}

async function getdataspV2(req, res) {
    try {
        let endpoint = req.query.root;
        let c = req.query.c ? req.query.c : "R";
        let params = req.query.params;

        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }
        const query = tblList[endpoint].proc || "";
        const requiredParameters = tblList[endpoint].params || [];
        let sqlParams = GetSqlParams({ required: requiredParameters, user: req.user, params: params });

        console.log('List SP V2:: ', sqlParams);
        let result = await sqlHelper.sqlProcedure(c, query, sqlParams);

        return res.status(200).json({
            data: result?.recordsets || [],
            info: {},
        });

    } catch (err) {
        console.log('catch error', err);
        return res.status(400).json({
            status: 400,
            error: "Please enter correct inputs."
        });
    }
}

async function UpdateCourseData (req,res){
    try{
        let c =  req.body.data.c ? req.body.data.c : "R";
        let params= req.body.data.formData;
        let endpoint="UpdateCourseMaster";
        let query = tblList[endpoint];
        let sqlparam = [];
       
        if(params)
        {
            for(let k in params){
                
                sqlparam.push({
                    key:  k,
                    value: params[k]
                })
            }

        }
    
      
        let result=await sqlHelper.sqlProcedure(c, query, sqlparam);


        let params2=req.body.data.mappingData;
        let CourseId= params.CourseId;
        let endpoint2='InsertUpdateCategoryProductMapping';
        let query2 = tblList[endpoint2];
        let sqlparam2 = [];
        let promises = [];

        for(let i=0;i<params2.length;i++)
        {
        sqlparam2=[];
        sqlparam2.push({key:'CourseId', value:CourseId});
        sqlparam2.push({key:'ProductId', value: params2[i].ProductId});
        sqlparam2.push({key: 'CategoryId', value: params2[i].CategoryId});

        promises.push(sqlHelper.sqlProcedure('R', query2, sqlparam2))
      
        }
          
        await Promise.allSettled(promises);

        return res.send({
            status: 200,
            data: result,
            message: "Success"
        });
        
    }catch (err) {
        console.log(err);
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        
    }
}

async function PBSchoolQuizInsertResponsesData(req, res) {
    try {
        methods.PBSchoolQuizInsertResponsesData(req, res)

    } catch (err) {
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}



module.exports = {
    insert: insert,
    update: update,
    getdata: getdata,
    delete: deleteRow,
    getdatasp: getdatasp,
    getdataspV2: getdataspV2,
    agentidletime: agentidletime,
    agentstats: agentstats,
    awsfile: awsfile,
    login: login,
    UploadIncentiveFile: UploadIncentiveFile,
    UploadVideoFile: UploadVideoFile,
    UploadStoryFile: UploadStoryFile,
    UploadChatAgentFile: UploadChatAgentFile,
    HealthCheck: HealthCheck,
    getawsfile: getawsfile,
    rejectLeads: rejectLeads,
    uploadUserGrades: uploadUserGrades,
    ProcessUploadIncentivefile: ProcessUploadIncentivefile,
    UploadAllocationFile: UploadAllocationFile,
    AgentSurveyExcel: AgentSurveyExcel,
    InsertSurveyMapping: InsertSurveyMapping,
    InsertQuizMapping: InsertQuizMapping,
    AgentQuizExcel: AgentQuizExcel,
    GetAnswerQuizDetails: GetAnswerQuizDetails,
    QuizRedirection: QuizRedirection,
    DownloadSurveyData: DownloadSurveyData,
    DownloadQuizData: DownloadQuizData,
    QuizInsertResponsesData: QuizInsertResponsesData,
    AddUpdateCourseContentData: AddUpdateCourseContentData,
    insertCourseData: insertCourseData,
    UpdateCourseData:UpdateCourseData,
    PBSchoolQuizInsertResponsesData:PBSchoolQuizInsertResponsesData
};