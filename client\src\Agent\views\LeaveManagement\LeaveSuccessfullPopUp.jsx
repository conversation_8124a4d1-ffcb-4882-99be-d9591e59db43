import { React, useState, useEffect } from "react";

import {
    GetCommonData, InsertData, UpdateData, GetCommonspData, AllocationFormData
} from "../../store/actions/CommonAction";

import { connect } from "react-redux";
import { Modal, Box, Grid, FormControl, InputLabel, Select, MenuItem } from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import moment from 'moment';

const LeaveSuccessfullPopUp = (props) => {
    const { leaves, LeaveType, Userid, approvedDate, leaveName, approvalId, approvalName, shrinkage } = props
    return (
        <>
            <Modal
                open={props.open}
                // onClose={props.handleClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
            >
                <Box className="LeaveApplicationPopup">
                    <header>
                        <h2></h2>
                        <CloseIcon onClick={props.handleClose} className="closebtn" />

                    </header>

                    <Grid item md={12}>
                        <div className="LeaveAppliedSucess"><CheckCircleIcon />
                            <h3>
                                {LeaveType === 1 || !approvalName
                                    ? "Leave Applied Successfully"
                                    : `Pending for Approval at ${approvalName}`}
                            </h3>
                            <ul>
                                <li>Start Date</li>
                                <li>{moment(approvedDate).format('DD-MMM-YYYY')}</li>
                                {/* <li>End Date</li>
                                <li>{approvedDate}</li> */}
                                <li>Leave Type</li>
                                <li>{leaveName}</li>
                            </ul>
                            {shrinkage && <p style={{
                                fontSize: '16px',
                                fontWeight: '500',
                                color: '#333',
                                backgroundColor: '#f5f5f5',
                                padding: '10px 15px',
                                borderRadius: '8px',
                                margin: '10px 0'
                            }}>Before Shrinkage = {shrinkage.BeforeShrinkage} AND After Shrinkage = {shrinkage.Shrinkage}</p>}

                            <button onClick={props.handleClose}>DONE</button>
                        </div>
                    </Grid>

                </Box>

            </Modal>

        </>
    );

}

function mapStateToProps(state) {
    return {
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData
    }
)(LeaveSuccessfullPopUp);
