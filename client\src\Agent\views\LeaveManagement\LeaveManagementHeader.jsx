import React, { Fragment, useEffect } from "react";
import { useState } from "react";
import { styled } from '@mui/material/styles';
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import { connect } from "react-redux";
import moment from 'moment';


const Item = styled(Paper)(({ theme }) => ({
  backgroundColor: theme.palette.mode === 'dark' ? '#1A2027' : '#fff',
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: 'left',
  color: theme.palette.text.secondary,
}));


const LeaveManagementHeader = (props) => {

  let date = moment(new Date()).format("DD")
  let month = moment(new Date()).format("MMM")
  let year = moment(new Date()).format("YYYY")
  // let time = moment(new Date()).format("HH:mm")

  var time = new Date();
  console.log(
    time.toLocaleString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true })
  );

  return (
    <div className="LeaveMgtHeader">
      {/* <p className="pagetitle">DASHBOARD</p> */}
      {/* <h2>Leave Management System</h2> */}

      <Grid container className='inner-details'>
        <Grid xs={12} md={4} sm={3} className="logo">
          <img src="/Lms/vistalogo.svg" alt="Vista by matrix" />
        </Grid>

        <Grid xs={12} md={8} sm={9} className="innerContext">
          <Grid xs={6} sm={3} md={3}>
            <Item className="inner">
              <p>Process Name</p>
              {props.ProcessName}
          </Item>
          </Grid>
          <Grid xs={6} sm={2} md={2}>
            <Item className="inner"> <p>Employee Id</p>
            {props.UserType ? props.EmployeeId + ' (' + props.UserType + ')' : props.EmployeeId}</Item>
          </Grid>
          <Grid xs={6} sm={2 }md={2}>
            <Item className="inner"><p>TL Name</p>
            {props.ManagerId && props.ManagerName && props.ManagerName }</Item>
          </Grid>
          {/* <Grid xs={6} md={3}>
            <Item className="inner">
            <img src="/Lms/calendar.png" alt="calendar" /><p>TODAY</p>
            {date + "th " + month + ", " + year}
            </Item>
          </Grid> */}
          <Grid xs={6} sm={2} md={2}>
            <Item className="inner">
            <p>Approver1</p>
            {props.Approver1 && props.Approver1 && props.Approver1 }
            </Item>
          </Grid>
          <Grid xs={6}sm={2} md={2}>
            <Item className="inner">
              <p>Approver2</p>
              {props.Approver2 && props.Approver2 && props.Approver2 }
            </Item>
          </Grid>
        </Grid>
      </Grid>
      
      {/* <ul>
        <li>
          <p>Process Name</p>

          {props.ProcessName}
        </li>
        <li>
          
          <p>Employee Id</p>
          {props.UserType ? props.EmployeeId + ' (' + props.UserType + ')' : props.EmployeeId}
        </li>
        <li>
          
          <p>TL Name</p>
          {props.ManagerId && props.ManagerName && props.ManagerName }
        </li>
        <hr />
        <li> <img src="/Lms/calendar.png" /><p>TODAY</p>
         
          {date + "th " + month + ", " + year}
        </li>
      </ul> */}


      <Grid container>
        <Grid xs={12} md={12}>
          <div className="banner">
            <img src="/Lms/headerImg.png" alt = "headerImg"/>
            <div className="bannertext">
              <p>LEAVE MANAGEMENT
              </p>
              {/* <span>CLOSES IN: 00:47</span> */}
              {/* <button>APPLY NOW</button> */}
            </div>
          </div>
        </Grid>  
      </Grid>


    </div>

  );

}

function mapStateToProps(state) {
  return {
  };
}

export default connect(
  mapStateToProps,
  {

  }
)(LeaveManagementHeader);
