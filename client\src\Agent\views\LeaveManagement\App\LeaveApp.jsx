import React, { useState, useEffect } from 'react';
import LeaveRoutes from '../Routes/LeaveRoutes';

import { connect } from "react-redux";
import LeaveManagementHeader from '../LeaveManagementHeader';
import "../../../assets/scss/LeaveManagement.scss"
import { Container } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { getUrlParameter } from '../../../utility/utility.jsx';
import {
  getAdvisorData
} from "../../../store/actions/CommonAction";

const LeaveApp = (props) => {

  let token = localStorage.getItem('PBRMSToken');
  let emp = getUrlParameter('EmployeeId');
  let source = getUrlParameter('source');
  const [userid, setUserId] = useState();
  const [applicationId, setApplicationId] = useState();
  const [employeeId, setEmployeeId] = useState();
  const [processId, setProcessId] = useState();
  const [processName, setProcessName] = useState();
  const [userType, setUserType] = useState();
  const [managerId, setManagerId] = useState();
  const [managerName, setManagerName] = useState();
  const [stateId, setstateId] = useState();
  const [Approver1, setApprover1] = useState();
  const [Approver2, setApprover2] = useState();

  useEffect(() => {
    let { Userid, ApplicationId, EmployeeId, ProcessId, ProcessName, UserType, ManagerId, ManagerName, StateId, Approver1, Approver2 } = 0
    if (source == 'Approval' && emp != '') {
      // console.log('hello');
      getAdvisorData(function (results) {
        if (results && results.data && results.data.status == 200 && results.data.result?.length > 0) {
          const employee = results?.data?.result?.[0];
          ({ EmployeeId, ProcessName, UserType, ManagerId, ManagerName, Approver1, Approver2 } = employee || {});

          setUserId(Userid);
          setApplicationId(ApplicationId);
          setEmployeeId(EmployeeId);
          setProcessId(ProcessId);
          setProcessName(ProcessName);
          setUserType(UserType);
          setManagerId(ManagerId);
          setManagerName(ManagerName);
          setstateId(StateId);
          setApprover1(Approver1);
          setApprover2(Approver2);
        }

      })
    }
    else if (token) {
      let AgentInfo = atob(token);
      let data = {};
      if (AgentInfo) {
        data = JSON.parse(AgentInfo) || {};
        console.log(data)
      }
      ({ Userid, ApplicationId, EmployeeId, ProcessId, ProcessName, UserType, ManagerId, ManagerName, Approver1, Approver2 } = data);

      setUserId(Userid);
      setApplicationId(ApplicationId);
      setEmployeeId(EmployeeId);
      setProcessId(ProcessId);
      setProcessName(ProcessName);
      setUserType(UserType);
      setManagerId(ManagerId);
      setManagerName(ManagerName);
      setstateId(StateId);
      setApprover1(Approver1);
      setApprover2(Approver2);
    }
  }, [])

  // console.log(token)
  const navigate = useNavigate();
  const [applyActive, setApplyActive] = useState("active");
  const [leaveActive, setLeaveActive] = useState();
  const [deviceType, setDeviceType] = useState("");

  useEffect(() => {
    if (window.location.pathname.includes("LeaveStatus")) {
      // console.log("The URL contains LeaveStatus");
      setApplyActive('');
      setLeaveActive('active')
    }
    if (
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Windows Phone/i.test(
        navigator.userAgent
      )
    ) {
      setDeviceType("Mobile");
      // setWeekOffTitle('w/o')
    } else {
      setDeviceType("Desktop");
      // setWeekOffTitle(text.split(" "))
    }
  }, []);

  const applyLeave = () => {
    if (source == 'Approval' && emp != '') {
      navigate(`/agent/LeaveManagement?EmployeeId=${emp}&source=Approval`);
    }
    else{
      navigate(``);
    }
    setApplyActive('active');
    setLeaveActive('')
  }

  const leaveStatus = () => {
    if (source == 'Approval' && emp != '') {
      navigate(`LeaveStatus?EmployeeId=${emp}&source=Approval`);
    }
    else{
      navigate(`LeaveStatus`);
    }
    setApplyActive('');
    setLeaveActive('active')
  }

  return (
    <div className="LeaveManagementLayout">
      <Container maxWidth="lg">
        <LeaveManagementHeader EmployeeId={employeeId} ProcessName={processName} UserType={userType}
          ManagerId={managerId} ManagerName={managerName} Approver1 = {Approver1} Approver2 = {Approver2} />
        {/* <div className="MiddleLayout"> */}
        <ul className="TabButton">
          <li className={applyActive} onClick={applyLeave}>Apply Leave</li>
          <li className={leaveActive} onClick={leaveStatus}>Leave Status</li>
        </ul>
        <LeaveRoutes EmployeeId={employeeId} deviceType={deviceType} processId = {processId}/>
        {/* </div> */}
      </Container>
    </div>
  )
};

export default connect(null, {

})(LeaveApp);