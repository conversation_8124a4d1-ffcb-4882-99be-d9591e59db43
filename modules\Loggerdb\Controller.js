const axios = require('axios');
const config = require('../../env_config')


async function LogData(req, res) {

  try{
    let body = req.body;
    let data = await <PERSON><PERSON>(body);

    if(data.errorStatus !== undefined && data.errorStatus === 0) {
      res.status(200).json({
        status: 200,
        message: 'success',
        data: data.result
      })
    } else {
      res.status(500).json({
        status: 500,
        data: data.message
      })
    }
     
  } catch (err) {
    console.log(err);
    res.status(500).json({
      status: 500,
      data: err.message
    })
  }

}

async function Logger(data) {
  try{

    let errorStatus = 0;
    let LoggerDB = loggerdb;
    let result = await LoggerDB.collection('Log_Collection').insertOne(data);
    return { errorStatus, result };

  } catch (err) {

    let errorStatus = 1;
    console.log(err);
    return { errorStatus, message: err.message };

  }
}

module.exports = {
  LogData: Log<PERSON><PERSON>,
  Logger: <PERSON>gger
}

