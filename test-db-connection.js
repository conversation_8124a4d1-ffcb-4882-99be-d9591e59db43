const sql = require("mssql");
const { GetParsedConfigFromCache, GetParsedConfigLocal } = require('./modules/common/CommonMethods');

async function testDatabaseConnection() {
  try {
    console.log("Starting database connection test...");
    
    // Get configuration
    let SecretConfig = {};
    if (process.env.ENVIRONMENT_MTX_DASH === 'DEV_MTX_DASH') {
      SecretConfig = await GetParsedConfigLocal();
    } else {
      SecretConfig = await GetParsedConfigFromCache({ key: 'ENV_CONFIG' });
    }
    
    if (!SecretConfig) {
      console.error("Failed to load SecretConfig");
      return;
    }
    
    console.log("SecretConfig loaded successfully");
    
    // Test different database configurations
    const dbConfigs = [
      { name: "SQL_URL_RMS", config: SecretConfig.SQL_URL_RMS },
      { name: "RSQL_URL_RMS", config: SecretConfig.RSQL_URL_RMS },
      { name: "SQL_URL", config: SecretConfig.SQL_URL },
      { name: "RSQL_URL", config: SecretConfig.RSQL_URL }
    ];
    
    for (const dbTest of dbConfigs) {
      if (dbTest.config) {
        console.log(`\n--- Testing ${dbTest.name} ---`);
        console.log(`Server: ${dbTest.config.server}`);
        console.log(`Database: ${dbTest.config.database}`);
        console.log(`Port: ${dbTest.config.port || 1433}`);
        
        try {
          // Configure timeouts
          const testConfig = { ...dbTest.config };
          testConfig.connectionTimeout = 30000;
          testConfig.requestTimeout = 30000;
          testConfig.pool = {
            max: 10,
            min: 0,
            idleTimeoutMillis: 30000,
            acquireTimeoutMillis: 30000
          };
          
          const pool = new sql.ConnectionPool(testConfig);
          
          pool.on('error', err => {
            console.error(`Pool error for ${dbTest.name}:`, err.message);
          });
          
          console.log("Attempting to connect...");
          await pool.connect();
          console.log(`✅ Successfully connected to ${dbTest.name}`);
          
          // Test a simple query
          const result = await pool.request().query('SELECT 1 as test');
          console.log(`✅ Query test successful: ${JSON.stringify(result.recordset)}`);
          
          await pool.close();
          console.log(`✅ Connection closed for ${dbTest.name}`);
          
        } catch (error) {
          console.error(`❌ Failed to connect to ${dbTest.name}:`, error.message);
          if (error.code) {
            console.error(`Error Code: ${error.code}`);
          }
          if (error.originalError) {
            console.error(`Original Error: ${error.originalError.message}`);
          }
        }
      } else {
        console.log(`\n--- ${dbTest.name} not configured ---`);
      }
    }
    
  } catch (error) {
    console.error("Test failed:", error);
  }
}

// Run the test
testDatabaseConnection().then(() => {
  console.log("\nDatabase connection test completed");
  process.exit(0);
}).catch(err => {
  console.error("Test error:", err);
  process.exit(1);
});
