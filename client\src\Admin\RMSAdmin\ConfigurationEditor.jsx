import react, { useEffect, useState } from 'react';
import { connect } from "react-redux";
import { GetCommonData, InsertData, UpdateData, GetCommonspData, DeleteData } from "../store/actions/CommonAction";
import {
    Box,
    styled,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableRow
} from '@mui/material';

import { PopupContext } from './Context/ConfigContext';
import EditPopup from './Components/EditPopup';
import moment from 'moment';

const StyledTable = styled(Table)(({ theme }) => ({
    whiteSpace: "pre",
    "& thead": {
        "& tr": { "& th": { paddingLeft: 0, paddingRight: 0 } },
    },
    "& tbody": {
        "& tr": { "& td": { paddingLeft: 0, textTransform: "capitalize" } },
    },
}));

const ConfigurationEditor = (props) => {

    const [configurations, setConfigurations] = useState([]);
    const [open, setOpen]= useState(false);
    const [popupData,setPopupData]= useState({});
    const [keyName,setKeyName]= useState("");
    const [valueType, setValueType]= useState(['number','date','text','date-time']);

    useEffect(() => {
        props.GetCommonData({
            root: 'Configuration',
            c: 'R'
        }, (data) => {
            // console.log("The configurations are ", data);
            setConfigurations(data);
        })
    }, [])

    const handleOpenPopup=(e, data)=>{
        debugger
        setPopupData(data);
        setOpen(true);
    }

    const closePopup=()=>{
        setOpen(false);
    }

    const saveNewValue=(data, value)=>{
        // console.log("the date is ",value);
        if(data.valueType=='date')
        {
            value=moment(value.$d).format('YYYY-MM-DD');
           
        }
        let tempConfig= configurations;
        for(let i=0;i<tempConfig.length;i++)
        {
            if(tempConfig[i].KeyName==data.KeyName)
            {
                tempConfig[i].Value= String(value);
                break;
            }
        }
        props.UpdateData({
            root:'Configuration',
            c:'L',
            body:{'Value':String(value)},
            querydata:{'Id': data.Id}
        },()=>{
        })
        setConfigurations(tempConfig);
        setOpen(false);
    }

    // const handleChange=(e)=>{
    //         if(e.target.id=='key')
    //         {
    //             setKey(e.target.value);
    //         }
    //         else if(e.target.id="")
    // }


    return (
        <PopupContext.Provider value={{open:open, setPopup:closePopup, handleSave: saveNewValue}}>
        
        <Box width="100%" overflow="auto">
                         
            <StyledTable>
                <TableHead>
                    <TableRow sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                        {
                            configurations?.length > 0 &&
                            configurations.map((configuration) => {
                                return (
                                    <TableCell align='center' component="th" scope="row">{configuration.KeyName}</TableCell>
                                )
                            })
                        }

                    </TableRow>
                </TableHead>
                <TableBody>
                    <TableRow>
                        {
                            configurations.map((configuration) => {
                                return (
                                    <TableCell onClick={(e)=>handleOpenPopup(e,configuration)} component="th" scope="row">{configuration.Value}</TableCell>
                                )
                            })
                        }
                    </TableRow>
                </TableBody>
            </StyledTable>
        </Box>
       {open && <EditPopup data={popupData} name={popupData.KeyName} type={popupData.valueType} value={popupData.Value}></EditPopup>}
        {/* {open && <DateSelector/>} */}
        </PopupContext.Provider>
    )
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData,
        InsertData,
        UpdateData,
        DeleteData
    }
)(ConfigurationEditor);