import React, { useEffect, useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { Grid, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import { connect } from "react-redux";
import { GetCommonData, GetCommonspData } from '../../store/actions/CommonAction';
import TextField from '@mui/material/TextField';

const ApproveRequestPopup = (props) => {

    const { open, onClose, user, handleApprove, handleRemovalRequest } = props
    const [processMaster, setProcessMaster] = useState([]);
    const [ManagerEmployeeId, setManagerEmployeeId] = useState();
    const [processChange, setProcessChange] = useState(0);
    const [employeeIdExist, setEmployeeIdExist] = useState(true);

    const Approval = (TransferId, userId, status) => {
        handleApprove(TransferId, userId, status);
    }

    const handleEmployeeId = (e) => {
        if (e.target.value == "") {
            setEmployeeIdExist(true);
        }
        setManagerEmployeeId(e.target.value);
    }

    const handleChangeProcess = (e) => {
        setProcessChange(e.target.value);
    }

    useEffect(() => {
        props.GetCommonData({
            root: 'ProcessMaster',
            cols: ["ProcessId", "ProcessName", "ProductId", "ProductName"],
            order: ["ProcessName"],
            direction: ['ASC'],
            con: [{ "IsActive": 1 }],
            c: "R",
        }, function (data) {
            console.log(data);
            setProcessMaster(data);
        })
    }, [])

    const checkTLIdAndApprove = () => {
        props.GetCommonspData({
            root: 'CheckTLEmployeeId',
            params: [{ EmployeeId: ManagerEmployeeId }],
            c: "R",
        }, function (result) {
            console.log(result.data.data[0][0].Status);
            setEmployeeIdExist(result.data.data[0][0].Status);
            if (result.data.data[0][0].Status && processChange > 0) {
                handleRemovalRequest(user.TransferId, user.UserId, 2, processChange, ManagerEmployeeId);
            }
        })
    }

    const handleResignedRequest = () => {
        handleRemovalRequest(user.TransferId, user.UserId, 2, 0, 0);
    }


    return (
        <>
            {open && user &&
                <Dialog open={open} onClose={onClose} maxWidth="xs" className="ApproveRequestPopup">
                    <DialogTitle>
                        {"Approve Request"}
                        <IconButton
                            aria-label="close"
                            onClick={onClose}
                            sx={{ position: 'absolute', right: 8, top: 8, color: (theme) => theme.palette.grey[500] }}
                        >
                            <CloseIcon />
                        </IconButton>
                    </DialogTitle>
                    <DialogContent>


                        <Grid container>
                            <Grid item md={12} sm={12} xs={12}>
                                <p>You're about to accept a request, this is a non-revertible action</p>
                                <div className="ApproveRequestDetails">
                                    <Grid container spacing={2}>
                                        <Grid item md={6}>Request type</Grid>
                                        <Grid item md={6}><span>{user.RequestName}</span></Grid>
                                        <Grid item md={6}>TL name</Grid>
                                        <Grid item md={6}><span>{user.ManagerName}</span></Grid>
                                        <Grid item md={6}>Request E-Code</Grid>
                                        <Grid item md={6}><span>{user.EmployeeId}</span></Grid>
                                        {/* <Grid item md={6}>Usergroup</Grid>
                                        <Grid item md={6}><span>MARVEL</span></Grid> */}
                                        <Grid item md={6}>Current process</Grid>
                                        <Grid item md={6}><span>{user.ProcessName}</span></Grid>

                                        {user.RequestType != 2 && <><Grid item md={6}>Move to process</Grid><Grid item md={6}><span>{user.TransferProcessTo}</span></Grid></>}
                                        {user.RequestType == 2 && <><Grid item md={6}>Move to process</Grid><Grid item md={6}><FormControl fullWidth>
                                            <InputLabel id="demo-simple-select-label">Process</InputLabel>
                                            <Select
                                                labelId="demo-simple-select-label"
                                                id="demo-simple-select"
                                                // value={}
                                                label="Process"
                                                onChange={handleChangeProcess}
                                            >
                                                {processMaster.map((process) => {
                                                    return (
                                                        <MenuItem value={process.ProcessId}>{process.ProcessName}</MenuItem>
                                                    );
                                                })}
                                                {/* <MenuItem value={10}>Ten</MenuItem> */}
                                            </Select>
                                        </FormControl></Grid><Grid item md={6}>Move to TL</Grid><Grid item md={6}><TextField id="outlined-basic" label="EmployeeId" variant="outlined" onChange={handleEmployeeId} /></Grid></>}
                                        {!employeeIdExist && <p>EmployeeId Wrong Please Check </p>}
                                    </Grid>
                                </div>
                            </Grid>

                            <Grid item md={12} sm={12} xs={12} className="alignCenter">
                                {user.RequestType != 2 ? <button className="Approvebutton" value='2' onClick={() => Approval(user.TransferId, user.UserId, 2)} >Approve</button>
                                    :
                                    <><button className="Approvebutton" value='2' onClick={checkTLIdAndApprove}>Approve</button>
                                        <button className="Approvebutton" value='2' onClick={handleResignedRequest}>Resigned</button></>}
                            </Grid>
                        </Grid>

                    </DialogContent>

                </Dialog>
            }
        </>
    );
}

// export default ApproveRequestPopup;

export default connect(
    null,
    {
        GetCommonData,
        GetCommonspData
    }
)(ApproveRequestPopup);

