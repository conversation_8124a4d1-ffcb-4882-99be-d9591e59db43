import moment from 'moment';
import { ALL_SUPERVISORS } from './Constants';

export const GetAgentAttendanceOnDate = (data = [], date) => {

  for (let index = 0; index < data.length; index++) {
    const element = data[index];
    let attendanceDate = moment(element?.AttendanceDate).format('YYYY-MM-DD');
    if (date === attendanceDate && element.IsPresent == 1) {
      return true;
    }
    else if(date === attendanceDate && element.IsPresent == 0){
      return false;
    }
    // else{
    //   return true;
    // }

  }
  return null;
}

export const GetAllManagerIds = (data) => {
  let result ="";
  for (let index = 0; index < data.length; index++) {
    const element = data[index];
    if(element?.value && element?.value !== ALL_SUPERVISORS) {
      result += element?.value?.toString();
      if(index < (data.length - 1)) result += ",";
    }
  }
  return result;
}


export const GetAgentEligibleOnDate = (data = [], date) => {
  let EligibleDates = []
  for (let index = 0; index < data.length; index++) {
    const element = data[index];
    EligibleDates.push(moment(element?.AvailableDates).format('YYYY-MM-DD'));
  }
  if(EligibleDates.indexOf(date)>-1){
    return true;
  }
  else{
    return false;
  }
}
