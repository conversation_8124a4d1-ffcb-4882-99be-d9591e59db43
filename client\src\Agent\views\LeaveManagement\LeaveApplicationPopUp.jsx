import { React, useState, useEffect } from "react";

import {
    GetCommonData, GetCommonspData, GetCommonspDataV2,
    validationPlannedLeave
} from "../../store/actions/CommonAction";

import { connect } from "react-redux";
import { Modal, Box, Grid, FormControl, InputLabel, Select, MenuItem } from "@mui/material";
import TextSnippetOutlinedIcon from '@mui/icons-material/TextSnippetOutlined';
import CloseIcon from '@mui/icons-material/Close';
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import moment from 'moment';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import ThumbDownIcon from '@mui/icons-material/ThumbDown';
import ConfirmationPopUp from "./ConfirmationPopUp";
import { getUrlParameter } from '../../utility/utility.jsx';

const LeaveApplicationPopUp = (props) => {
    const { leaves, rosterData, GetLeaves, EmployeeId, selectedDate, disabledDates, processId,
        lastMonthFirstDate, lastMontLastDate } = props

    const [firstDate, setFirstDate] = useState();
    const [LeaveType, setLeaveType] = useState();
    const [leaveDate, setLeaveDate] = useState(null);
    const [confirmPopUp, setConfirmPopUp] = useState(false);
    const [months, setMonths] = useState([]);
    const [openPopUp, setOpenPopUp] = useState(false);
    const [openErrorPopUp, setOpenErrorPopUp] = useState(false);
    const [ErrorMessage, setErrorMessage] = useState('');
    const [shrinkage, setShrinkage] = useState('');
    const [shrinkageColor, setShrinkageColor] = useState('');
    const [showButton, setShowButton] = useState(true);

    // Trigger checkdatesfunction when selectedDate or LeaveType changes
    useEffect(()=>{
        setShowButton(true);
        setErrorMessage("");
    },[props.open])

    useEffect(() => {
        if (leaveDate && LeaveType) {
            checkdatesfunction();
        }
    }, [leaveDate, LeaveType])

    // Calculate and Set Month Display
    const getMonth = (dates) => {
        if (!leaveDate) return;

        setMonths([]);

        // Check if leaveDate is within the roster range or LeaveType is 1
        const isInRosterRange = moment(leaveDate).isBetween(
            moment(rosterData[0].StartDate),
            moment(rosterData[0].EndDate),
            null,
            '[]'
        ) || LeaveType == 1;

        let baseDate = isInRosterRange ? moment(rosterData[0].StartDate) : moment(leaveDate);

        // Prevent updating baseDate if leaveDate is within the 14-day range of `dates`
        if (moment(leaveDate).isBetween(moment(dates), moment(dates).clone().add(13, 'days'), null, '[]')) {
            baseDate = moment(dates); // Keep `dates` as baseDate if leaveDate is within range
        } else {
            baseDate = baseDate.startOf('isoWeek'); // Move to the nearest Monday
        }

        // Calculate and update month display
        const first = baseDate.format('MMM');
        const second = baseDate.clone().add(13, 'days').format('MMM');

        setMonths(first !== second ? [`${first} / ${second}`] : [first]);
    };


    // Set Leave Type Based on Availability
    useEffect(() => {
        if (!selectedDate) return;

        setLeaveDate(selectedDate);
        getShrinkage(selectedDate);

        if (
            selectedDate >= moment(rosterData[0].StartDate).format('YYYY-MM-DD') &&
            selectedDate <= moment(rosterData[0].EndDate).format('YYYY-MM-DD') &&
            rosterData[0].Enabled
        ) {
            if (leaves?.length) {
                for (let i = 0; i < leaves.length; i++) {
                    if (leaves[i].LeaveCount >= 1) {
                        setLeaveType(leaves[i].Id);
                        break;
                    }
                }
            }
        } else {
            if (leaves?.length) {
                for (let i = 0; i < leaves.length; i++) {
                    if (leaves[i].LeaveCount >= 1 && leaves[i].Id != 1) {
                        setLeaveType(leaves[i].Id);
                        break;
                    }
                }
            }
        }
    }, [selectedDate]);  // Only depends on selectedDate

    // Determine Available Dates for Leave
    const checkdatesfunction = () => {
        if (selectedDate && leaves && leaves.length > 0) {
            let newFirstDate = firstDate;

            if (LeaveType == 1 && rosterData[0].Enabled) {
                let rosterStartDate = moment(rosterData[0].StartDate).startOf('isoWeek');
                let rosterEndDate = moment(rosterData[0].EndDate);

                if (!firstDate || !(moment(firstDate).isBetween(rosterStartDate, rosterEndDate, null, '[]'))) {
                    newFirstDate = rosterStartDate.format('YYYY-MM-DD');
                    setFirstDate(newFirstDate);
                }
                // if (leaveDate >= rosterStartDate.format('YYYY-MM-DD') && leaveDate <= rosterEndDate.format('YYYY-MM-DD')) {
                //     setLeaveDate(selectedDate);
                // }
            } else {
                let leaveStartDate = moment(leaveDate || selectedDate).startOf('isoWeek');

                // if (!firstDate || !(moment(firstDate).isBetween(leaveStartDate, leaveStartDate.clone().add(13, 'days'), null, '[]'))) {
                if (!firstDate || !(moment(leaveStartDate).isBetween(moment(firstDate), moment(firstDate).clone().add(13, 'days'), null, '[]'))) {

                    newFirstDate = leaveStartDate.format('YYYY-MM-DD');
                    setFirstDate(newFirstDate);
                }
            }
            getMonth(newFirstDate);
        }
    };

    // Handle Leave Type Selection Change
    const handleChange = (event) => {
        setLeaveType(event.target.value);
        setShowButton(true);
        setErrorMessage('');
    };

    // Update Calendar to Show Next or Previous Weeks
    const updateWeek = (direction) => {
        // console.log(direction === -1 ? "beforeWeek" : "afterWeek");
        const newDate = moment(firstDate).add(direction * 14, 'days');
        setFirstDate(newDate);

        // Update month display
        const first = newDate.format('MMM');
        const second = moment(newDate).add(14, 'days').format('MMM');
        setMonths(first !== second ? [`${first} / ${second}`] : [first]);

        if (LeaveType !== 1) {
            checkAvailiblity(newDate);
        }
    }

    const getShrinkage = (date) => {
        if (getUrlParameter('source') == 'Approval') {
            props.GetCommonspData({
                root: 'GetShrinkageOnApplicationDate',
                c: "R",
                params: [{
                    EmployeeId: getUrlParameter('EmployeeId'),
                    ApplicationDate: date
                }],
            }, function (result) {
                setShrinkage(result.data.data[0][0]);
                // setShrinkageColor(result.data.data[0][0].Color);
            })
        }
    }

    const leaveAppliedDate = (date) => {
        // setLeaveDate(selectedDate);
        setShowButton(true);
        setLeaveDate(date);
        setOpenErrorPopUp(false);

        if(LeaveType != 1){
            getShrinkage(date);
        }
        // ---------
        // if (lastMonthFirstDate >= date || lastMontLastDate <= date) {
        //     setErrorMessage("Please Select Valid Date");
        //     setOpenErrorPopUp(true)
        //     return;
        // }
        // ---------

        for (let i = 0; i < disabledDates.length; i++) {
            if (LeaveType == 1 && moment(disabledDates[i].MonthlyDate).format('YYYY-MM-DD') == date) {
                // if (disabledDates[i].IsRoster == 1 && moment(disabledDates[i].LeaveTakenDate).format('YYYY-MM-DD') == date) {
                //     setErrorMessage(disabledDates[i].Message);
                //     setOpenErrorPopUp(true);
                //     return;

                // }
                // else
                if (disabledDates[i].DisabledDates == 1 && disabledDates[i].SlotFull > 0) {
                    setErrorMessage(disabledDates[i].Message);
                    setOpenErrorPopUp(true);
                    return;
                }
                else if (disabledDates[i].SlotFull <= 0) {
                    setErrorMessage(disabledDates[i].Message);
                    setLeaveDate(date);
                    setOpenErrorPopUp(true);
                    return;
                }
            }

            // ----------------
            if (LeaveType != 1 && moment(disabledDates[i].MonthlyDate).format('YYYY-MM-DD') == date) {
                if (moment(disabledDates[i].LeaveTakenDate).format('YYYY-MM-DD') == date) {
                    setErrorMessage(disabledDates[i].Message);
                    // setLeaveName(leave_type)
                    setOpenErrorPopUp(true);
                    return;
                }
                //     else if (disabledDates[i].SlotFull <= 0 && disabledDates[i].LeaveCategoryId == 2) {
                //         setErrorMessage(disabledDates[i].Message)
                //         setLeaveDate(date);
                //         // setLeaveName(leave_type)
                //         setOpenErrorPopUp(true);
                //         return;
                //     }
            }
            // ----------------
        }
        // setLeaveDate(date);
    }

    const disabledDatesCalendar = (checkDate) => {
        const today = moment();
        let minAllowedDate = '';

        // Calculate 21st of 2 months ago
        const twoMonthsBack21 = today.clone().subtract(2, 'months').date(21);
        const aprilFirst = moment('2025-04-01');

        if (today.isAfter(aprilFirst)) {
            // After April 1st
            for (let i = 0; i < leaves.length; i++) {
                if (LeaveType === leaves[i].Id) {
                    if ([4, 5].includes(leaves[i].LeaveCategoryId)) {
                        // For LeaveCategoryId 4,5 — go 2 months back to 21st
                       return twoMonthsBack21.format('YYYY-MM-DD');
                    } else {
                        // For others — min allowed is April 1st OR 2 months back, whichever is later
                        return moment.max(aprilFirst, twoMonthsBack21).format('YYYY-MM-DD');
                    }
                    break; // stop once matched
                }
            }
        } else {
            // Before or on April 1st — allow up to 2 months back (21st)
            return twoMonthsBack21.format('YYYY-MM-DD');
        }
    }

    const Availiblity = (checkDate) => {
        const maxAllowedDate = moment().add(44, 'days').format('YYYY-MM-DD'); // 45 days ahead
        let minAllowedDate;
        if(LeaveType !=1 ){
            minAllowedDate = disabledDatesCalendar(checkDate);
        }

        if (LeaveType !=1  && moment(checkDate).isBefore(minAllowedDate) || moment(checkDate).isAfter(maxAllowedDate)) {
            return "rgb(194 183 183 / 37%)"; // Disable dates beyond the allowed range
        }

        for (let i = 0; i < disabledDates.length; i++) {
            if (LeaveType == 1 && moment(disabledDates[i].MonthlyDate).format('YYYY-MM-DD') == checkDate) {
                if (disabledDates[i].forceApply == 1) {
                    return 2; // Force apply case
                }
                if (disabledDates[i].IsRoster == 1 && moment(disabledDates[i].LeaveTakenDate).format('YYYY-MM-DD') == checkDate && disabledDates[i].LeaveType == 1) {
                    return disabledDates[i].Color; // Cant select these dates
                }
                else if (disabledDates[i].DisabledDates == 1 && disabledDates[i].SlotFull > 0) {
                    return "rgb(194 183 183 / 37%)"; // Slot full
                }
                else if (disabledDates[i].SlotFull <= 0) {
                    return 1; // Slot available
                }
                else if (leaveDate == checkDate) {
                    return 2; // Specific leave date selected
                }
            }

            // ---------
            // Check for LeaveType other than 1
            if (LeaveType != 1 && moment(disabledDates[i].MonthlyDate).format('YYYY-MM-DD') == checkDate) {
                // if (disabledDates[i].IsRoster == 1 && moment(disabledDates[i].LeaveTakenDate).format('YYYY-MM-DD') == checkDate) {
                //     return disabledDates[i].Color;
                // }
                // disabledDates[i].IsRoster == 0 && 
                if (moment(disabledDates[i].LeaveTakenDate).format('YYYY-MM-DD') == checkDate) {
                    return disabledDates[i].Color; // Leave taken for non-roster
                }
                else if (leaveDate == checkDate) {
                    return 2; // Specific leave date selected
                }
            }
            // ---------

            if (LeaveType != 1 && moment(disabledDates[i].MonthlyDate).format('YYYY-MM-DD') == checkDate) {
                if (leaveDate == checkDate) {
                    return 2;
                }
            }
        }

    }

    // Check Availability for Selected Date
    const checkAvailiblity = (checkDate) => {
        let avail = Availiblity(moment(checkDate).format('YYYY-MM-DD'))

        if (avail == 1) return { cssClass: "noSlotAvailable white", Color: '' }

        else if (avail == 2) return { cssClass: "WeeklyOff white", Color: '#397444' }

        else if (avail) return { cssClass: "white", Color: avail }

        return { cssClass: "", Color: '' }
    }

    // Function to Apply Leave
    const applyLeave = () => {
        if (LeaveType == 1) {
            for (let i = 0; i < disabledDates.length; i++) {
                //LeaveType == 1 &&
                if (moment(disabledDates[i].MonthlyDate).format('YYYY-MM-DD') == moment(leaveDate).format('YYYY-MM-DD')) {
                    // if (disabledDates[i].IsRoster == 1 && moment(disabledDates[i].LeaveTakenDate).format('YYYY-MM-DD') == leaveDate) {
                    //     return;
                    // }
                    // else 
                    if (disabledDates[i].DisabledDates == 1 && disabledDates[i].SlotFull > 0) {
                        return;
                    }
                    else if (disabledDates[i].SlotFull <= 0) {
                        return;
                    }
                }
            }
        }

        if (LeaveType > 1) {
            // Check if leave already applied for this date and leave type
            let leaveAlreadyApplied = false;
            for (let i = 0; i < disabledDates.length; i++) {
                if (
                    moment(disabledDates[i].MonthlyDate).format('YYYY-MM-DD') == moment(leaveDate).format('YYYY-MM-DD') &&
                    disabledDates[i].LeaveTakenDate &&
                    moment(disabledDates[i].LeaveTakenDate).format('YYYY-MM-DD') == moment(leaveDate).format('YYYY-MM-DD')
                ) {
                    leaveAlreadyApplied = true;
                    break;
                }
            }
            if (leaveAlreadyApplied) {
                setErrorMessage('Leave already applied for this date.');
                setOpenErrorPopUp(true);
                setShowButton(false);
                return;
            }
            let leave_type;
            let leaveCategoryId;
            for (let i = 0; i < leaves.length; i++) {
                if (leaves[i].Id == LeaveType) {
                    leave_type = leaves[i].LeaveType;
                    leaveCategoryId = leaves[i].LeaveCategoryId;
                }
            }
            let body = {
                leaveType: LeaveType, leaveDate: leaveDate,
                leaveCategoryId: leaveCategoryId,
                processId: processId
            }
            if (getUrlParameter('source') == 'Approval') {
                setConfirmPopUp(true);
            }
            else {
                validationPlannedLeave(body, function (result) {
                    // console.log(result);
                    // props.handleClose();
                    if (result && result.data && result.data.status == 200) {
                        let item = result.data.result?.APIResponse;
                        // let ErrorCode = result.data.results[0]?.ErrorCode;
                        let Message = result.data.result?.ResponseMsg;
                        if (item) {
                            setConfirmPopUp(true);
                            setShowButton(true);
                        }
                        else {
                            setErrorMessage(Message);
                            setOpenErrorPopUp(true);
                            setShowButton(false);
                        }
                    }
                });
            }
        }
        else{
            if (leaveDate) {
                setConfirmPopUp(true);
            }    
        }
        // if (leaveDate) {
        //     setOpenPopUp(true);
        // }
    }

    // Close Confirmation PopUp
    const handleCloseConfirmPopUp = () => {
        setConfirmPopUp(false);
        // setOpenPopUp(false);
    }

    // Close Error PopUp
    const handleCloseErrorPopUp = () => {
        setOpenErrorPopUp(false);
    }

    return (
        <>
            {leaves && leaves?.length > 0 && firstDate && <Modal
                open={props.open}
                onClose={props.handleClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
            >
                <Box className="LeaveApplicationPopup">
                    <header>
                        <h2><TextSnippetOutlinedIcon /> Leave Application</h2>
                        <button>{EmployeeId}</button>
                        <CloseIcon onClick={props.handleClose} className="closebtn" />

                    </header>
                    <Grid item md={12}>
                        <label>Type of Leave</label>
                        {Array.isArray(leaves) && leaves.length > 0 && <Select
                            labelId="TypeofLeave"
                            id="TypeofLeave"
                            value={LeaveType}
                            // label="Weekly Off"
                            onChange={handleChange}
                            className="TypeOfLeaveSeclect"
                        >
                            {leaves?.length > 0 &&
                                leaves.map((leave) =>
                                    leave?.LeaveCount >= 1 || [4, 5].includes(leave.LeaveCategoryId) ? (
                                        <MenuItem key={leave.Id} value={leave.Id}>
                                            <p style={{ background: [4, 5].includes(leave.LeaveCategoryId) || leave.LeaveCount >= 1 ? leave.Color : "transparent" }}>
                                                {leave.LeaveCategoryId == 1 && leave.LeaveCount >= 1
                                                    ? leave.LeaveCount
                                                    : leave.LeaveCount > 0 || [4, 5].includes(leave.LeaveCategoryId)
                                                        ? <ThumbUpIcon />
                                                        : <ThumbDownIcon />}
                                            </p>
                                            {leave.LeaveType}
                                        </MenuItem>
                                    ) : null
                                )}
                        </Select>}
                    </Grid>

                    <Grid item md={12}>
                        <div className="calendarheader">
                            <ul className="monthView">

                                <li>Month -
                                    {months && months.length > 0 && months.map((month) => (
                                        month
                                    ))}
                                </li>
                            </ul>
                            <p></p>

                            <p className="arrow">
                                <span></span> No Slots Available
                                {LeaveType != 1 && <><NavigateBeforeIcon onClick={() => updateWeek(-1)} />
                                    <NavigateNextIcon onClick={() => updateWeek(1)} />
                                </>}
                            </p>
                        </div>
                        <hr />
                        <ul class="weekdays">
                            <li>Mon</li>
                            <li>Tue</li>
                            <li>Wed</li>
                            <li>Thu</li>
                            <li>Fri</li>
                            <li>Sat</li>
                            <li>Sun</li>
                        </ul>

                        {<ul class="days">
                            {Array.from({ length: 7 }, (_, index) => (
                                <li className={checkAvailiblity(moment(firstDate).add(index, 'days').format('YYYY-MM-DD')).cssClass}
                                    style={{ background: checkAvailiblity(moment(firstDate).add(index, 'days').format('YYYY-MM-DD')).Color }}
                                    onClick={() => leaveAppliedDate(moment(firstDate).add(index, 'days').format("YYYY-MM-DD"))}
                                >
                                    {moment(firstDate).add(index, 'days').format("DD")}
                                    {LeaveType !=1 && shrinkage && leaveDate == moment(firstDate).add(index, 'days').format('YYYY-MM-DD') ? <span style={{color : shrinkage.ShrinkageColor}}>{shrinkage.Shrinkage}</span> : <span></span>}
                                </li>
                            ))}

                        </ul>}
                        {<ul class="days">
                            {Array.from({ length: 7 }, (_, index) => (
                                <li className={checkAvailiblity(moment(firstDate).add(index + 7, 'days').format('YYYY-MM-DD')).cssClass}
                                    style={{ background: checkAvailiblity(moment(firstDate).add(index + 7, 'days').format('YYYY-MM-DD')).Color }}
                                    onClick={() => leaveAppliedDate(moment(firstDate).add(index + 7, 'days').format("YYYY-MM-DD"))}
                                >
                                    {moment(firstDate).add(index + 7, 'days').format("DD")}
                                    {/* <span>23</span> */}
                                    {LeaveType !=1 && shrinkage && leaveDate == moment(firstDate).add(index + 7, 'days').format('YYYY-MM-DD') ? <span style={{color : shrinkage.ShrinkageColor}}>{shrinkage.Shrinkage}</span> : <span></span>}

                                </li>
                            ))}
                        </ul>}
                        {openErrorPopUp &&
                            <div className="LeaveAppliedSucess">
                                <li className="noSlot">{ErrorMessage}</li>
                            </div>
                        }

                        <div className="text-center">
                            {showButton && <button className="ApplyLeaveBtn" onClick={applyLeave}>APPLY</button>}
                        </div>
                    </Grid>
                </Box>

            </Modal>}

            {<ConfirmationPopUp open={confirmPopUp} leaves={leaves} LeaveType={LeaveType} leaveDate={leaveDate} disabledDates={disabledDates}
                processId={processId}
                rosterData={rosterData}
                handleCloseConfirmPopUp={handleCloseConfirmPopUp}
                handleClose={() => {
                    setConfirmPopUp(false);
                    props.handleClose();
                }}
                shrinkage = {shrinkage}
            ></ConfirmationPopUp>}


            {/* <Modal
                open={openErrorPopUp}
                onClose={handleCloseErrorPopUp}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
            >
                <Box className="LeaveApplicationPopup">
                    <header className="alignRight">
                        <CloseIcon onClick={handleCloseErrorPopUp} className="closebtn" />

                    </header>

                    <Grid item md={12}>
                        <div className="LeaveAppliedSucess">
                            <li className="noSlot">{ErrorMessage}</li>
                        </div>
                    </Grid>

                </Box>

            </Modal> */}

        </>
    );

}

function mapStateToProps(state) {
    return {
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData,
        GetCommonspDataV2
    }
)(LeaveApplicationPopUp);
