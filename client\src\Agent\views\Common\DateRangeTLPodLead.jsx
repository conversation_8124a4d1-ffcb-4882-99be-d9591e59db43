
import React from "react";
import { Form } from 'react-bootstrap';

// reactstrap components
import moment from 'moment';

import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css'


import { Row, Col } from 'react-bootstrap';



class DateRange extends React.Component {
    constructor(props) {
        super(props);
        this.handleStartDateChange = this.handleStartDateChange.bind(this);
        this.handleEndDateChange = this.handleEndDateChange.bind(this);

        this.state = {
            startdate: props.startDate? props.startDate : moment().subtract(30, 'days').format("YYYY-MM-DD"),
            enddate: props.endDate? props.endDate: moment().format("YYYY-MM-DD"),
        }
    }
    componentDidMount() {

    }
    componentWillReceiveProps(props,nextProps) {debugger;
        if(props.startDate != this.state.startdate){
            this.setState({ startdate : props.startDate? props.startDate : moment().subtract(30, 'days').format("YYYY-MM-DD"),
            enddate : props.endDate ? props.endDate :  moment().format("YYYY-MM-DD")}
           );
        }

    }

    handleStartDateChange = (e, props) => {debugger;
        if (e._isAMomentObject) {
            this.props.onStartDate(e.format("YYYY-MM-DD"));
            this.props.onEndDate(this.props.range?e.add(this.props.range, 'days').format("YYYY-MM-DD"):e.add(30, 'days').format("YYYY-MM-DD") );

            this.setState({ startdate: e.format("YYYY-MM-DD"), enddate: this.props.range?e.add(this.props.range, 'days').format("YYYY-MM-DD"):e.add(30, 'days').format("YYYY-MM-DD") }, function () {
            });
        }
    }

    handleEndDateChange = (e, props) => {
        if (e._isAMomentObject) {
            this.props.onEndDate(e.format("YYYY-MM-DD"));
            this.setState({ enddate: e.format("YYYY-MM-DD") }, function () {
                //this.fetchCallBackData();
            });
        }
    }
    validation = (currentDate) => {
        if (currentDate.isBefore(moment().subtract(45, "days").format("YYYY-MM-DD"))) {
            return false;
          }
          return currentDate.isBefore(moment());   
    };

    validationEndDate = (currentDate) => {
        

        if (!currentDate.isBefore(moment())) {
            return false;
          }
      
          if (currentDate.isBefore(moment(this.state.startdate))) {
            return false;
          }
      
          return true;
        
    };

    getSelectedDateRange() {
        return {
            startdate: this.state.startdate,
            enddate: this.state.enddate
        }
    }

    render() {


        let { visible } = this.props;

        if (visible == false) {
            return null;
        }
        return (

            <>
                <Col md={this.props.colWidth ? this.props.colWidth:6}>
                    <Form.Group controlId="startdate_field">
                        <Form.Label>From Date :</Form.Label>
                        <Datetime value={new Date()}
                            dateFormat="YYYY-MM-DD"
                            value={this.state.startdate}
                            isValidDate={this.validation.bind(this)}
                            onChange={moment => this.handleStartDateChange(moment)}
                            utc={true}
                            timeFormat={false}
                            className="form-group"

                        />
                    </Form.Group>
                </Col>
                <Col md={this.props.colWidth ? this.props.colWidth:6}>
                    <Form.Group controlId="enddate_field">
                        <Form.Label>To Date :</Form.Label>
                        <Datetime value={new Date()}
                            dateFormat="YYYY-MM-DD"
                            value={this.state.enddate}
                            isValidDate={this.validationEndDate.bind(this)}
                            onChange={moment => this.handleEndDateChange(moment)}
                            utc={true}
                            timeFormat={false}
                            className="form-group right0"
                        />
                    </Form.Group>
                </Col>
            </>

        );
    }
}


export default DateRange;

