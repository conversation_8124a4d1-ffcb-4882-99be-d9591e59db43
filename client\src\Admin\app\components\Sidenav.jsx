import { Fragment, useEffect, useState } from 'react';
import Scrollbar from 'react-perfect-scrollbar';
import { styled } from '@mui/material';
import { MatxVerticalNav } from '../components';
import useSettings from '../../app/hooks/useSettings';
import { navigations } from '../../app/navigations';
import { connect } from "react-redux";
import {
  GetUserMenus, GetCommonspDataV2
} from './../../store/actions/CommonAction';

const StyledScrollBar = styled(Scrollbar)(() => ({
  paddingLeft: '1rem',
  paddingRight: '1rem',
  position: 'relative'
}));

const SideNavMobile = styled('div')(({ theme }) => ({
  position: 'fixed',
  top: 0,
  left: 0,
  bottom: 0,
  right: 0,
  width: '100vw',
  background: 'rgba(0, 0, 0, 0.54)',
  zIndex: -10,
  [theme.breakpoints.up('sm')]: { display: 'none' }
}));

const Sidenav = (props, { children }) => {
  const { settings, updateSettings } = useSettings();
  const [navigations, setNavigations] = useState([])

  const updateSidebarMode = (sidebarSettings) => {
    let activeLayoutSettingsName = settings.activeLayout + 'Settings';
    let activeLayoutSettings = settings[activeLayoutSettingsName];

    updateSettings({
      ...settings,
      [activeLayoutSettingsName]: {
        ...activeLayoutSettings,
        leftSidebar: {
          ...activeLayoutSettings.leftSidebar,
          ...sidebarSettings
        }
      }
    });
  };

  let token = localStorage.getItem('PBRMSToken');
  console.log('token', token);
  let { Userid } = 0
  if (token) {
    let AgentInfo = atob(token);
    let data = {};
    if (AgentInfo) {
      data = JSON.parse(AgentInfo) || {};
      console.log(data)
    }
    ({ Userid } = data);

  }

  useEffect(()=>{
    // GetUserMenus(function (result){
    //   let arr = result.data.results;
    //   let res = [];
    //   for(let i = 0; i < arr.length; i++){
    //     if(arr[i].InLeftMenu == 1){
    //       res.push(arr[i]);
    //     }
    //   }
    //   setNavigations(res);
      // setNavigations(result.data.results);
    //   console.log(result);
    // })
    try{
      props.GetCommonspDataV2({
        root: 'GetPermissions',
        // params: [{ MenuId: MenuId }],
        c: "R",
      }, function (errorStatus, result) {
        if (!errorStatus) {
          let arr = [];
          let res = [];
          // console.log(result)
          if (result && result.data && result.data[0].length > 0) {
            let menus = result.data[0];
            for(let i = 0; i < menus.length; i++){
              if(menus[i].InLeftMenu == 1){
                res.push(menus[i]);
              }
            }
            setNavigations(res);
          } 
        }
      });
    }
    catch(e){

    }
  },[])
  
  return (
    <Fragment>
      <StyledScrollBar options={{ suppressScrollX: true }}>
        {children}
        <MatxVerticalNav items={navigations} />
      </StyledScrollBar>

      {/* <SideNavMobile onClick={() => updateSidebarMode({ mode: 'compact' })} /> */}
    </Fragment>
  );
};

// export default Sidenav;

function mapStateToProps(state) {
  return {
  };
}

export default connect(
  mapStateToProps,
  {
    // GetCommonData,
    // GetCommonspData,
    GetCommonspDataV2
  }
)(Sidenav);
