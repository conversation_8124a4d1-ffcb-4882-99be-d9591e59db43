@import './variables.scss';
@import './utils.scss';
@import './index.scss';

h1 {
    color: blue;
}

.main-container {
    font-family: $title-font-family;
    margin: {
        right: 30px;
    }
    padding: {
        top: 10px;
        bottom: 30px;
    };
    position: relative;
    overflow-x: hidden;

    &.closed {
        left: 100px;
        width: calc(100% - 170px);
    }

    &.open {
        left: $sideNavWidth + 40px;
        width: calc(100% - 360px);
    }   
}

label {
    color: $form-label-color;
    font-size: 13px;
    margin-right: 20px;
    margin-bottom: 0px;
    font-family: 'Roboto';
}

input[type="file"] {
    display: none;
}

body {
    margin: 0px;
}

hr {
    margin: 20px 0px 10px;
    border: 1px dashed #aaa;
}

a {
    text-decoration: none;
}
