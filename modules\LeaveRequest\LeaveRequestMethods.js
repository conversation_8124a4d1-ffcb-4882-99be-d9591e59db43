
const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
var fs = require('fs');
let express = require('express');
var path = require('path');
const uuid = require('uuid');
const axios = require('axios');
const conf = require("../../env_config");
const { Base64Encode } = require('../../auth');
const CryptoJS = require("crypto-js");
const moment = require("moment");
const cache = require('memory-cache');
const _ = require('underscore');

exports.leaveAppliedByManager = async function (req, res) {
  try {

      // let sqlParams = [];  
      // sqlParams.push({ key: "RosterId", value: req.query.RosterId });
  
      let sqlParams = [];
      sqlParams.push({ key: "EmployeeId", value: req.headers.employeeid });
      sqlParams.push({ key: "AppliedBy", value: req.user.userId });    
      // params.push({ key: "LeaveCategoryId", value: req.body.leaveCategoryId });
      sqlParams.push({ key: "ApplicationDate", value: req.body.leaveDate });
      sqlParams.push({ key: "LeaveType", value: req.body.leaveType });
      sqlParams.push({ key: "Comment", value: req.body.agentComments });

      let response = await sqlHelper.sqlProcedure("R", "[Rms].[LeaveAppliedByManager]", sqlParams);     
      
      return res.send({
          status: 200,
          message: 'success',
          results: response.recordsets[0]
      });
  }
  catch (e) {
      console.log(e);
      return res.send({
          status: 500,
          message: e
      });
  }
}
