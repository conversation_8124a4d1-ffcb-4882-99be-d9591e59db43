
const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
var fs = require('fs');
let express = require('express');
var path = require('path');
const uuid = require('uuid');
const axios = require('axios');
const conf = require("../../env_config");



exports.setAgentStatus = async function (req, res) {
    const url = conf.dialerApiUrlV1 + "setAgentStatus.php"
    const body = {
        emp_id: req.query.agentCode,
        requested_by: req.query.managerid,
        leadid: req.query.bookingid,
        grade: req.query.grade,
        status: "blocked"
    }
    console.log("setAgentStatus", url);
    let response = await axios.get(url, {params:body});
    console.log("setAgentStatus", response);
    try {
        
        res.send(response.data);
        
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
}


exports.multi_conference = async function (req, res) {
    const url = conf.dialerApiUrlV1 + "multi_conference.php"
    const body = req.query;

    console.log("multi_conference", url);
    let response = await axios.get(url, {params:body});
    console.log("multi_conference", response);
    try {
        
        res.send(response.data);
        
    }
    catch (e) {
        console.log(e);

        res.send({
            status: 500,
            message: e
        });
    }
}
exports.mob2mobcall = async function (req, res) {
    const url = conf.dialerApiUrlV1 + "mob2mobcall.php"
    const body = req.query;

    console.log("mob2mobcall", url);
    let response = await axios.get(url, {params:body});
    console.log("mob2mobcall", response);
    try {
        
        res.send(response.data);
        
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
}