import React from "react";
import { TableContainer, Table, TableHead, TableRow, TableCell, TableBody, Checkbox } from "@mui/material";
import CircleIcon from "@mui/icons-material/Circle";
import RosterWiseData from "./RosterWiseData";
import moment from "moment";

const RosterSection = ({ title, className, startDate, endDate, rosterData, selectedLeave, checkedAllLeaveId, checkedLeaveId, totalShrinkage }) => {
  return (
    <div className={className}>
      <div className="ActiveRosterDate">
        <CircleIcon />
        <h4>{title}: </h4>
        {startDate && endDate && <p>{moment(startDate).format("DD MMM YY")} - {moment(endDate).format("DD MMM YY")}</p>}
      </div>
      <RosterWiseData 
        title = {title}
        rosterData={rosterData}
        selectedLeave={selectedLeave}
        checkedAllLeaveId={checkedAllLeaveId}
        checkedLeaveId={checkedLeaveId}
        totalShrinkage = {totalShrinkage}
      />
    </div>
  );
};

export default RosterSection;
