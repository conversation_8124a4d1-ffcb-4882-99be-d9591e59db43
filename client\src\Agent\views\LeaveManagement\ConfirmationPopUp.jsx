import { React, useState, useEffect } from "react";

import {
    GetCommonData, GetCommonspData, GetCommonspDataV2, RosterAvailableDates, GetPlannedLeaveAvailability,
    InsertLeaveRequest, leaveAppliedByManager
} from "../../store/actions/CommonAction";

import { connect } from "react-redux";
import { Modal, Box, Grid, TextField } from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import LeaveSuccessfullPopUp from "./LeaveSuccessfullPopUp";
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import moment from 'moment';
import Spinner from './Spinner';
import _ from 'lodash';
import { getUrlParameter } from '../../utility/utility.jsx';


const ConfirmationPopUp = (props) => {
    const { leaves, LeaveType, leaveDate, disabledDates, rosterData, processId, shrinkage } = props

    const [approvedDate, setApprovedDate] = useState();
    const [leaveName, setLeaveName] = useState();
    const [leaveApproved, setLeaveApproved] = useState(false);
    const [ErrorMessage, setErrorMessage] = useState('');
    const [showButton, setShowButton] = useState(true);
    const [loader, setLoader] = useState(false);
    const [leaveCategoryId, setLeaveCategoryId] = useState();
    const [agentComments, setAgentComments] = useState();
    const [approvalId, setApprovalId] = useState();
    const [approvalName, setApprovalName] = useState();
    const [otherLeaveExists, setOtherLeaveExists] = useState(false);

    useEffect(() => {
        let leave_type = ""
        let leaveCategory;
        if (leaves && leaves.length > 0) {
            for (let i = 0; i < leaves.length; i++) {
                if (leaves[i].Id == LeaveType) {
                    leave_type = leaves[i].LeaveType;
                    leaveCategory = leaves[i].LeaveCategoryId;
                }
            }
            setLeaveName(leave_type);
            setLeaveCategoryId(leaveCategory);
            setShowButton(true);
        }

        if(LeaveType == 1){
        const leavesOnSelectedDate = disabledDates?.filter(
            (d) => moment(d.MonthlyDate).format('YYYY-MM-DD') == leaveDate
          );
          
          const otherLeaveExists = leavesOnSelectedDate?.some(
            (d) => d.IsRoster && d.LeaveType > 1
          );
        //   console.log("otherLeaveExists", otherLeaveExists);
          setOtherLeaveExists(otherLeaveExists);
        }
    }, [props])


    const ErrorCheck = async (ErrorCode) => {
        await props.GetCommonData({
            limit: 10,
            skip: 0,
            root: 'ErrorMessage',
            con: [{ "IsActive": 1 }, { "ErrorCode": ErrorCode }],
            // con: [ "ORDER BY Id DESC" ],
            c: "R",
        }, function (data) {
            if (data && data.length > 0) {
                setErrorMessage(data[0].Message)
                setShowButton(false);
            }
        });
    }

    const ThrottledButton = _.throttle(() => {
        leaveConfirm();
        // Add any other logic you need here
    }, 2000); // Throttle for 2000ms (2 seconds)

    const weekOffRequest = () => {
        let RequestLeave = 0
        let AvailableDates = [];
        for (let j = 0; j < disabledDates.length; j++) {
            if (leaveDate == moment(disabledDates[j].MonthlyDate).format('YYYY-MM-DD') && disabledDates[j].RequestDates) {
                RequestLeave = disabledDates[j].RequestDates
            }
            if (disabledDates[j].IsRoster == 1 && disabledDates[j].SlotFull > 0 && disabledDates[j].DisabledDates == 0) {
                AvailableDates = AvailableDates + moment(disabledDates[j].MonthlyDate).format('YYYY-MM-DD');
                AvailableDates = AvailableDates + ',';
            }
        }
        console.log(AvailableDates.slice(0, -1));
        AvailableDates = AvailableDates.slice(0, -1)

        let body = {
            LeaveType: LeaveType, LeaveDate: leaveDate, RequestLeave: RequestLeave, AvailableDates: AvailableDates
        }
        try {
            InsertLeaveRequest(body, function (result) {
                if (result && result.data && result.data.status == 404) {
                    window.location.reload();
                }
                if (result && result.data && result.data.status == 200) {
                    let item = result.data.results[0]?.Status;
                    let ErrorCode = result.data.results[0]?.ErrorCode;
                    let Message = result.data.results[0]?.Message;
                    if (item) {
                        setLeaveApproved(true);
                        // GetLeaves()
                        props.handleClose();
                    }
                    else if (ErrorCode > 0) {
                        setLeaveApproved(false)
                        // ErrorCheck(ErrorCode)
                        setErrorMessage(Message)
                        setShowButton(false);
                    }
                    else {
                        setLeaveApproved(false)
                        props.handleClose()
                    }
                    console.log(result);
                }
            });
        }
        catch (ex) {
            props.handleClose()
        }
    }

    const otherLeavesRequest = () => {
        try {
            let source = getUrlParameter('source');
            // let EmployeeId = getUrlParameter('EmployeeId');
            let body = {
                leaveType: LeaveType, leaveDate: leaveDate,
                leaveCategoryId: leaveCategoryId,
                processId: processId,
                agentComments: agentComments
            }
            if (source == 'Approval') {
                leaveAppliedByManager(body, function (result) {
                    // console.log(result);
                    // props.handleClose();
                    if (result && result.data && result.data.status == 200) {
                        let item = result.data.results[0]?.Status;
                        // let ErrorCode = result.data.results[0]?.ErrorCode;
                        let Message = result.data.results?.ResponseMsg;
                        if (item) {
                            setApprovalId(result.data.results[0]?.ApprovalId);
                            setApprovalName(result.data.results[0]?.ApprovalName);
                            setLeaveApproved(true);
                            // GetLeaves()
                            props.handleClose();
                        }
                        else {
                            setLeaveApproved(false)
                            // ErrorCheck(ErrorCode)
                            setErrorMessage(Message)
                            setShowButton(false);
                        }

                        // console.log(result);
                    }
                });
            }
            else {
                GetPlannedLeaveAvailability(body, function (result) {
                    // console.log(result);
                    // props.handleClose();
                    if (result && result.data && result.data.status == 200) {
                        let item = result.data.results[0]?.Status;
                        // let ErrorCode = result.data.results[0]?.ErrorCode;
                        let Message = result.data.results?.ResponseMsg;
                        if (item) {
                            setApprovalId(result.data.results[0]?.ApprovalId);
                            setApprovalName(result.data.results[0]?.ApprovalName);
                            setLeaveApproved(true);
                            // GetLeaves()
                            props.handleClose();
                        }
                        else {
                            setLeaveApproved(false)
                            // ErrorCheck(ErrorCode)
                            setErrorMessage(Message)
                            setShowButton(false);
                        }

                        // console.log(result);
                    }
                });
            }

        }
        catch (ex) {
            props.handleClose();
        }

    }

    const handleCommentChange = (e) => {
        const value = e.target.value.slice(0, 100); // Limit input to 100 chars
        setAgentComments(value);
    };

    const leaveConfirm = () => {
        setLoader(true);
        setApprovedDate(leaveDate);
        setTimeout(function () {
            setLoader(false);
        }, 2000);
        let leave_type = ""
        for (let i = 0; i < leaves.length; i++) {
            if (leaves[i].Id == LeaveType) {
                leave_type = leaves[i].LeaveType
            }
        }
        setLeaveName(leave_type)
        if (LeaveType == 1) {
            weekOffRequest();
        }
        else {
            otherLeavesRequest();
        }

        //     root: 'InsertLeaveRequest',
        //     params: [{ LeaveType: LeaveType,  LeaveDate: leaveDate, RequestLeave: RequestLeave, AvailableDates: AvailableDates }],
        //     // con: [{ "IsActive": 1 }],
        //     c: "L",
        // }, function (errorStatus, result) {

        //     try {
        //         if (!errorStatus && result && result.data && result.data[0].length>0) {
        //             let item = result.data[0][0].Status
        //             let ErrorCode = result.data[0][0].ErrorCode
        //             let Message = result.data[0][0].Message
        //             if (item) {
        //                 // if(AvailableDates && AvailableDates.length > 0){
        //                 //     RosterAvailableDates(body, function (results) {
        //                 //         if (results && results.data && results.data.status == 200 && results.data.results==1 ) {
        //                 //             // setLeaveApproved(true)
        //                 //             // // GetLeaves()
        //                 //             // props.handleClose()
        //                 //         }
        //                 //     });
        //                 // }
        //                 setLeaveApproved(true)
        //                 // GetLeaves()
        //                 props.handleClose()
        //             }
        //             else if(ErrorCode>0){
        //                 setLeaveApproved(false)
        //                 // ErrorCheck(ErrorCode)
        //                 setErrorMessage(Message)
        //                 setShowButton(false);
        //                 // ErrorCheck(ErrorCode, function(message) {
        //                 //     setErrorMessage(message);
        //                 //     showButton(false);
        //                 // })


        //             }
        //             else{
        //                 setLeaveApproved(false)
        //                 props.handleClose()
        //             }

        //             console.log(result);
        //         }
        //     }
        //     catch (ex) {
        //         props.handleClose()
        //     }
        // });
    }

    return (
        <>
            {loader && <div className="Backdrop"> <Spinner /></div>}
            <Modal
                open={props.open}
                // onClose={props.handleClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
            >
                <Box className="LeaveApplicationPopup">
                    <header>

                        {showButton ? <h2><ErrorOutlineIcon />Please Confirm</h2>
                            : <h2></h2>}

                        <CloseIcon onClick={props.handleCloseConfirmPopUp} className="closebtn" />

                    </header>

                    <Grid item md={12}>
                        <div className="LeaveAppliedSucess">
                            {/* <CheckCircleIcon /> */}

                            {/* <h3>Leave Applied Successfully</h3> */}
                            <ul>
                                {/* <li>Start Date</li>
                                {leaveDate && <li>{leaveDate}</li>}
                                <li>End Date</li>
                                {leaveDate && <li>{leaveDate}</li>} */}
                                <li>Selected Date</li>
                                {leaveDate && <li>{moment(leaveDate).format('DD-MMM-YYYY')}</li>}
                                <li>Leave Type</li>
                                {leaveName && <li>{leaveName}</li>}
                            </ul>
                            {/* // value={comment}
                                // onChange={handleCommentChange} */}

                            {shrinkage && shrinkage.DefaultShrinkage < shrinkage.Shrinkage && <p style={{ color: "red" }}>Are you sure you want to apply for leave beyond {shrinkage.Shrinkage}% shrinkage</p>}
                            {LeaveType != 1 && <TextField
                                // label="Write your comment..."
                                placeholder="Write your comment (max 100 characters)..."
                                variant="outlined"
                                fullWidth
                                multiline
                                rows={3}
                                // value='lll'
                                onChange={handleCommentChange}
                                sx={{ width: "300px", marginY: 2 , margin: "auto", display: "block" }}
                            />}

                            {showButton ?
                                <>
                                    {LeaveType == 1 &&
                                        <>
                                            {otherLeaveExists && <p className="NoteMsg">Week off Date: {moment(leaveDate).format('DD-MMM-YYYY')}. Your EL/CL/SL etc would be replaced by Week off</p>}
                                            <p className="NoteMsg">*Leaves once applied cannot be changed for the Roster</p>
                                        </>}
                                    <button onClick={ThrottledButton}>Confirm The Date</button>
                                </>
                                :
                                <p className="noSlot">{ErrorMessage}</p>
                            }

                        </div>
                    </Grid>

                </Box>
            </Modal>

            {<LeaveSuccessfullPopUp open={leaveApproved && !loader} leaves={leaves} LeaveType={LeaveType} approvedDate={approvedDate}
                leaveName={leaveName} approvalId={approvalId} approvalName={approvalName}
                handleClose={() => {
                    setLeaveApproved(false);
                }}
                shrinkage={shrinkage}
            ></LeaveSuccessfullPopUp>}
        </>
    );

}

function mapStateToProps(state) {
    return {
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData,
        GetCommonspDataV2
    }
)(ConfirmationPopUp);
