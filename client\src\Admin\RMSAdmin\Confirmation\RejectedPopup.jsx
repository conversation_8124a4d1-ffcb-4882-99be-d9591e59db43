import React, {useState} from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { Grid, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

const RejectedPopup = ( props ) => {

    const {open, onClose, user, handleApprove} = props
    const [comment,setComment] = useState();

    const handleReject = (TransferId, userId, status) => {
        // handleApprove(e.target.value);
        handleApprove(TransferId, userId, status, comment);
    }

    const handleComment = (e) => {
        console.log(e.target.value);
        setComment(e.target.value);
    }

    return (
        <Dialog open={open} onClose={onClose} maxWidth="xs" className="ApproveRequestPopup">
            <DialogTitle>
                {"Reject request"}
                <IconButton
                    aria-label="close"
                    onClick={onClose}
                    sx={{ position: 'absolute', right: 8, top: 8, color: (theme) => theme.palette.grey[500] }}
                >
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent>
            {user &&
                    <>
                <Grid container>
                    <Grid item md={12} sm={12} xs={12}>
                    <p>You're about to reject a request, this is a non-revertible action</p> 
                    <div className="ApproveRequestDetails">
                    <Grid container spacing={2}>
                        <Grid item md={6}>Request type</Grid>
                        <Grid item md={6}><span>{user.RequestName}</span></Grid>
                        <Grid item md={6}>TL name</Grid>
                        <Grid item md={6}><span>{user.ManagerName}</span></Grid>
                        <Grid item md={6}>Request E-Code</Grid>
                        <Grid item md={6}><span>{user.EmployeeId}</span></Grid>
                        {/* <Grid item md={6}>Usergroup</Grid>
                        <Grid item md={6}><span>MARVEL</span></Grid> */}
                        <Grid item md={6}>Current process</Grid>
                        <Grid item md={6}><span>{user.ProcessName}</span></Grid>
                        <Grid item md={6}>Move to process</Grid>
                        <Grid item md={6}><span>{user.TransferProcessTo}</span></Grid>
                        <Grid item md={6}>Comment</Grid>
                        <Grid item md={6}><span><textarea onChange = {handleComment} rows="5" cols="15"></textarea></span></Grid> 
                    </Grid>
                    </div>  
                    </Grid>

                    <Grid item md={12} sm={12} xs={12} className="alignCenter">
                        <button className="Approvebutton rejectBtn"  value = '0' onClick = {() => handleReject(user.TransferId,user.UserId, 0)}>Reject</button>
                    </Grid>
                </Grid>
                </>}
            </DialogContent>

        </Dialog>
    );
}

export default RejectedPopup;
