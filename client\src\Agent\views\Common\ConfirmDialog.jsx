import React from "react";
import { But<PERSON>, Modal } from 'react-bootstrap';
import Loader from './Loader';


class ConfirmDialog extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            show: this.props.show,
            title: this.props.title,
            message: this.props.message,
            isLoading: false,
        }
    }

    handleClose () {
        //this.setState({ show: false });
        this.props.onCancel();
    }
    
    handleConfirm (props) {
        this.setState({ isLoading: true });
        this.props.onConfirm();
    }

    render() {
            return (                
            <>
            <Modal show={this.state.show} onHide={() => this.handleClose()}>
                <Modal.Header closeButton>
                    <Modal.Title>{this.state.title}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{this.state.message}</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => this.handleClose()}>
                        No
                    </Button>
                    <Button variant="primary" onClick={() => this.handleConfirm()} disabled={this.state.isLoading}>
                        Yes
                    </Button>
                    {(
                        this.state.isLoading && <Loader />
                    )}
                </Modal.Footer>
            </Modal>
            </>)
    }
}

export default ConfirmDialog;
