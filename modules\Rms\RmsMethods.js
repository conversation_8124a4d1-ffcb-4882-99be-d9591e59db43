
const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
var fs = require('fs');
let express = require('express');
var path = require('path');
const uuid = require('uuid');
const axios = require('axios');
const conf = require("../../env_config");
const { Base64Encode } = require('../../auth');
const CryptoJS = require("crypto-js");
const moment = require("moment");
const cache = require('memory-cache');
const _ = require('underscore');


const app = express();


exports.CreateRoster = async function (req, res) {
    try {
        let sqlParams = [];
        let response = await sqlHelper.sqlProcedure("R", "[Rms].[CreateRoster]", sqlParams);

        console.log(response)
        return res.send({
            status: 200,
            message: 'success'
        });
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}

exports.CreateRosterv2 = async function (req, res) {
    try {
        let sqlParams = [];
        let response = await sqlHelper.sqlProcedure("R", "[Rms].[CreateRoster_v2]", sqlParams);

        console.log(response)
        return res.send({
            status: 200,
            message: 'success'
        });
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}

exports.RmsRedirectionToUrl = async function (req, res) {
    try {

        // const userId = req.cookies.MatrixToken;
        const { userId } = req.user || {};
        let Source = req.query.Source || 'matrix';

        // console.log('Source1', Source);
        // console.log('Test', userId)
        // console.log('keys', Object.keys(req))
        // let userId = req.query.userId;
        // var ApplicationId = req.query.ApplicationId;
        // console.log('Test', userId)
        let ApplicationId = 1;
        // let query = `Select * from Rms.UserMaster
        //     where 
        //     IsActive = 1 
        //     and userId = @userId
        //     and ApplicationId = @ApplicationId
        //     order by id`;
        // let cachedGetUser = cache.get('GetUser_'+userId);

        // if (cachedGetUser) {
        //     let UserInfo = {
        //         "Userid": userId,
        //         "ApplicationId": ApplicationId,
        //         "EmployeeId": cachedGetUser.recordsets[0]?.EmployeeId || '',
        //         "RoleId": cachedGetUser.recordsets[0]?.RoleId || 0,
        //         "ProcessId": cachedGetUser.recordsets[0].ProcessId,
        //         "ProcessName": cachedGetUser.recordsets[0].ProcessName || '',
        //         "UserType": cachedGetUser.recordsets[0].UserType || '',
        //         "ManagerId": cachedGetUser.recordsets[0].ManagerId || '',
        //         "ManagerName": cachedGetUser.recordsets[0].ManagerName || '',
        //     };


        //     let Info = ""
        //     Info = Base64Encode(JSON.stringify(UserInfo));

        //     //   res.cookie(`PBRMSToken`, Info, { maxAge: 900000, httpOnly: true });
        //     res.cookie(`PBRMSToken`, Info, {domain: '.policybazaar.com',secure: true, httpOnly: true, maxAge: 5 * 60 * 1000});
        //     res.cookie(`Source`, Source, {domain: '.policybazaar.com',secure: true, httpOnly: true, maxAge: 5 * 60 * 1000});

        //     return res.send({
        //       status: 200,
        //       Info: Info,
        //       message: "Success"
        //     });

        // } 


        let sqlParams = [];
        sqlParams.push({ key: "UserId", value: userId });
        sqlParams.push({ key: "ApplicationId", value: ApplicationId });

        // console.log('test2', query);
        // let result = await sqlHelper.sqlquery("R", query, sqlparam);
        let result = await sqlHelper.sqlProcedure("R", "[Rms].[GetUserData]", sqlParams);


        if (result && result.recordset && result.recordset[0]) {
            let UserInfo = {
                "Userid": userId,
                "ApplicationId": ApplicationId,
                "EmployeeId": result.recordset[0]?.EmployeeId || '',
                "RoleId": result.recordset[0]?.RoleId || 0,
                "ProcessId": result.recordset[0].ProcessId,
                "ProcessName": result.recordset[0].ProcessName || '',
                "UserType": result.recordset[0].UserType || '',
                "ManagerId": result.recordset[0].ManagerId || '',
                "ManagerName": result.recordset[0].ManagerName || '',
                "Approver1": result.recordset[0].Approver1 || '',
                "Approver2": result.recordset[0].Approver2 || '',
            };


            let Info = ""
            Info = Base64Encode(JSON.stringify(UserInfo));

            //   res.cookie(`PBRMSToken`, Info, { maxAge: 900000, httpOnly: true });
            res.cookie(`PBRMSToken`, Info, { domain: '.policybazaar.com', secure: true, httpOnly: true, maxAge: 5 * 60 * 1000 });
            res.cookie(`Source`, Source, { domain: '.policybazaar.com', secure: true, httpOnly: true, maxAge: 5 * 60 * 1000 });

            // cache.put('GetUser_'+userId, result, (5 * 60 * 1000));

            return res.send({
                status: 200,
                message: 'success',
                Info: Info
            });
        }
        else {
            return res.send({
                status: 500,
                message: 'False'
            });
        }

    } catch (err) {
        return res.send({
            status: 500,
            message: err.toString(),
        });
    }
}

exports.DownloadRotaData = async function (req, res) {
    try {
        let sqlParams = []
        sqlParams.push({ key: "RosterId", value: req.query.RosterId });
        sqlParams.push({ key: "ProductId", value: req.query.ProductId });
        sqlParams.push({ key: "StateId", value: req.query.StateId });
        let response = await sqlHelper.sqlProcedure("R", "[Rms].[GetRotaWeeklySheet_v2]", sqlParams);

        // console.log(response)
        return res.send({
            status: 200,
            message: 'success',
            results: response.recordsets[0]
        });
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}

exports.InsertRosterHistory = async function (req, res) {
    let sheetResults = [];
    let sheetRow = 0;

    try {
        let RosterId = req.body.RosterId;
        let ProductId = req.body.ProductId;
        // console.log("Req body is ", req.body);

        if (req.body.AllIndia == 1) {
            let data = req.body.excelData;
            const results = [];
            const stateData = _.groupBy(data, 'StateId');
            // console.log(Object.keys(stateData));
            let stateList = Object.keys(stateData)
            const ProcessData = _.groupBy(data, 'ProcessId');
            // console.log(Object.keys(ProcessData));
            let processList = Object.keys(ProcessData)
            for (let i = 0; i < stateList.length; i++) {
                for (let j = 0; j < processList.length; j++) {
                    let query = `delete ruh from RMS.RosterUserHistory as ruh where ruh.ProcessId=@ProcessId and ruh.RosterId=@RosterId and StateId=@StateId;`
                    let sqlparam = [];
                    sqlparam.push({ key: "StateId", value: stateList[i] });
                    sqlparam.push({ key: "RosterId", value: RosterId });
                    sqlparam.push({ key: "ProcessId", value: processList[j] });
                    await sqlHelper.sqlquery('L', query, sqlparam);
                }

            }
            if (data.length > 0) {
                for (let index = 0; index < data.length; index++) {
                    let sqlparam = [];
                    sqlparam.push({ key: "EmployeeId", value: data[index].EmployeeId });
                    // sqlparam.push({ key: "ProcessName", value: data[index].ProcessName });
                    sqlparam.push({ key: "ProcessId", value: data[index].ProcessId });
                    sqlparam.push({ key: "TLEmployeeId", value: data[index].ManagerId });
                    sqlparam.push({ key: "RosterId", value: RosterId });
                    sqlparam.push({ key: "ProductId", value: ProductId });
                    sqlparam.push({ key: "StateId", value: data[index].StateId });

                    let result = await sqlHelper.sqlProcedure("L", "[RMS].[AddUserInRoster]", sqlparam);
                    results.push(result.recordsets[0]);
                }

                return res.status(200).json({
                    status: 200,
                    data: 'Success'
                });
            }
        }
        else if (req.body.AllIndia === undefined) {
            let data = req.body.excelData;
            let StateId = req.body.StateId;
            // console.log("Array is ", req.body.ProcessList);
            let ProcessNames = req.body.ProcessList;
            let results = [];
            let promises = [];
            // let query=`delete ruh from RMS.RosterUserHistory as ruh inner join RMS.ProcessMaster as pm on ruh.ProcessId=pm.Processid where pm.ProcessName=@ProcessName and ruh.RosterId=@RosterId;`

            let query = `delete ruh from RMS.RosterUserHistory as ruh where ruh.ProcessId=@ProcessId and ruh.RosterId=@RosterId and StateId=@StateId;`
            for (let index = 0; index < ProcessNames.length; index++) {
                let sqlparam = [];
                sqlparam.push({ key: "ProcessId", value: ProcessNames[index] });
                sqlparam.push({ key: "RosterId", value: RosterId });
                sqlparam.push({ key: "StateId", value: StateId });
                promises.push(sqlHelper.sqlquery('L', query, sqlparam));
            }

            await Promise.allSettled(promises);

            if (data.length > 0) {
                for (let index = 0; index < data.length; index++) {

                    let sqlparam = [];
                    sqlparam.push({ key: "EmployeeId", value: data[index].EmployeeId });
                    // sqlparam.push({ key: "ProcessName", value: data[index].ProcessName });
                    sqlparam.push({ key: "ProcessId", value: data[index].ProcessId });
                    sqlparam.push({ key: "TLEmployeeId", value: data[index].ManagerId });
                    sqlparam.push({ key: "RosterId", value: RosterId });
                    sqlparam.push({ key: "ProductId", value: ProductId });
                    sqlparam.push({ key: "StateId", value: StateId });

                    let result = await sqlHelper.sqlProcedure("L", "[RMS].[AddUserInRoster]", sqlparam);
                    sheetResults.push(result.recordsets[0]);
                }
                // rows = sheetResults.length-1;
                return res.status(200).json({
                    status: 200,
                    data: 'Success'
                });
            }
        }
        else {
            return res.status(404).json({
                status: 404,
                data: 'No data to insert'
            });
        }
    }
    catch (err) {
        console.log(err);
        sheetRow = sheetResults.length + 1;
        return res.status(500).json({
            status: 500,
            error: err,
            sheetRow: sheetRow
        });

    }
    finally {

    }
}


function parseCookies(request) {
    var cookies = {};
    request.headers && request.headers.cookie && request.headers.cookie.split(';').forEach(function (cookie) {
        var parts = cookie.match(/(.*?)=(.*)$/);
        cookies[parts[1].trim()] = (parts[2] || '').trim();
    });
    return cookies;
}


exports.InsertLeaveRequest = async function (req, res) {
    try {
        // console.log(req.body);
        if (req.body.AvailableDates.length > 0) {

            let CheackLeaveAvailability = await GetAvailableDates(req.user.userId)
            let ApplyDate = false;
            for (let i = 0; i <= CheackLeaveAvailability.length; i++) {
                if ((CheackLeaveAvailability[i] && moment(CheackLeaveAvailability[i].MonthlyDate).format('YYYY-MM-DD')) == req.body.LeaveDate) {
                    ApplyDate = true;
                    break;
                }
            }

            // let checkresponse;
            let response;
            // if (AppliedDate) {
            //     response = await sqlHelper.sqlProcedure("R", "[Rms].[InsertLeaveRequest]", sqlParams);
            // }

            // if(ApplyDate && req.body.RequestLeave == 1 ) {
            //     response = await ForceApplyLeave (req);
            // }
            if (ApplyDate && req.body.LeaveType == 1) {
                response = await InsertWeekOffRequest(req);
            }

            if (response && response.recordsets && response.recordsets[0]) {
                return res.send({
                    status: 200,
                    message: 'success',
                    results: response.recordsets[0]
                });
            }
            else {
                return res.status(500).json({
                    status: 500
                    // error: err
                });
            }
        }
        return res.status(200).json({
            status: 404
        });
    }
    catch (err) {
        console.log(err);
        return res.status(500).json({
            status: 500,
            error: err
        });

    }

}

const GetAvailableDates = async function (UserId) {
    let params = [];
    params.push({ key: "UserId", value: UserId });
    params.push({ key: "ForAuto", value: 1 });
    let TotalDates = await sqlHelper.sqlProcedure("L", "[Rms].[GetLeaveTakenAndSlotAvailibility_v2]", params);
    let AvailableDatesDate = TotalDates.recordsets[0];
    // for(let i = 0; i<=AvailableDatesDate.length;i++){
    //     if(moment(AvailableDatesDate[i].MonthlyDate).format('YYYY-MM-DD') == req.body.LeaveDate ){
    //         return true;
    //     }
    // }
    return AvailableDatesDate;
}


const CheckWeekOffValidity = async function (req) {
    let sqlParams = [];
    const Source = parseCookies(req)['Source'] || 'NULL';
    sqlParams.push({ key: "UserId", value: req.user.userId });
    sqlParams.push({ key: "AvailableDates", value: req.body.AvailableDates });
    sqlParams.push({ key: "LeaveDate", value: req.body.LeaveDate });
    sqlParams.push({ key: "LeaveType", value: req.body.LeaveType });
    sqlParams.push({ key: "RequestLeave", value: req.body.RequestLeave });
    sqlParams.push({ key: "AppliedBy", value: req.user.userId });
    sqlParams.push({ key: "Source", value: Source });

    return await sqlHelper.sqlProcedure("R", "[Rms].[CheckWeekOffValidity]", sqlParams);

}


const ForceApplyLeave = async function (req) {
    let sqlParams = [];
    const Source = parseCookies(req)['Source'] || 'NULL';
    sqlParams.push({ key: "UserId", value: req.user.userId });
    sqlParams.push({ key: "AvailableDates", value: req.body.AvailableDates });
    sqlParams.push({ key: "ApplicationDate", value: req.body.LeaveDate });
    sqlParams.push({ key: "LeaveType", value: req.body.LeaveType });
    sqlParams.push({ key: "AppliedBy", value: req.user.userId });
    sqlParams.push({ key: "Source", value: Source });

    return await sqlHelper.sqlProcedure("R", "[Rms].[ForceApplyLeave_v2]", sqlParams);
}


const InsertWeekOffRequest = async function (req) {
    let sqlParams = [];
    const Source = parseCookies(req)['Source'] || 'NULL';
    sqlParams.push({ key: "UserId", value: req.user.userId });
    sqlParams.push({ key: "AvailableDates", value: req.body.AvailableDates });
    sqlParams.push({ key: "LeaveDate", value: req.body.LeaveDate });
    sqlParams.push({ key: "LeaveType", value: req.body.LeaveType });
    sqlParams.push({ key: "RequestLeave", value: req.body.RequestLeave });
    sqlParams.push({ key: "AppliedBy", value: req.user.userId });
    sqlParams.push({ key: "Source", value: Source });

    return await sqlHelper.sqlProcedure("R", "[Rms].[InsertAdvisorWeekOff]", sqlParams);
}


exports.ApplyAutoLeave = async function (req, res) {
    try {
        let query = 'Select TOP(1) Id AS RosterId from Rms.RosterMaster where FreezedForAdmin = 1 AND FreezedForAgent = 0 order by CreatedOn'
        let RosterId = await sqlHelper.sqlquery("R", query, []);
        let sqlParams = [];
        // sqlParams.push({ key: "RosterId", value: req.query.RosterId });
        // console.log(RosterId.recordsets[0]);
        sqlParams.push({ key: "RosterId", value: RosterId.recordsets[0] });
        // // fetch all agents
        let toalPendingLeaves = await sqlHelper.sqlProcedure("L", "[Rms].[PendingLeaves]", sqlParams);

        //loop all agents
        for (let index = 0; index < toalPendingLeaves.recordsets[0].length; index++) {
            const Agent = toalPendingLeaves.recordsets[0][index];

            //check for pending leaves
            for (let leave = 0; leave < Agent.PendingLeaves; leave++) {
                //fetch dates
                let GetLeaveTakenAndSlotAvailibility_v2Params = [];
                GetLeaveTakenAndSlotAvailibility_v2Params.push({ key: "UserId", value: Agent.UserId });
                GetLeaveTakenAndSlotAvailibility_v2Params.push({ key: "ForAuto", value: 1 });
                let Slots = await sqlHelper.sqlProcedure("L", "[Rms].[GetLeaveTakenAndSlotAvailibility_v2]", GetLeaveTakenAndSlotAvailibility_v2Params);
                if (Slots.recordsets && Slots.recordsets[0].length) {
                    //Apply leaves

                    let AvailableDates = [];

                    for (let ad = 0; ad < Slots.recordset.length; ad++) {
                        //console.log("Slots.recordset[ad]",Slots.recordset[ad])
                        AvailableDates.push(moment(Slots.recordset[ad].MonthlyDate).format('YYYY-MM-DD'))
                    }

                    let avalibleleave = Slots.recordsets[0][0];
                    let InserLeaveRequestParams = [];
                    InserLeaveRequestParams.push({ key: "UserId", value: Agent.UserId });
                    InserLeaveRequestParams.push({ key: "LeaveType", value: 1 });
                    InserLeaveRequestParams.push({ key: "Leavedate", value: moment(avalibleleave.MonthlyDate).format('YYYY-MM-DD') });
                    InserLeaveRequestParams.push({ key: "RequestLeave", value: avalibleleave.RequestDates });
                    InserLeaveRequestParams.push({ key: "AvailableDates", value: AvailableDates.join() });
                    let response = await sqlHelper.sqlProcedure("L", "[Rms].[InsertLeaveRequest]", InserLeaveRequestParams);

                    //console.log(InserLeaveRequestParams)
                    // console.log(response)
                }

            }
        }

        return res.send({
            status: 200,
            message: 'success'
        });
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}

exports.InsertTransferRequest = async function (req, res) {
    try {
        let params = [];
        params.push({ key: "UserId", value: req.body.UserId });
        params.push({ key: "ManagerId", value: req.user.userId });
        params.push({ key: "RequestedBy", value: req.user.userId });
        params.push({ key: "CurrentProcess", value: req.body.CurrentProcess });
        params.push({ key: "MovedToProcess", value: req.body.MovedToProcess });
        params.push({ key: "RequestType", value: req.body.RequestType });

        // params.push({ key: "Status", value: 1 });


        let response = await sqlHelper.sqlProcedure("L", "[Rms].[InsertTransferRequest]", params);

        if (response.recordsets[0]) {
            return res.send({
                status: 200,
                message: 'success',
                results: response.recordsets[0]
            });
        }
        else {
            return res.status(500).json({
                status: 500
            });
        }
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}

exports.updateAdvisorTransferData = async function (req, res) {
    try {
        let params = [];
        params.push({ key: "TransferId", value: req.body.TransferId });
        params.push({ key: "UserId", value: req.body.userId });
        params.push({ key: "Status", value: req.body.status });
        params.push({ key: "Comments", value: req.body.comment });
        params.push({ key: "ApprovedBy", value: req.user.userId });

        let response = await sqlHelper.sqlProcedure("L", "[Rms].[UpdateTransferData]", params);

        if (response?.recordsets) {
            return res.send({
                status: 200,
                message: 'success',
                results: response?.recordsets
            });
        }
        else {
            return res.status(500).json({
                status: 500
            });
        }
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}

exports.updateAdvisorRemoveRequest = async function (req, res) {
    try {
        let params = [];
        params.push({ key: "TransferId", value: req.body.TransferId });
        params.push({ key: "UserId", value: req.body.UserId });
        params.push({ key: "Status", value: req.body.Status });
        // params.push({ key: "Comments", value: req.body.comment });    
        params.push({ key: "ProcessId", value: req.body.ProcessId });
        params.push({ key: "ManagerEmployeeId", value: req.body.ManagerEmployeeId });
        params.push({ key: "ApprovedBy", value: req.user.userId });

        let response = await sqlHelper.sqlProcedure("L", "[Rms].[updateAdvisorRemoveRequest]", params);

        if (response?.recordsets) {
            return res.send({
                status: 200,
                message: 'success',
                results: response?.recordsets
            });
        }
        else {
            return res.status(500).json({
                status: 500
            });
        }
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}

exports.addAdvisorsProcess = async function (req, res) {
    try {
        let users = req.body.userId;
        let response
        for (let i = 0; i < users.length; i++) {

            let sqlparams = [];
            sqlparams.push({ key: "UserId", value: users[i] });
            sqlparams.push({ key: "ManagerId", value: req.user.userId });
            sqlparams.push({ key: "RequestedBy", value: req.user.userId });
            sqlparams.push({ key: "MovedToProcess", value: req.body.MovedToProcess });
            sqlparams.push({ key: "RequestType", value: req.body.RequestType });

            response = await sqlHelper.sqlProcedure("L", "[Rms].[InsertTransferRequest]", sqlparams);

        }

        if (response?.recordsets) {
            return res.send({
                status: 200,
                message: 'success',
                results: response?.recordsets
            });
        }
        else {
            return res.status(500).json({
                status: 500
            });
        }
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}


exports.FreezedTlData = async function (req, res) {
    try {
        let params = [];
        params.push({ key: "ManagerId", value: req.user.userId });

        let response = await sqlHelper.sqlProcedure("L", "[Rms].[UpdateFreezeTlRoster]", params);

        if (response?.recordsets?.[0]) {
            return res.send({
                status: 200,
                message: 'success',
                results: response?.recordsets[0]
            });
        }
        else {
            return res.status(500).json({
                status: 500
            });
        }
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}


exports.getTLRosterData = async function (req, res) {
    try {
        let params = [];
        params.push({ key: "ManagerId", value: req.user.userId });

        let response = await sqlHelper.sqlProcedure("R", "[Rms].[GetTLRosterData]", params);

        if (response?.recordsets?.[0]) {
            return res.send({
                status: 200,
                message: 'success',
                results: response?.recordsets[0]
            });
        }
        else {
            return res.status(500).json({
                status: 500
            });
        }
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}


exports.MenuAuthentication = async function (req, res) {
    try {
        let params = [];
        let menuStatus = false;
        params.push({ key: "UserId", value: req.user.userId });

        let response = await sqlHelper.sqlProcedure("R", "[Persona].[GetPermissions]", params);
        console.log(req.query);
        console.log(response?.recordsets?.[0]);
        let menus = response?.recordsets?.[0]

        for (let i = 0; i < menus.length; i++) {
            if (menus[i].MenuId == req.query.MenuId) {
                menuStatus = true;
                break;
            }
        }

        if (response?.recordsets?.[0]) {
            return res.send({
                status: 200,
                message: 'success',
                results: menuStatus
            });
        }
        else {
            return res.status(500).json({
                status: 500
            });
        }
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}


exports.GetUserMenus = async function (req, res) {
    try {
        let params = [];
        params.push({ key: "UserId", value: req.user.userId });

        let response = await sqlHelper.sqlProcedure("R", "[Persona].[GetPermissions]", params);

        if (response?.recordsets?.[0]) {
            return res.send({
                status: 200,
                message: 'success',
                results: response?.recordsets?.[0]
            });
        }
        else {
            return res.status(500).json({
                status: 500
            });
        }
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}

exports.GetRosterData = async function (req, res) {
    try {
        let sqlParams = [];
        let response = await sqlHelper.sqlProcedure("R", "[Rms].[GetRosterDataApproval]", sqlParams);

        return res.send({
            status: 200,
            message: 'success',
            result: response.recordsets[0]
        });
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}


exports.updateLeaveApproval = async function (req, res) {
    try {
        // let sqlParams = [];  
        // sqlParams.push({ key: "RosterId", value: req.query.RosterId });
        // console.log(req.body)
        for (let i = 0; i < req.body.leaveData.length; i++) {
            let sqlParams = [];
            sqlParams.push({ key: "UserId", value: req.user.userId });
            // sqlParams.push({ key: "ApprovalValue", value: req.body.ApprovalValue });
            sqlParams.push({ key: "LeaveId", value: req.body.leaveData[i].leaveId });
            sqlParams.push({ key: "WeekOffShrinkage", value: req.body.leaveData[i].weekoffShrinkage || 0 });
            sqlParams.push({ key: "CurrentShrinkage", value: req.body.leaveData[i].currentShrinkage || 0 });
            sqlParams.push({ key: "Comment", value: req.body.comments });
            // console.log(req.body.leaveIds[i])
            let response = await sqlHelper.sqlProcedure("R", "[Rms].[UpdateLeaveApproval]", sqlParams);
        }
        // let GetRosterData = await sqlHelper.sqlProcedure("R", "[Rms].[LeaveApprovalUpdate]", sqlParams); 


        return res.send({
            status: 200,
            message: 'success',
            result: 1
        });
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}

exports.updateLeaveRejection = async function (req, res) {
    try {
        // console.log(req.body)
        for (let i = 0; i < req.body.leaveData.length; i++) {
            let sqlParams = [];
            sqlParams.push({ key: "UserId", value: req.user.userId });
            sqlParams.push({ key: "LeaveId", value: req.body.leaveData[i].leaveId });
            sqlParams.push({ key: "WeekOffShrinkage", value: req.body.leaveData[i].weekoffShrinkage || 0 });
            sqlParams.push({ key: "CurrentShrinkage", value: req.body.leaveData[i].currentShrinkage || 0 });
            sqlParams.push({ key: "Comment", value: req.body.comments });

            // console.log(req.body.leaveIds[i])
            let response = await sqlHelper.sqlProcedure("R", "[Rms].[UpdateLeaveRejection]", sqlParams);

        }

        return res.send({
            status: 200,
            message: 'success',
            result: 1
        });
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}


exports.updateLeaveCancellation = async function (req, res) {
    // let leaveIds = [];
    // leaveIds = req.body.leaveIds;
    try {
        for (let i = 0; i < req.body.leaveIds.length; i++) {
            let sqlParams = [];
            sqlParams.push({ key: "UserId", value: req.user.userId });
            sqlParams.push({ key: "LeaveId", value: req.body.leaveIds[i] });
            sqlParams.push({ key: "WeekOffShrinkage", value: req.body.weekoffShrinkage || 0 });
            sqlParams.push({ key: "CurrentShrinkage", value: req.body.currentShrinkage || 0 });
            // console.log(req.body.leaveIds[i])
            let response = await sqlHelper.sqlProcedure("R", "[Rms].[updateLeaveCancellation]", sqlParams);
            // console.log(GetData);
        }

        return res.send({
            status: 200,
            message: 'success',
            result: 1
        });
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}

exports.leaveAppliedByManager = async function (req, res) {
    try {

        // let sqlParams = [];  
        // sqlParams.push({ key: "RosterId", value: req.query.RosterId });

        let sqlParams = [];
        sqlParams.push({ key: "EmployeeId", value: req.headers.employeeid });
        sqlParams.push({ key: "AppliedBy", value: req.user.userId });
        // params.push({ key: "LeaveCategoryId", value: req.body.leaveCategoryId });
        sqlParams.push({ key: "ApplicationDate", value: req.body.leaveDate });
        sqlParams.push({ key: "LeaveType", value: req.body.leaveType });

        let response = await sqlHelper.sqlProcedure("R", "[Rms].[LeaveAppliedByManager]", sqlParams);

        return res.send({
            status: 200,
            message: 'success',
            result: response.recordsets[0]
        });
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}

exports.getAdvisorData = async function (req, res) {
    try {

        // let sqlParams = [];  
        // sqlParams.push({ key: "RosterId", value: req.query.RosterId });

        let sqlParams = [];
        sqlParams.push({ key: "EmployeeId", value: req.headers.employeeid });

        let response = await sqlHelper.sqlProcedure("R", "[Rms].[GetAdvisorOnEmployeeId]", sqlParams);

        return res.send({
            status: 200,
            message: 'success',
            result: response.recordsets[0]
        });
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}

exports.validationPlannedLeave = async function (req, res) {

    try {
        let leaveFlag = await validations(req);
        return res.send({
            status: 200,
            message: 'success',
            result: leaveFlag
        });

    }
    catch (ex) {

    }
}

exports.plannedLeave = async function (req, res) {
    let statusFlag = 0
    
    try {
        let leaveFlag = await validations(req);
        // console.log(leaveFlag);

        if (leaveFlag?.APIResponse) {
            statusFlag = await InsertLeaves(req, res);

            return res.send({
                status: 200,
                message: 'success',
                results: statusFlag.recordsets[0]
            });
        }
        else {
            return res.send({
                status: 200,
                message: 'success',
                results: leaveFlag
            });
        }
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}

const validations = async (req) => {
    let UserArr = [];
    let RoasterDates = [];
    let body = {};
    let RoasterArr = []
    try {
        let params = [];
        params.push({ key: "UserId", value: req.user.userId });
        params.push({ key: "LeaveCategoryId", value: req.body.leaveCategoryId });
        params.push({ key: "LeaveType", value: req.body.leaveType });
        params.push({ key: "ProcessId", value: req.body.processId });
        params.push({ key: "ApplicationDate", value: req.body.leaveDate });

        response = await sqlHelper.sqlProcedure("L", "[Rms].[GetPlannedLeaveDataOnUsers]", params);
        RoasterArr = response?.recordsets[0];
        UserArr = response?.recordsets[1];
        roasterDates = response?.recordsets[2];

        const transformedArray =
            UserArr.map(item => ({
                UserObj: {
                    UserId: parseInt(item.UserId, 10), // Convert UserId to an integer
                    LeaveAppliedStatus: item.LeaveAppliedStatus
                }
            }))


        UserArr = transformedArray;

        RoasterDates = roasterDates.map(date => ({
            RosterStartDate: moment(date.RosterStartDate).format("YYYY-MM-DD"),
            StartDate: moment(date.StartDate).format("YYYY-MM-DD"), // Desired format
            EndDate: moment(date.EndDate).format("YYYY-MM-DD"), // Desired format
            EndDatePlusOne: moment(date.EndDate).add(1, "days").format("YYYY-MM-DD") // Desired format
        }));


        // Format ApplicationDate in RoasterArr to remove timezone and time
        const formattedRoasterArr = RoasterArr.map(item => ({
            ...item,
            ApplyDate: item.ApplicationDate ? moment(item.ApplicationDate).format("YYYY-MM-DD") : null
        }));

        body = {
            UserArr,
            RoasterDates,
            RoasterArr: formattedRoasterArr
        }

        let leaveFlag = await leaveCondition(body);

        return leaveFlag;
    }
    catch (e) {
        return ex;
    }
}

const leaveCondition = async (body) => {

    const url = conf.UnifyValidationApi + "/api-endpoint/RMS/LeaveApprovalFlow"
    // const body =
    // {
    //     "UserArr": [
    //         {
    //             "UserObj": {
    //                 "UserId": 1,
    //                 "LeaveAppliedStatus": 0
    //             }
    //         },
    //         {
    //             "UserObj": {
    //                 "UserId": 2,
    //                 "LeaveAppliedStatus": 1
    //             }
    //         },
    //         {
    //             "UserObj": {
    //                 "UserId": 3,
    //                 "LeaveAppliedStatus": 0
    //             }
    //         }
    //     ],
    //     "RoasterDates": [
    //         {
    //             "StartDate": "2025-01-01",
    //             "EndDate": "2025-01-10"
    //         }
    //     ],
    //     "RoasterArr": [
    //         {
    //             "ShrinkageType": 40,
    //             "ShrinkagePercentage": 40,
    //             "ApplicationDate": "2025-01-11",
    //             "LeaveCategoryId": 2
    //         }
    //     ]

    // }
    const headers = {
        "api-token": conf.UnifyApiToken,
        "content-type": 'application/json'
    }

    try {
        const response = await axios.post(url, body, { headers });
        if (response.status === 200) {
            return response.data;
        }
        return null;

    } catch (e) {
        console.error(e);
        throw new Error(`Error in leaveCondition: ${e.message}`);
    }
}


const InsertLeaves = async (req, res) => {

    let params = [];
    params.push({ key: "UserId", value: req.user.userId });
    params.push({ key: "LeaveCategoryId", value: req.body.leaveCategoryId });
    params.push({ key: "LeaveType", value: req.body.leaveType });
    params.push({ key: "LeaveDate", value: req.body.leaveDate });
    params.push({ key: "Comment", value: req.body.agentComments });

    let response = []
    response = await sqlHelper.sqlProcedure("L", "[Rms].[InsertAdvisorLeaves]", params);
    return response;
}