
import React from "react";
import FooterVersion from "../views/PbSurvey/Agent/Components/FooterVersion";



class School extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      backgroundColor: "black",
      activeColor: "info",
      inMatrix: true,
      user: {},
      location: '',
    };
    this.mainPanel = React.createRef();
  }

  render() {
    const {element} = this.props;
    return (
      <div className="wrapper">
        <div ref={this.mainPanel} className="PBSchoolUI">
         {element}
         <FooterVersion/>
        </div>
      </div>
    );
  }
}

export default School;
