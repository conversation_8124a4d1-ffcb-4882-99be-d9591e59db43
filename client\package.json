{"name": "PBRms", "version": "1.0.0", "private": true, "homepage": "/", "dependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fullcalendar/daygrid": "^6.1.8", "@fullcalendar/react": "^6.1.8", "@fullcalendar/timegrid": "^6.1.8", "@mui/icons-material": "^5.14.1", "@mui/lab": "^5.0.0-alpha.140", "@mui/material": "^5.14.5", "@mui/x-date-pickers": "^6.11.2", "@reduxjs/toolkit": "^1.9.5", "autosuggest-highlight": "^3.3.4", "axios": "^1.4.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "dayjs": "^1.11.9", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "formik": "^2.4.2", "fullcalendar": "^6.1.8", "history": "^5.3.0", "lodash": "^4.17.21", "moment": "^2.29.4", "node-sass": "^9.0.0", "notistack": "^3.0.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-datepicker": "^4.16.0", "react-dom": "^18.2.0", "react-material-ui-form-validator": "^3.0.1", "react-perfect-scrollbar": "^1.5.8", "react-redux": "^8.1.2", "react-router": "^6.15.0", "react-router-dom": "^6.15.0", "react-scripts": "^5.0.1", "react-select": "^5.7.4", "redux": "^4.2.1", "redux-thunk": "^2.4.2", "shortid": "^2.2.16", "underscore": "^1.13.6", "xlsx": "^0.18.5", "yup": "^1.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "install:clean": "rm -rf node_modules/ && rm -rf package-lock.json && npm install && npm start", "compile-sass": "node-sass src/assets/scss/paper-dashboard.scss src/assets/css/paper-dashboard.css", "minify-sass": "node-sass src/assets/scss/paper-dashboard.scss src/assets/css/paper-dashboard.min.css --output-style compressed", "map-sass": "node-sass src/assets/scss/paper-dashboard.scss src/assets/css/paper-dashboard.css --source-map true"}, "eslintConfig": {"extends": "react-app"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "optionalDependencies": {"jquery": "3.6.0", "typescript": "4.6.2"}, "devDependencies": {"@babel/runtime": "^7.22.6", "axios-mock-adapter": "^1.21.5", "babel-plugin-import": "^1.13.8", "cross-env": "^7.0.3", "customize-cra": "^1.0.0", "gh-pages": "^5.0.0", "react-app-rewired": "^2.2.1"}}