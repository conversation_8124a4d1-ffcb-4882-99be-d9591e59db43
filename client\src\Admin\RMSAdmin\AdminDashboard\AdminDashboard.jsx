import React, { useState, useEffect, useRef } from 'react';
import Select from 'react-select';
import { connect } from "react-redux";
import { DownloadRotaData, GetCommonData, GetCommonspData, GetCommonspDataV2 } from '../../store/actions/CommonAction';
import moment from 'moment';
import "../../app/css/AttendanceDashboard.scss"
import "../AdminDashboard/adminDashboard.scss"
import { Grid, styled, Checkbox } from '@mui/material';
import Loader from '../Components/Loader';
import _ from 'underscore';
import ShrinkageBreakdown from '../Components/ShrinkageBreakdown';
import { getRMSUser, onexport } from '../../../Agent/utility/utility';
import { ALL_SUPERVISORS, ALL_PROCESS } from '../Common/Constants';
import { GetAgentAttendanceOnDate, GetAllManagerIds } from '../Common/Utilities';
import DownloadIcon from '@mui/icons-material/Download';

import Tooltip, { tooltipClasses } from '@mui/material/Tooltip';
import CommentPopup from './CommentPopup';

const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    border: '2px solid #000',
    boxShadow: 24,
    p: 4,
};


const HtmlTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: '#f5f5f9',
        color: 'rgba(0, 0, 0, 0.87)',
        maxWidth: 220,
        fontSize: theme.typography.pxToRem(12),
        border: '1px solid #dadde9',
    },
}));


const AdminDashboard = (props) => {
    const label = { inputProps: { 'aria-label': 'Checkbox demo' } };
    const [rosterMaster, setRosterMaster] = useState([]);
    const [processMaster, setProcessMaster] = useState([]);
    const [product, setProduct] = useState([]);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [selectedRoster, setSelectedRoster] = useState(null);
    const [selectedProcess, setSelectedProcess] = useState(0);
    const [user, setUser] = useState(null);
    const [supervisorData, setSupervisorsData] = useState([]);
    const [selectedSupervisor, setSelectedSupervisor] = useState(null);
    const [agentLeavesData, setAgentLeavesData] = useState(null);
    const [attendanceAgentWiseData, setAttendanceAgentWiseData] = useState(null);
    const [rosterDates, setRosterDates] = useState([]);
    const [leavesDateWise, setLeavesDateWise] = useState({});
    const [attendanceDateWiseData, setAttendanceDateWiseData] = useState({});
    const [isLoading, setIsLoading] = useState(true);
    const refAgents = useRef([]);
    const [processData, setProcessData] = useState(null);
    const currentDate = moment(new Date()).format('YYYY-MM-DD');
    const [rosterId, setRosterId] = useState(null);
    const [anchorEl, setAnchorEl] = React.useState(null);
    const open = Boolean(anchorEl);
    const [openModal, setOpenmodal] = React.useState(false);

    const handleModalOpen = () => {
        setOpenmodal(true);
    }
    const handleModalClose = () => {
        setOpenmodal(false);
    }

    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };

    const HandleChangeRoster = (e) => {
        setSelectedRoster(e);
        let startDate = moment(e?.value?.StartDate?.substring(0, 10));
        let endDate = moment(e?.value?.EndDate?.substring(0, 10));
        let currentDate = startDate.clone();
        let dates = [];
        while (currentDate.isSameOrBefore(endDate)) {
            dates.push(currentDate.format('YYYY-MM-DD'))
            currentDate.add(1, 'day');
        }
        setRosterDates(dates);
        setRosterId(e.value.Id);
    }

    const HandleChangeProduct = (data) => {
        setSelectedProduct(data);
    }
    const GetAllProcess = (e) => {
        console.log(e)
    }

    const HandleChangeProcess = (data) => {
        // setSelectedProcess(data);
        if (data?.value === ALL_PROCESS) {
            setSelectedProcess(data);
            // let managerIds = GetAllManagerIds(supervisorData);
            // setSelectedSupervisor({ ...data, value: managerIds });
            // if (data.Process && data.Process.length > 0) {
            //   let managerIds = GetAllProcess(data.Process);
            //   console.log({ ...data, value: managerIds });
            //   setSelectedProcess({ ...data, value: managerIds });
            // } else {
            //   let managerIds = GetAllProcess(supervisorData);
            //   setSelectedProcess({ ...data, value: managerIds });
            //   console.log({ ...data, value: managerIds });
            // }
        } else {
            setSelectedProcess(data);

        }
    }

    const HandleChangeSupervisor = (data) => {
        if (data?.value === ALL_SUPERVISORS) {
            // let managerIds = GetAllManagerIds(supervisorData);
            // setSelectedSupervisor({ ...data, value: managerIds });
            if (data.supervisors && data.supervisors.length > 0) {
                let managerIds = GetAllManagerIds(data.supervisors);
                setSelectedSupervisor({ ...data, value: managerIds });
            } else {
                let managerIds = GetAllManagerIds(supervisorData);
                setSelectedSupervisor({ ...data, value: managerIds });
            }
        } else {
            setSelectedSupervisor(data);

        }
    }

    const GetRosterData = () => {
        props.GetCommonData({
            limit: 10,
            skip: 0,
            root: 'RosterMaster',
            cols: ["TOP(10) Id AS Id", "StartDate AS StartDate", "EndDate AS EndDate"],
            order: ["Id"],
            con: [{ "IsActive": 1 }],
            c: "R",
        }, function (data) {
            const FormattedRoster = Array.isArray(data) && data.map(roster => {
                let status = "";
                let start = moment(roster?.StartDate).format("YYYY-MM-DD");
                let end = moment(roster?.EndDate).format("YYYY-MM-DD");
                if (moment(start).isSameOrBefore(currentDate) &&
                    moment(end).isSameOrAfter(currentDate)) {
                    status = " -[ONGOING]"
                }

                let label = moment(roster?.StartDate).format('DD MMM YYYY') + ' - ' +
                    moment(roster?.EndDate).format('DD MMM YYYY') + status;
                return {
                    label,
                    value: roster
                }
            }) || [];

            setRosterMaster(FormattedRoster);
            setSelectedRoster(FormattedRoster[0] || null);
            HandleChangeRoster(FormattedRoster[0] || {})
            setIsLoading(false);
        })
    }

    const GetProductData = () => {
        props.GetCommonData({
            limit: 10,
            skip: 0,
            root: 'ProcessMaster',
            cols: ["ProcessId", "ProcessName", "ProductId", "ProductName"],
            order: ["ProcessName"],
            direction: ['ASC'],
            con: [{ "IsActive": 1 }],
            c: "R",
        }, function (data) {
            debugger;

            setProcessData(data);
            let ProductList = _.uniq(data, (item) => {
                return item.ProductId
            })

            const FormattedProduct = Array.isArray(ProductList) && ProductList.map(product => {
                if (product?.ProductId) {
                    return { label: product?.ProductName, value: product?.ProductId }
                } else {
                    return { label: "NA", value: 0 }
                }
            }) || [];

            setProduct(FormattedProduct);
            setIsLoading(false);
        })
    }

    const GetProcessData = () => {
        if (processData && processData.length > 0) {
            let processArr = [];
            for (let i = 0; i < processData.length; i++) {
                if (selectedProduct && selectedProduct?.value && processData[i].ProductId == selectedProduct.value) {
                    processArr.push(processData[i]);
                }
            }

            let FormattedProcess = Array.isArray(processArr) && processArr.map(process => {
                if (process?.ProcessId) {
                    return { label: process?.ProcessName + " (Product - " + process?.ProductName + ")", value: process?.ProcessId }
                } else {
                    return { label: "NA", value: 0 }
                }
            }) || [];

            FormattedProcess = [
                { label: "ALL PROCESS", value: ALL_PROCESS },
                ...FormattedProcess
            ];
            setProcessMaster(FormattedProcess);
            HandleChangeProcess({ label: "ALL PROCESS", value: ALL_PROCESS, label: "ALL PROCESS", Process: FormattedProcess })

            setIsLoading(false);
        }
    }

    const GetSupervisorData = (ProductId, ProcessId) => {

        if (ProcessId?.value == 'ALL_PROCESS') {
            ProcessId.value = 0;
        }
        if (ProductId?.value || ProcessId?.value) {
            setIsLoading(true);
            props.GetCommonspData({
                root: 'GetSupervisorsByProcessAndProduct',
                c: "R",
                // , ProcessId: param?.value || 0
                params: [{ ProductId: ProductId?.value || 0, ProcessId: ProcessId?.value, RosterId: rosterId }],

            }, function (response) {
                if (response.status === 200) {
                    const data = response?.data?.data?.[0] || [];
                    let FormattedSupervisorData = Array.isArray(data) && data.map(item => {
                        return { label: item?.ManagerName + '-' + item?.ManagerEmployeeId, value: item?.ManagerId || 0 }
                    }) || [];

                    FormattedSupervisorData = [
                        { label: "ALL SUPERVISORS", value: ALL_SUPERVISORS },
                        ...FormattedSupervisorData
                    ];
                    setSupervisorsData(FormattedSupervisorData);
                    HandleChangeSupervisor({ label: "ALL SUPERVISORS", value: ALL_SUPERVISORS, label: "ALL SUPERVISORS", supervisors: FormattedSupervisorData })

                } else {
                    //
                }
                setIsLoading(false);
            })
        }
    }


    useEffect(() => {

        if (selectedSupervisor?.value && selectedRoster?.value?.Id || user?.RoleId === 12) {

            setIsLoading(true);
            setLeavesDateWise({});
            setAgentLeavesData({});
            setAttendanceAgentWiseData({});
            setAttendanceDateWiseData({});
            props.GetCommonspDataV2({
                root: 'GetAgentLeavesByRoster',
                c: "R",
                params: [{
                    RoleId: user?.RoleId || 0,
                    ManagerIds: selectedSupervisor?.value || '',
                    RosterId: parseInt(selectedRoster?.value?.Id),
                    ProcessId: parseInt(selectedProcess?.value || 0)
                }],

            }, function (errorStatus, response) {
                if (!errorStatus) {
                    const appliedLeavesData = response?.data && response.data[0] || [];
                    const attendanceData = response?.data && response.data[1] || [];
                    const agentsData = response?.data && response.data[2] || [];
                    agentsData?.sort((a, b) => {
                        const nameA = a.EmployeeName?.toUpperCase(); // ignore upper and lowercase
                        const nameB = b.EmployeeName?.toUpperCase(); // ignore upper and lowercase
                        if (nameA < nameB) {
                            return -1;
                        }
                        if (nameA > nameB) {
                            return 1;
                        }

                        // names must be equal
                        return 0;
                    });

                    refAgents.current = agentsData;
                    let leavesAgentWise = _.groupBy(appliedLeavesData, 'EmployeeId') || {}
                    let leavesApplicationDateWise = _.groupBy(appliedLeavesData, (item) => {
                        let date = moment(item?.ApplicationDate || '').format('YYYY-MM-DD')
                        return date;
                    });

                    let attendanceAgentWise = _.groupBy(attendanceData, 'EmployeeId') || {}
                    let attendanceDateWise = _.groupBy(attendanceData, (item) => {
                        let date = moment(item?.AttendanceDate || '').format('YYYY-MM-DD')
                        return date;
                    });

                    setLeavesDateWise(leavesApplicationDateWise);
                    setAgentLeavesData(leavesAgentWise);

                    setAttendanceAgentWiseData(attendanceAgentWise);
                    setAttendanceDateWiseData(attendanceDateWise);
                }
                setIsLoading(false);
            })
        }

    }, [selectedRoster, selectedSupervisor])

    useEffect(() => {
        const data = getRMSUser();
        setUser(data);
        GetRosterData();
        GetProductData();
    }, []);

    useEffect(() => {
        refAgents.current = [];
        setSelectedProcess()
        GetProcessData();
    }, [selectedProduct])

    useEffect(() => {
        if (selectedProduct) {
            refAgents.current = [];
            setSupervisorsData([])
            setSelectedSupervisor(null)
            setLeavesDateWise({});
            setAgentLeavesData({});

            setAttendanceAgentWiseData({});
            setAttendanceDateWiseData({});
            GetSupervisorData(selectedProduct, selectedProcess)
        }
    }, [selectedProduct, selectedProcess, rosterId]);

    const ContentBox = styled('div')(({ theme }) => ({
        margin: '20px',
        [theme.breakpoints.down('sm')]: { margin: '16px' },
    }));

    const exportToExcel = (tableID, filename = '') => {
        var downloadLink;
        var dataType = 'application/vnd.ms-excel';
        var tableSelect = document.getElementById(tableID);
        var tableHTML = tableSelect.outerHTML.replace(/ /g, '%20');

        // Specify file name
        filename = filename ? filename + '.xls' : 'excel_data.xls';

        // Create download link element
        downloadLink = document.createElement("a");

        document.body.appendChild(downloadLink);

        if (navigator.msSaveOrOpenBlob) {
            var blob = new Blob(['\ufeff', tableHTML], {
                type: dataType
            });
            navigator.msSaveOrOpenBlob(blob, filename);
        } else {
            // Create a link to the file
            downloadLink.href = 'data:' + dataType + ', ' + tableHTML;

            // Setting the file name
            downloadLink.download = filename;

            //triggering the function
            downloadLink.click();
            handleClose();
        }
    }

    const exportData = () => {
        if (selectedProduct && selectedProduct.value) {
            DownloadRotaData(rosterId, selectedProduct.value, function (results) {
                if (results && results.data && results.data.status == 200) {
                    // setdownload(false)
                    onexport(results.data.results, "Rota")
                    handleClose();
                }
            });
        }
        else {
            // alert("Please Select Product");
            handleModalOpen();
            handleClose();
        }

    }
    

    return (
        <>
            <ContentBox className="analytics">

                <Grid container spacing={2}>
                    <Grid item xs={12} md={10}>
                        <p className="Caption">DASHBOARD</p>
                        <h2 className="heading">Leave Management System</h2>
                    </Grid>


                    <Grid item xs={12} md={2}>
                        <img src="/Lms/calendar.png" className="calendarIcon" />
                        <span className="day">  TODAY</span>
                        <h3 className="calendar">{moment().format('DD MMM, YYYY')}</h3>
                    </Grid>

                </Grid>


                <Grid container spacing={2}>
                    <Grid item xs={12} md={3}>
                        <label className="RosterLabels">Select Roster</label>
                        <Select
                            options={rosterMaster}
                            onChange={(e) => HandleChangeRoster(e)}
                            components={{
                                IndicatorSeparator: () => null
                            }}
                            className="RosterMaster"
                            value={selectedRoster}
                        />
                    </Grid>
                    {
                        user?.RoleId && ![12].includes(user.RoleId) && <>
                            <Grid item xs={12} md={2}>
                                <label className="RosterLabels">Select Product</label>

                                <Select
                                    options={product}
                                    onChange={(e) => HandleChangeProduct(e)}
                                    components={{
                                        IndicatorSeparator: () => null
                                    }}
                                    className="RosterMaster"
                                    value={selectedProduct}
                                />
                            </Grid>

                            <Grid item xs={12} md={2}>
                                <label className="RosterLabels">Select Process</label>

                                <Select
                                    options={processMaster}
                                    onChange={(e) => HandleChangeProcess(e)}
                                    components={{
                                        IndicatorSeparator: () => null
                                    }}
                                    className="RosterMaster"
                                    value={selectedProcess}
                                />
                            </Grid>

                            <Grid item xs={12} md={2}>
                                <label className="RosterLabels">Select Supervisor</label>

                                <Select
                                    options={supervisorData}
                                    onChange={(e) => HandleChangeSupervisor(e)}
                                    components={{
                                        IndicatorSeparator: () => null
                                    }}
                                    className="RosterMaster"
                                    value={selectedSupervisor}
                                />
                            </Grid>


                            <Grid item xs={12} md={1} >

                                <HtmlTooltip className="downloadDropdown"
                                    title={
                                        <React.Fragment  >
                                            <p onClick={() => exportToExcel("TableAttendanceDashboard")}>RosterData</p>
                                            <p onClick={exportData}>ROTA</p>
                                        </React.Fragment>
                                    }
                                >
                                    <button className="downloadBtn"
                                        aria-controls={open ? 'basic-menu' : undefined}
                                        aria-haspopup="true"
                                        aria-expanded={open ? 'true' : undefined}
                                    //onClick={handleClick}

                                    >
                                        <DownloadIcon></DownloadIcon></button>
                                </HtmlTooltip>
                            </Grid>
                        </>
                    }


                </Grid>

                {
                    isLoading ? <Loader /> :

                        <Grid container spacing={2}>
                            <Grid item xs={12} md={12}>
                                <div className="AttendanceDashboard">
                                    <table id="TableAttendanceDashboard">
                                        <thead>
                                            <tr>
                                                <th colSpan="2">Employee Details</th>
                                                <th colSpan="7">Week 1</th>
                                                <th colSpan="7">Week 2</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><b>Emp. Code</b></td>
                                                <td><b>User Name</b></td>
                                                <td className="HideColumn"><b>TL Name</b></td>
                                                <td className="HideColumn"><b>TL Ecode</b></td>
                                                {Array.isArray(rosterDates) && rosterDates.map((date, ind) =>
                                                    <td key={ind} className={date <= moment().format('YYYY-MM-DD') ? 'AttendanceDate' : ''}><Checkbox {...label} className="AllcheckBoxTick" /> {moment(date).format('DD MMM YY')} <p className="day"> {moment(date).format('ddd')}  </p> </td>
                                                )}
                                            </tr>
                                            {
                                                Array.isArray(refAgents.current) && refAgents.current?.map((item, ind1) => {
                                                    let agentInfo = item;
                                                    const { EmployeeId, EmployeeName, UserType, TLName, TLEcode } = agentInfo;

                                                    return <tr key={ind1}>
                                                        <td>{EmployeeId || '-'}</td>
                                                        {/* <td>{EmployeeName?.substring(0, 20) + '...' || '-'}</td> */}
                                                        <td>{EmployeeName || '-'}{UserType == 'GOLD' || UserType == 'SILVER' ? '*' : ''}</td>
                                                        <td className="HideColumn">{TLName || '-'}</td>
                                                        <td className="HideColumn">{TLEcode || '-'}</td>

                                                        {Array.isArray(rosterDates) && rosterDates.map((date, ind2) => {
                                                            let data = agentLeavesData[EmployeeId] || [];

                                                            let isPresent = GetAgentAttendanceOnDate(attendanceAgentWiseData[EmployeeId], date);

                                                            let isLeave = false, leaveType = '';
                                                            let isDatePast = moment(date).isSameOrBefore(currentDate)

                                                            for (let index = 0; index < data.length; index++) {
                                                                const element = data[index];
                                                                const applicationDate = moment(element?.ApplicationDate).format('YYYY-MM-DD');
                                                                if (date === applicationDate) {
                                                                    isLeave = true;
                                                                    leaveType = element.LeaveTypeId;
                                                                }
                                                            }

                                                            if (isLeave) {
                                                                switch (leaveType) {
                                                                    case 1:
                                                                        return <td className={isPresent ? "weeklyOf AgentUnplanned" : "weeklyOf"}>  {isPresent ? "W/O / P" : "W/O"}</td>
                                                                    case 2:
                                                                        return <td className={isPresent ? "earnedLeave AgentUnplanned" : "earnedLeave"}> <Checkbox {...label} className="checkBoxTick" /> {isPresent ? "EL / P" : "EL"}</td>
                                                                    case 3:
                                                                        return <td>E. W/O</td>
                                                                    case 4:
                                                                        return <td> <Checkbox {...label} className="checkBoxTick" /> SL</td>
                                                                    case 5:
                                                                        return <td className={isPresent ? "causalLeave AgentUnplanned" : "causalLeave"} >{isPresent ? "CL / P" : "CL"}</td>
                                                                    // default:
                                                                    //   return <td className='AgentPresent'>P</td>
                                                                }

                                                            } else {
                                                                if (isDatePast && isPresent !== null) {
                                                                    return <td className={isPresent ? "AgentPresent" : "AgentAbsent"} >{isPresent ? "P" : "AB"}</td>
                                                                } else {
                                                                    // return <td className={isPresent ? "AgentPresent" : "AgentAbsent" } >{isPresent ? "P" : "AB" }</td>
                                                                    return <td className='AgentPresent'>P</td>
                                                                }

                                                            }
                                                        })
                                                        }
                                                    </tr>
                                                })
                                            }

                                            <ShrinkageBreakdown
                                                rosterDates={rosterDates}
                                                leavesDateWise={leavesDateWise}
                                                agentLeavesData={agentLeavesData}
                                                agentsData={refAgents.current}
                                                attendanceAgentWiseData={attendanceAgentWiseData}
                                                attendanceDateWiseData={attendanceDateWiseData}
                                            />

                                        </tbody>
                                    </table>
                                </div>
                            </Grid>
                            <Grid item xs={12} md={12}>
                                <div className="footerBtn">
                                 <span>
                                 Neque porro quisquam est qui dolorem ipsum quia dolor sit amet, consectet
                                 </span>   
                                <button className="Approvebtn">Approve</button>
                                <button className="FreezeBtn">Decline</button>                              
                                 <button className="AddCommentBtn">Add Comment</button>
                                
                                </div>
                            </Grid>
                        </Grid>
                }
            </ContentBox>
            <CommentPopup/>
        </>
    )
}

export default connect(
    null,
    {
        GetCommonData,
        GetCommonspData,
        GetCommonspDataV2
    }
)(AdminDashboard);