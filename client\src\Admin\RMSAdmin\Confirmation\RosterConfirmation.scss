@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');
$TextColor: #253858E3;
$fontRoboto: Roboto;
$fontPoppins: Poppins;
$boxShadow: 0px 3px 12px 0px rgba(0, 101, 255, 0.16);
$lightTextColor: #25385899;
$blueColor: #0065FF;

::-webkit-scrollbar {
    width: 3px;
    border-radius: 3px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #ece9e9;
}

::-webkit-scrollbar-thumb:hover {
    background: #c7c5c5;
}

@mixin headingFontSize {
    font-family: $fontPoppins;
    font-size: 36px;
    font-weight: 900;
    line-height: 54px;
    text-align: left;
    color: $TextColor;
    letter-spacing: normal;
}

@mixin fontsize12 {
    font-size: 12px;
    font-style: normal;
    line-height: 16px;
}

@mixin normalfont {
    font-size: 14px;
    font-style: normal;
    line-height: 20px;
}

@mixin flax {
    display: flex;
    justify-content: center;
    align-items: center;
}

@mixin fontSize16 {
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
}

.width130px {
    width: 130px;
}

.theme-light {
    background-color: #F7FBFF;
}

.marginBotton0 {
    margin-bottom: 0px;
}

.alignCenter {
    text-align: center;
}

.TextUppercase {
    text-transform: uppercase;
}

.mt-0 {
    margin-top: 0px !important;
}

.BlueBgColor {
    border-radius: 8px;
    background-color: #DDECFF !important;
    color: $TextColor;
    font-family: $fontPoppins;
    @include normalfont;
    font-weight: 600;

    /* 142.857% */
    fieldset {
        border: none;
    }
}

.scrollbar {
    overflow-y: auto;
    height: 26vh;
}

label {
    font-family: $fontPoppins;
    @include fontsize12;
    font-weight: 600;
    display: block;
    margin-bottom: 6px;
    color: $TextColor;
    letter-spacing: normal;
}

.MuiOutlinedInput-input {
    padding: 14px;
}

.Active {
    background-color: $blueColor !important;
    color: #fff;

    p {
        color: #fff !important;
    }

    .badge {
        color: $blueColor !important;
        background-color: #fff !important;
    }
}

.transferPopup {
    h4 {
        margin: 5px 0px;
        color: $TextColor;
        font-family: $fontPoppins;
        @include fontSize16;
        line-height: 24px;
    }

    h2 {
        color: $TextColor;
        font-family: $fontPoppins;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: 32px;
        padding: 25px 28px 10px;
    }

    .sendTransferbutton {
        color: #FFF;
        text-align: center;
        font-family: $fontPoppins;
        @include normalfont;
        background-color: $blueColor;
        border: none;
        border-radius: 8px;
        box-shadow: $boxShadow;
        margin: 0px auto 10px;
        height: 50px;
        padding: 16px 32px;
        cursor: pointer;
    }

    .MuiPaper-rounded {
        border-radius: 8px;
        max-width: 425px;
    }

    .MuiDialogContent-root {
        padding: 10px 30px 20px;
    }
}

.RemovePopup {
    h2 {
        color: $TextColor;
        font-family: $fontPoppins;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: 32px;
        padding: 25px 28px 10px;
    }

    .RemoveRequestbutton {
        color: #FFF;
        text-align: center;
        font-family: $fontPoppins;
        @include normalfont;
        border-radius: 8px;
        background: #E44A4A;
        border: none;
        width: 100%;
        box-shadow: $boxShadow;
        margin: 20px auto 10px;
        height: 50px;
        padding: 16px;
        cursor: pointer;
    }

    .MuiPaper-rounded {
        border-radius: 8px;
        max-width: 380px;
    }

    .MuiDialogContent-root {
        padding: 0px 30px 20px;

        p {
            color: $lightTextColor;
            font-family: Poppins;
            @include normalfont;
            font-weight: 600;
        }
    }

    h5 {
        margin: 0px 0px 20px 0px;
        color: $lightTextColor;
        font-family: $fontPoppins;
        @include fontsize12;
        font-weight: 400;
        display: flex;

        &::before {
            height: 16px;
            width: 2px;
            background-color: #B9CBFF;
            content: "";
            display: block;
            margin-right: 3px;
        }

        b {
            font-weight: 600;
            color: $TextColor;
            margin-right: 5px;
        }
    }



}

.AddAdvisorPopup {
    h2 {
        color: $TextColor;
        font-family: $fontPoppins;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: 32px;
        padding: 25px 28px 10px;
    }

    .MuiPaper-rounded {
        border-radius: 8px;
        max-width: 380px;
        max-height: calc(100% - 30px);
    }

    .EmiDetails {
        @include flax;
        justify-content: space-between !important;

        svg {
            cursor: pointer;
        }
    }

    .EMIName {
        font-family: $fontRoboto;
        color: $lightTextColor;
        line-height: normal;
        letter-spacing: 0.24px;
        margin-bottom: 0px;
        margin-top: 5px;
        @include fontSize16;
    }

    .EmiCode {
        @include flax;
        justify-content: right !important;
        color: $blueColor;
        font-family: $fontRoboto;
        @include fontSize16;
        font-weight: 500 !important;
        line-height: normal;
        letter-spacing: 0.24px;
        margin-bottom: 0px;
        margin-top: 5px;

        svg {
            width: 12px;
            margin-left: 5px;
            cursor: pointer;
        }
    }

    .sendTransferbutton {
        color: #FFF;
        text-align: center;
        font-family: $fontPoppins;
        @include normalfont;
        background-color: $blueColor;
        border: none;
        border-radius: 8px;
        box-shadow: $boxShadow;
        margin: 0px auto 10px;
        height: 50px;
        padding: 16px 32px;
        cursor: pointer;
    }
}

.MuiTooltip-tooltip {
    background-color: #000DB2;
    color: #F7FBFF;
    font-family: $fontPoppins;
    @include fontsize12;
    font-weight: 500;
    padding: 7px 12px;
    border-radius: 4px;

    .MuiTooltip-arrow {
        color: #000DB2;
    }
}

.ApproveRequestPopup {
    h2 {
        color: $TextColor;
        font-family: $fontPoppins;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: 32px;
        padding: 25px 28px 10px;
    }

    .MuiPaper-rounded {
        border-radius: 8px;
        max-width: 438px;
    }

    .MuiDialogContent-root {
        padding: 0px 30px 20px;
        p {
            color: $lightTextColor;
            font-family: Poppins;
            @include normalfont;
            font-weight: 600;
        }

        .ApproveRequestDetails {
            border-radius: 6px;
            background: #F9F9F9;
            color: $lightTextColor;
            font-family: $fontRoboto;
            @include normalfont;
            font-weight: 600;   
            padding: 0px 20px 10px 20px;   
            margin: 30px 0px;
            span{
                color: $TextColor;               
            }    
        }
    }
    .Approvebutton {
        color: #FFF;
        text-align: center; 
        font-family: $fontPoppins;
        @include normalfont;
        background-color: $blueColor;
        border: none;
        border-radius: 8px;
        box-shadow: $boxShadow;
        margin: 0px 10px 10px;
        height: 50px;
        padding: 16px 32px;
        cursor: pointer;
    }
    .rejectBtn{
        background-color: #E44A4A !important;
    }
}

.RoasterConfirmation {
    padding: 20px 10px;

    header {
        .Heading {
            @include headingFontSize;
        }

        p {
            margin: 0px;
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
            text-align: left;
            color: $lightTextColor;
            font-family: $fontPoppins;

            b {
                color: $TextColor;
            }
        }
    }

    .HistoryButton {
        width: 100px !important;
        float: right;
        margin-top: 22px;
        justify-content: space-evenly !important;
        cursor: pointer;
    }

    .bgFrame {
        width: 100%;
        height: 48px;
        @include flax;
        cursor: pointer;

        p {
            overflow: hidden;
            color: $TextColor;
            text-overflow: ellipsis;
            font-family: $fontPoppins;
            @include normalfont;
            font-weight: 500;
            width: 80px;
            white-space: nowrap;

        }

        .badge {
            border-radius: 32px;
            background: #6184FF;
            width: 24px;
            height: 24px;
            color: #F7FBFF;
            text-align: center;
            font-family: $fontRoboto;
            @include fontsize12;
            font-weight: 600;
            line-height: normal;
            @include flax;
            margin-left: 5px;
        }
    }
    .MisbgFrame {
        width: 93%;
        height: 48px;
        @include flax;
        cursor: pointer;     
      margin-top: 5px;
        padding: 10px;
        border: none;
        outline: none;
      
        p {
            overflow: hidden;
            color: $TextColor;
            text-overflow: ellipsis;
            font-family: $fontPoppins;
            @include normalfont;
            font-weight: 500;
            width: 80px;
            white-space: nowrap;

        }

        .badge {
            border-radius: 32px;
            background: #6184FF;
            width: 24px;
            height: 24px;
            color: #F7FBFF;
            text-align: center;
            font-family: $fontRoboto;
            @include fontsize12;
            font-weight: 600;
            line-height: normal;
            @include flax;
            margin-left: 5px;
        }
    }
    .Rostertable {

        table {
            th {
                color: $lightTextColor;
                font-family: $fontRoboto;
                @include normalfont;
                font-weight: 600;
                background-color: #F7FBFF;
                padding: 12px;
            }

            td {
                color: $TextColor;
                font-family: $fontRoboto;
                font-size: 15px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
                padding: 12px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .TransferBtn {
                color: $blueColor;
                margin-right: 40px;
            }

            .RemoveBtn {
                color: #E44A4A;
            }
        }
    }

    button {
        background-color: transparent;
        border: none;
        padding: 0px;
        font-family: $fontPoppins;
        font-weight: 600;
        display: inline-flex !important;
        @include normalfont;
        // @include flax;
        cursor: pointer;
        outline: none;

        img {
            margin-right: 5px;
        }

        svg {
            font-size: 20px;
            margin-right: 5px;
        }
    }

    .BottomBtn {
        width: 100%;
        position: fixed;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #FFF 54.5%);
        padding: 24px;
        bottom: 0px;
        left: 0px;
        right: 0px;
        display: flex;
        justify-content: right;

        .AddBtn {
            border-radius: 8px;
            border: 1px solid $blueColor;
            background: #FFF;
            box-shadow: $boxShadow;
            @include normalfont;
            font-family: $fontPoppins;
            font-weight: 500;
            height: 52px;
            padding: 16px 32px;
            cursor: pointer;
            color: $blueColor;

            svg {
                margin-right: 10px;
            }
        }

        .FreezeBtn {
            border-radius: 8px;
            background: #E44A4A;
            @include normalfont;
            font-family: $fontPoppins;
            font-weight: 500;
            margin: 0px 30px 0px 15px;
            height: 52px;
            padding: 16px 32px;
            cursor: pointer;
            color: #fff;
            box-shadow: $boxShadow;
        }
    }
}

.History {
    .StatusButton {
        font-family: $fontRoboto;
        font-weight: 600;
        justify-content: center;
        line-height: 15px;
        border: none;
        border-radius: 8px;
        padding: 8px 10px;
        width: 90px;

    }

    .Pending {
        background-color: #FFEFD6;
        color: #AF5F14;
    }

    .Approved {
        color: #028160;
        background-color: #C9F1E8;
    }

    .Declined {
        color: #810202;
        background-color: #F1C9C9;
    }

    .UndoBtn {
        border-radius: 8px;
        border: 1px solid $blueColor;
        color: $blueColor;
        font-family: $fontRoboto;
        @include normalfont;
        font-weight: 600;
        line-height: normal;
        padding: 6px 20px;
    }

}

.Null {
    color: #25385861 !important;
}

.MisTable {
    table {
        th {
            &:first-child {
                width: 70px;
            }

            &:last-child {
                width: 220px;
            }
        }
    }

    .TransferBtn {
        margin-right: 25px !important;
    }
}
.mt-1{
    margin-top: 1.3rem;
}

.heightspace{
    height:80px;
}