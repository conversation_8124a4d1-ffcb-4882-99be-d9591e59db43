* {
    margin: 0px;
    padding: 0px;
}

@font-face {
    font-family: "Proxima Nova";
    src:url("../../../../public/font/ProximaNovaFont.otf");
    font-weight: normal;
    font-style: normal;
}

body {
    background-color: #f7f7f7 !important;
    box-sizing: border-box;
}

.mainLayout {
    background-color: #f7f7f7;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    flex-wrap: wrap;
    box-sizing: border-box;

    .MiddleLayout {
        width: 1005px;
        height: 560px;
        background: #ffffff 0% 0% no-repeat padding-box;
        box-shadow: 0px 3px 12px #00000029;
        border-radius: 16px;
        opacity: 1;
        margin: auto;
        margin-bottom: 0px;

        // .container {
        //     background: linear-gradient(90deg, #0065ff 39%, #fff 38%);
        //     border-radius: 16px;
        //     box-shadow: 0px 3px 12px #00000029;
        // }
        .LeftSection {
            background: #0065ff 0% 0% no-repeat padding-box;
            border-radius: 16px 0px 0px 16px;
            opacity: 1;
            height: 560px;
            background-position: center;
            background-size: contain;
            background-image: url(../../../../public/image/leftimage.svg);
            background-repeat: no-repeat;

            h1 {
                display: none;
            }
        }

        .quizLeftSection {
            background: #0065ff 0% 0% no-repeat padding-box;
            border-radius: 16px 0px 0px 16px;
            opacity: 1;
            height: 560px;

            .Surveyimg {
                width: 280px;
                margin: auto;
                position: relative;
                margin-top: 3em;

                .Score {
                    text-align: center;
                    font: normal normal 600 40px/49px Proxima Nova;
                    letter-spacing: 0px;
                    color: #ffffff;
                    opacity: 1;
                    top: 0;
                    left: 0;
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    margin: auto;
                    height: 40px;
                }
            }

            p {
                text-align: center;
                font: normal normal 500 16px/20px Proxima Nova;
                letter-spacing: 0px;
                color: #FFFFFF;
                opacity: 1;
                padding-top: 3.5em;
            }

            h3 {
                text-align: center;
                font: normal normal 600 24px/29px Proxima Nova;
                letter-spacing: 0px;
                color: #FFFFFF;
                opacity: 1;
            }
        }

        .rightSection {
            position: relative;

            /*VC screen css */
            .videoCalling {
                width: 425px;
                padding-left: 30px;
                margin-left: 20px;

                .VcHeading {
                    text-align: left;
                    font: normal normal 600 32px/39px Proxima Nova;
                    letter-spacing: 0px;
                    color: #253858;
                    opacity: 1;
                    margin: 50px 20px 15px 0px;
                }

                .caption {
                    text-align: left;
                    font: normal normal bold 18px/22px Proxima Nova;
                    letter-spacing: 0px;
                    color: #253858;
                    opacity: 1;
                    margin: 20px 40px 30px 0px;
                }

                ul {
                    text-align: left;
                    list-style-type: none;

                    li {
                        text-align: left;
                        font: normal normal normal 16px/20px Proxima Nova;
                        letter-spacing: 0px;
                        color: #253858;
                        opacity: 1;
                        margin-bottom: 20px;

                        img {
                            margin-right: 0px;
                        }
                    }
                }

                .checkboxDiv {
                    text-align: left;
                    font: normal normal 600 16px/20px Proxima Nova;
                    letter-spacing: 0px;
                    color: #253858;
                    opacity: 1;
                    display: flex;
                    width: 430px;
                    margin: 45px 0px 25px 0px;

                    input[type="checkbox"] {
                        width: 18px;
                        height: 18px;
                        margin: 3px 12px 0px 0px;
                    }
                }

                .Btndiv {
                    width: 100%;

                    button {
                        background: #0065ff 0% 0% no-repeat padding-box;
                        border-radius: 4px;
                        width: 100%;
                        font: normal normal bold 16px/20px Proxima Nova;
                        letter-spacing: 0.02px;
                        color: #ffffff;
                        opacity: 1;
                        outline: none;
                        border: none;
                        height: 48px;
                        // margin: 8px;
                    }

                    .disable {
                        opacity: 0.4;
                    }
                }

                .Btndivquiz {
                    width: 100%;

                    button {
                        background: #0065ff 0% 0% no-repeat padding-box;
                        border-radius: 4px;
                        width: 45%;
                        font: normal normal bold 16px/20px Proxima Nova;
                        letter-spacing: 0.02px;
                        color: #ffffff;
                        opacity: 1;
                        outline: none;
                        border: none;
                        height: 48px;
                        margin: 8px;
                    }

                    .disable {
                        opacity: 0.4;
                    }
                }
            }

            /*END VC screen css*/

            /*surveryQuestion css*/
            .SurveryQuestionBox {
                padding: 28px 35px 0px 10px;
                position: relative;
                height: 560px;

                .caption {
                    text-align: left;
                    font: normal normal bold 16px/20px Proxima Nova;
                    letter-spacing: 0px;
                    color: #97a7c3;
                    opacity: 1;
                    margin: 15px 0px;
                }

                .mandatoryQues {
                    text-align: center;
                    background-color: #c10000;
                    color: #fff;
                    width: 160px;
                    border-radius: 5px;
                    padding: 2px;
                    margin-left: 10px;
                    font: normal normal 500 14px/20px Proxima Nova;
                    opacity: 1;
                    margin: 15px 0px;
                }

                .QuesText {
                    text-align: left;
                    font: normal normal 600 24px/29px Proxima Nova;
                    letter-spacing: 0px;
                    color: #150aa6;
                    opacity: 1;
                }

                .labeltext {
                    text-align: left;
                    font: normal normal 600 10px/12px Proxima Nova;
                    letter-spacing: 0px;
                    color: #95a5c6;
                    text-transform: uppercase;
                    opacity: 1;
                    margin-top: 0rem;
                    margin-bottom: 15px;
                }

                .mt-5 {
                    margin-top: 4rem;
                }

                .QuesOption {
                    border: 2px solid #150aa6;
                    border-radius: 8px;
                    opacity: 1;
                    height: 50px;
                    padding: 0px 15px;
                    display: flex;
                    align-items: center;
                    margin-bottom: 15px;
                    cursor: pointer;

                    span {
                        background: #e8e8e8 0% 0% no-repeat padding-box;
                        border-radius: 50px;
                        width: 19px;
                        height: 19px;
                        text-align: center;
                        font: normal normal 600 13px/20px Proxima Nova;
                        letter-spacing: 0px;
                        color: #150aa6;
                        opacity: 1;
                    }

                    p {
                        font: normal normal 600 14px/20px Proxima Nova;
                        letter-spacing: 0px;
                        color: #150aa6;
                        opacity: 1;
                        text-align: left;
                        margin: 0px 0px 0px 13px;
                        width: 84%;

                        a {
                            color: #0065ff;
                            text-decoration: none;
                        }
                    }

                    input[type="checkbox"] {
                        width: 18px;
                        height: 18px;
                    }
                }

                .QuesActive {
                    background: #150AA6 0% 0% no-repeat padding-box;
                    border: 2px solid #150AA6;

                    p {
                        color: #fff;
                    }

                    span {
                        background-color: #fff;
                    }
                }

                ::-webkit-input-placeholder {
                    /* Edge */
                    text-align: left;
                    font: italic normal normal 16px/20px Proxima Nova;
                    letter-spacing: 0px;
                    color: #95a5c6;
                    opacity: 1;
                    padding: 10px;
                }

                .previous {
                    font: normal normal 600 14px/17px Proxima Nova;
                    letter-spacing: 0px;
                    color: #150aa6;
                    opacity: 1;
                    text-align: center;
                    margin-top: 20px;
                    cursor: pointer;
                }

                button {
                    background: #ffffff 0% 0% no-repeat padding-box;
                    border: 2px solid #d0d8e7;
                    border-radius: 4px;
                    opacity: 1;
                    font: normal normal bold 14px/17px Proxima Nova;
                    letter-spacing: 0px;
                    color: #d0d8e7;
                    height: 48px;
                    width: 386px;
                    margin: 5px auto;
                }

                .activeBtn {
                    background: #0065ff 0% 0% no-repeat padding-box;
                    border-radius: 4px;
                    opacity: 1;
                    border: none;
                    color: #fff;
                }

                .QuesNo {
                    display: flex;
                    width: 100%;
                    justify-content: space-between;
                    align-items: center;

                    label {
                        text-align: left;
                        font: normal normal 600 16px/20px Proxima Nova;
                        letter-spacing: 0px;
                        color: #707070;
                        opacity: 1;
                    }

                    .progress {
                        width: 330px;
                        height: 5px;
                    }
                }

                textarea {
                    border: 2px solid #150aa6;
                    border-radius: 8px;
                    opacity: 1;
                    width: 100%;
                }

                .InputNumber {
                    border: 2px solid #150aa6;
                    border-radius: 8px;
                    opacity: 1;
                    width: 100%;
                    height: 48px;
                }

                .mt-6 {
                    margin-top: 6rem;
                }

                .footerBtn {
                    position: absolute;
                    left: 0;
                    right: 0;
                    bottom: 0px;
                }

                .webScroll {
                    height: 355px;
                    overflow-y: auto;
                    overflow-x: hidden;
                }

            }

            /*END Survey Question css*/
            /*QuizQuestion css*/
            .QuizQuestionBox {
                padding: 28px 35px 0px 10px;
                position: relative;
                height: 560px;

                .surveyLogo {
                    font: normal normal 600 20px/24px Proxima Nova;
                    letter-spacing: 0px;
                    color: #0065FF;
                    text-transform: capitalize;
                    opacity: 1;
                    width: 80px;
                    display: flex;
                    align-items: center;
                    justify-content: space-around;
                    margin: 0em auto 20px;

                    img {
                        width: 30px;
                    }
                }

                .heading {
                    font: normal normal bold 16px/20px Proxima Nova;
                    letter-spacing: 0px;
                    color: #23365B;
                    opacity: 1;
                    border-bottom: 1px solid #23365B;
                }

                .caption {
                    text-align: left;
                    font: normal normal bold 16px/20px Proxima Nova;
                    letter-spacing: 0px;
                    color: #97a7c3;
                    opacity: 1;
                    margin: 15px 0px;
                }

                .quizScroll {
                    height: 385px;
                    overflow-y: auto;
                    overflow-x: hidden;
                }

                .QuesText {
                    font: normal normal 600 24px/29px Proxima Nova;
                    letter-spacing: 0px;
                    color: #23365B;
                    opacity: 1;
                }

                .correctMsg {
                    font: normal normal normal 14px/17px Proxima Nova;
                    letter-spacing: 0px;
                    color: #9B9B9B;
                    opacity: 1;
                    margin-bottom: 5px;
                    position: relative;
                    top: -8px;
                }

                .wrongMsg {
                    font: normal normal normal 14px/17px Proxima Nova;
                    text-align: right;
                    letter-spacing: 0px;
                    color: #9B9B9B;
                    opacity: 1;
                    margin-bottom: 5px;
                    position: relative;
                    top: -8px;
                }

                .QuesOption {
                    background: #FFFFFF 0% 0% no-repeat padding-box;
                    border: 2px solid #95A5C6;
                    border-radius: 8px;
                    opacity: 1;
                    height: 40px;
                    padding: 0px 15px;
                    display: flex;
                    align-items: center;
                    margin-bottom: 15px;

                    p {
                        font: normal normal 600 14px/20px Proxima Nova;
                        letter-spacing: 0px;
                        color: #95A5C6;
                        opacity: 1;
                        text-align: left;
                        margin: 0px 0px 0px 13px;
                        width: 84%;
                    }

                    span {
                        background: #e8e8e8 0% 0% no-repeat padding-box;
                        border-radius: 50px;
                        width: 19px;
                        height: 19px;
                        text-align: center;
                        font: normal normal 600 13px/20px Proxima Nova;
                        letter-spacing: 0px;
                        color: #95A5C6;
                        opacity: 1;
                    }

                }

                .QuesMCorrect {
                    border: 2px solid #206F13;
                    cursor: pointer;

                    p {
                        color: #206F13;
                    }

                    span {
                        background-color: #007500;
                        color: #fff;
                    }

                    input[type="checkbox"] {
                        width: 18px;
                        height: 18px;
                        accent-color: #007500;
                    }
                }

                .QuesMWrong {
                    border: 2px solid #C10000;

                    p {
                        color: #C10000;
                    }

                    span {
                        background-color: #C10000;
                        color: #fff;
                    }

                    input[type="checkbox"] {
                        width: 18px;
                        height: 18px;
                        content: "x";
                        color: #e8e8e8;
                        accent-color: #C10000;
                    }
                }

                .labeltext {
                    text-align: left;
                    font: normal normal 600 10px/12px Proxima Nova;
                    letter-spacing: 0px;
                    color: #95a5c6;
                    text-transform: uppercase;
                    opacity: 1;
                    margin-top: 0rem;
                    margin-bottom: 15px;
                }

                .pbquizBtn {
                    background: #0065FF 0% 0% no-repeat padding-box;
                    border-radius: 4px;
                    opacity: 1;
                    border: none;
                    outline: none;
                    font: normal normal 500 14px/17px Proxima Nova;
                    letter-spacing: 0px;
                    color: #FFFFFF;
                    padding: 10px;
                    width: 295px;
                    margin: 8px auto auto;

                }

            }

            /*QuizQuestion css*/
        }

        /*Start Survey Complete css*/
        .SurveyCompleteSection {
            padding: 25px;

            .pblogo {
                margin-top: 20px;
            }

            .Surveyimg {
                width: 280px;
                margin: auto;
                position: relative;

                .Score {
                    text-align: center;
                    font: normal normal 600 40px/49px Proxima Nova;
                    letter-spacing: 0px;
                    color: #ffffff;
                    opacity: 1;
                    top: 0;
                    left: 0;
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    margin: auto;
                    height: 40px;
                }
            }

            h3 {
                font: normal normal bold 24px/29px Proxima Nova;
                letter-spacing: 0px;
                color: #150aa6;
                opacity: 1;
                text-align: center;
            }

            p {
                font: normal normal 600 16px/20px Proxima Nova;
                letter-spacing: 0px;
                color: #253858;
                opacity: 1;
                text-align: center;
            }

            .surveyBtn {
                margin: 2rem 0rem;
                width: 100%;
                text-align: center;

                button {
                    background: #150aa6 0% 0% no-repeat padding-box;
                    border-radius: 4px;
                    font: normal normal 600 14px/17px Proxima Nova;
                    letter-spacing: 0px;
                    color: #ffffff;
                    opacity: 1;
                    outline: none;
                    border: none;
                    width: 288px;
                    height: 48px;
                }

                #fos_home {
                    display: none;
                }
            }
        }

        .loadingText {
            text-align: center;
            font: normal normal 600 23px/17px Proxima Nova;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 566px;

        }

        /*END Survey Complete css*/

        /*logo  css*/
        .pblogo {
            width: 100px;
            display: flex;
            margin: auto;
            align-items: center;
            height: 40px;
            margin-top: 40px;
            justify-content: space-evenly;

            img {
                width: 36px;
            }

            h4 {
                font: normal normal 600 20px/24px Proxima Nova;
                letter-spacing: 0px;
                color: #0065ff;
                text-transform: capitalize;
                opacity: 1;
                margin-bottom: 0px;
            }
        }

        /*END Logo css*/

    }

    /* Start Quiz Dashboard*/
    .QuizDashboard {
        width: 1050px;
        background: #FFFFFF 0% 0% no-repeat padding-box;
        box-shadow: 0px 3px 12px #00000029;
        border-radius: 8px;
        opacity: 1;
        min-height: 100vh;
        border: none;
        padding: 20px 30px;
        // position: absolute;
        // left: 0px;
        // right: 0;
        // bottom: 0px;
        // top: 0px;
        margin: auto;

        .surveyLogo {
            font: normal normal 600 20px/24px Proxima Nova;
            letter-spacing: 0px;
            color: #0065FF;
            text-transform: capitalize;
            opacity: 1;
            width: 80px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            margin: 0em auto 40px;

            img {
                width: 30px;
            }
        }

        .nav-tabs {
            border: none;

            .nav-item {
                margin-right: 20px;

                .nav-link {
                    font: normal normal 600 14px/17px Proxima Nova;
                    letter-spacing: 0px;
                    color: #95A5C6;
                    opacity: 1;
                    background: #fff 0% 0% no-repeat padding-box;
                    padding: 5px 20px;
                    border-radius: 0px;
                    border: none;
                }

                .active {
                    background: #0065FF 0% 0% no-repeat padding-box;
                    color: #fff;
                }
            }
        }

        .tab-content {
            .active {
                background-color: #fff;
            }

            .card {
                background: #FFFFFF 0% 0% no-repeat padding-box;
                box-shadow: 0px 2px 4px #00000029;
                border-radius: 4px;
                opacity: 1;
                border: none;

                .GreenBorder {
                    border-top: 3px solid #94C9A9 !important;

                    .card-text {
                        color: #93C8A8 !important;
                    }
                }

                .orangeBorder {
                    border-top: 3px solid #F78631 !important;

                    .card-text {
                        color: #F78631 !important;
                    }
                }

                .card-body {
                    border-top: 3px solid #9B9B9B;
                    border-radius: 4px 4px 0px 0px;
                    padding: 10px;

                    .card-text {
                        font: normal normal 600 12px/15px Proxima Nova;
                        letter-spacing: 0px;
                        color: #9B9B9B;
                        text-transform: capitalize;
                        opacity: 1;
                        float: left;
                        width: 50%;
                    }

                    .card-title {
                        font: normal normal 600 15px/18px Proxima Nova;
                        letter-spacing: 0px;
                        color: #253858;
                        opacity: 1;
                        margin-bottom: 1.2rem;
                        clear: both;
                    }

                    .percentage {
                        font: normal normal bold 16px/20px Proxima Nova;
                        letter-spacing: 0px;
                        color: #0065FF;
                        opacity: 1;
                        text-align: right;
                    }

                    .quesCount {
                        text-align: left;
                        font: normal normal normal 13px/16px Proxima Nova;
                        letter-spacing: 0px;
                        color: #9B9B9B;
                        opacity: 1;
                    }

                    button {
                        // background: #0065FF 0% 0% no-repeat padding-box;
                        border-radius: 4px;
                        font: normal normal 500 13px/13px Proxima Nova;
                        letter-spacing: 0px;
                        float: right;
                        color: #FFFFFF;
                        opacity: 1;
                        text-transform: capitalize;
                        padding: 7px 17px;
                        border: none;
                        outline: none;
                    }

                    .submitedBtn {
                        background: #9B9B9B 0% 0% no-repeat padding-box;

                        &:hover {
                            background: #9B9B9B 0% 0% no-repeat padding-box;
                        }
                    }
                }
            }
        }
    }

    /* End QuizDahboard */
    .footerVersion {
        width: 100%;
        text-align: center;
    }

    .redtext {
        color: #c10000;
    }
}



.QuesText {
    text-align: left;
    font: normal normal 600 24px/29px Proxima Nova;
    letter-spacing: 0px;
    color: #150aa6;
    opacity: 1;
}

.QuesOption {
    border: 2px solid #150aa6;
    border-radius: 8px;
    opacity: 1;
    height: 50px;
    padding: 0px 15px;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    cursor: pointer;

    span {
        background: #e8e8e8 0% 0% no-repeat padding-box;
        border-radius: 50px;
        width: 19px;
        height: 19px;
        text-align: center;
        font: normal normal 600 13px/20px Proxima Nova;
        letter-spacing: 0px;
        color: #150aa6;
        opacity: 1;
    }

    p {
        font: normal normal 600 14px/20px Proxima Nova;
        letter-spacing: 0px;
        color: #150aa6;
        opacity: 1;
        text-align: left;
        margin: 0px 0px 0px 13px;
        width: 84%;

        a {
            color: #0065ff;
            text-decoration: none;
        }
    }

    input[type="checkbox"] {
        width: 18px;
        height: 18px;
    }
}



@media screen and (min-width: 320px) and (max-width: 640px) {

    .mainLayout {
        align-items: baseline;
        background-color: #fff;
        display:block;
        overflow-x: hidden;
        .QuizDashboard {
            width: 100% ;
            min-height: 100vh ;
            box-shadow: none ;
            padding: 20px 15px;
            .nav-tabs{
                border: 2px solid #0065ff;
                padding-bottom: 0px;
                .nav-item{
                    width:50%;
                    margin-right: 0px;
                    .nav-link{
                        width:100%;
                        height:100%;
                        padding: 8px 20px;
                    }
                }
            }
            .surveyLogo {
                margin: 0px 0px 25px;
            }
        }
        .MiddleLayout {
            width: 100%;
            height: auto;
            margin-top: 0;
            overflow: hidden;
            box-shadow: none;
            border-radius: 0;

            // .container {
            //     background: linear-gradient(180deg, #6c63ff 32%, #fff 32%);
            //     box-shadow: none;
            //     border-radius: 0;
            // }
            .mobileview {
                display: block !important;

                h1 {
                    position: absolute;
                    bottom: 8px;
                    left: 10px;
                    font: normal normal bold 20px/24px Proxima Nova;
                    letter-spacing: 0px;
                    color: #FFFFFF;
                    opacity: 1;
                    display: block;
                }
            }

            // .BGColorMobile {
            //     background: #fff;
            //     box-shadow: none;
            // }
            .LeftSection {
                display: none;
                border-radius: 0;
                width: 100%;
                height: 229px;
                background-position: center;
                background-size: contain;
                background-image: url(../../../../public/image/leftimage_mobile.svg);
                background-repeat: no-repeat;
                background-color: #6c63ff;
                position: relative;

                // &::after {
                //     content: "PB Survey";
                //     text-align: left;
                //     font: normal normal bold 20px/24px Proxima Nova;
                //     letter-spacing: 0px;
                //     color: #ffffff;
                //     opacity: 1;
                //     position: absolute;
                //     bottom: 20px;
                //     left: 0;
                //     padding: 0px 60px 0px 30px;
                // }
            }

            .quizLeftSection {
                background-color: #fff;
                height: auto;
                padding-top: 15px;

                p {
                    opacity: 1;
                    padding-top: 2em;
                    text-align: center;
                    font: normal normal 600 14px/17px Proxima Nova;
                    letter-spacing: 0px;
                    color: #150AA6;
                }

                h3 {
                    font: normal normal 600 24px/29px Proxima Nova;
                    letter-spacing: 0px;
                    color: #150AA6;
                }

                .Surveyimg {
                    margin-top: 1em;
                    background-position: center;
                    background-size: contain;
                    background-image: url(../../../../public/image/surveyscore.gif);
                    background-repeat: no-repeat;
                    height: 220px;

                    img {
                        display: none;
                    }
                }

            }

            .rightSection {

                .videoCalling {
                    width: 100%;
                    padding: 15px 20px 0px;
                    margin-left: 0;
                    height: 64vh;
                    position: relative;

                    .startsurveybtn {
                        position: fixed;
                        bottom: 25px;
                        left: 0;
                        right: 0;
                        padding: 15px;

                        .checkboxDiv {
                            width: 100%;
                        }
                    }
                }

                .SurveryQuestionBox {
                    width: 100%;
                    padding: 25px 15px;
                    height: 76vh;
                    overflow-y: auto;

                    .QuesNo {
                        justify-content: center;
                        align-items: center;
                        flex-wrap: wrap;

                        label {
                            margin-bottom: 35px;
                        }
                    }

                    .footerBtn {
                        bottom: 25px;
                        position: fixed;
                        background-color: #fff;
                        left: 0;
                        right: 0;
                        padding: 0px 15px 15px;
                    }

                    button {
                        width: 92%;
                    }

                    .mandatoryQues {
                        margin-left: 12px;
                    }

                    .mt-6 {
                        margin-top: 3rem;
                    }
                }

                .QuizQuestionBox {
                    height: 100%;
                    padding-right: 10px;

                    .quizScroll {
                        height: auto;
                        overflow-y: inherit;
                        margin-bottom: 40px;
                    }

                    .pbquizBtn {
                        left: 0px;
                        right: 0px;
                        bottom: 8px;
                        position: fixed;
                    }

                    .surveyLogo {
                        display: none;
                    }
                }
            }

            .VcHeading {
                display: none;
            }

            .pblogo {
                display: none;
            }
        }

        .footerVersion {
            position: fixed;
            bottom: 10px;
            background-color: #fff;
        }
    }

    .webScroll {
        height: auto !important;
        overflow-y: unset !important;

    }

    .surveyBtn {
        #fos_home {
            display: block !important;
            margin: auto;
        }
    }

  
}

@media screen and (max-width: 320px) {
    .LeftSection {
        height: 170px !important;
    }
}

.floatLeft {
    // float: left;
    padding-top: 2px;
    position: relative;
    height: auto;
    width: 100%;
}

.floatLeft:after,
.floatLeft:before {
    display: table;
    content: " ";
}

.floatLeft:after {
    clear: both;
}

.line {
    display: inline-block;
    height: 10px;
    margin-top: 7px;
    margin-bottom: 7px;
    width: 100%;
    background-color: #ede7dc;
    float: left;
    clear: both;
}

.line--trunc {
    max-width: 240px;
    width: 100%;
}

.shimmer {
    background-image: linear-gradient(90deg, #f6f7f9 0, #e9ebee 20%, #f6f7f9 40%, #f6f7f9);
    background-size: 99% 100%;
    background-repeat: no-repeat;
    animation: shimmer 1s linear 1ms infinite backwards;
}

@keyframes shimmer {
    0% {
        background-position: 100% * 5 100%;
    }

    100% {
        background-position: 100% * 100 100%;
    }
}


.flexbox {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    height: 100%;
    align-items: center;
}

.flexbox>div {
    width: 300px;
    height: 130px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 1 0 25%;
    border: none;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    overflow: hidden;
}

.bt-spinner {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: transparent;
    border: 4px solid #222;
    border-top-color: #009688;
    -webkit-animation: 1s spin linear infinite;
    animation: 1s spin linear infinite;
}

@-webkit-keyframes spin {
    -webkit-from {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    -webkit-to {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-webkit-keyframes spin {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes spin {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.Backdrop{ 
    // width: 100%;
    // height: 100vh;
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.7;
    top:0;
    bottom: 0;
    left: 0;
    right:0;
    position: fixed;
    z-index: 9;
}