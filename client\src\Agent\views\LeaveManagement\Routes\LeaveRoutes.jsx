import React, { useEffect, FunctionComponent, Fragment } from 'react';
import {
    Route,
    Routes,
} from 'react-router-dom';
import ApplyLeave from '../ApplyLeave';
import LeaveStatus from '../LeaveStatus';

import { connect } from "react-redux";


const LeaveRoutes = (props) => {

    // let token = localStorage.getItem('PBRMSToken');
    // console.log('token', token);
    let { ApplicationId, EmployeeId, deviceType, processId } = props
    
    return (
        <>
            <Routes>
                <Route path="/" element={<ApplyLeave EmployeeId = {EmployeeId} deviceType = {deviceType} processId = {processId} />} />            
                <Route path="/leaveStatus" element={<LeaveStatus EmployeeId = {EmployeeId} deviceType = {deviceType}/>} />
            </Routes>
        </>
    );

};

function mapStateToProps(state) {
    return {
    };
}

// export default connect<{}, {}, QuizRoutesProps>(mapStateToProps,  {
//     GetCommonspData,
//     GetCommonData
// })(QuizRoutes);

export default connect(mapStateToProps,  {

    })(LeaveRoutes);
