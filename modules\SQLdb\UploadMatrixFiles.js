
const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
var _ = require('underscore');
var fs = require('fs');
var path = require('path');
const moment = require("moment");
const xlsxFile = require('read-excel-file/node');


exports.UploadChatAgentFile = async function (req, res) {
    try {
        console.log(req.body);
        console.log(req.files);
        if (!req.files) {
            return res.status(500).send({ msg: "file is not found" })
        }
        // accessing the file
        const myFile = req.files.myFile;
        xlsxFile(fs.createReadStream(`${myFile.tempFilePath}`)).then((rows) => {
            var _result = [];
            var JsonExcel = {};
            for (var i = 1; i in rows; i++) {
                var obj = {};
                for (j in rows[i]) {
                    obj[rows[0][j]] = rows[i][j];
                }
                var innerObj = {};
                _result.push(obj);
            }
            //console.log("result", _result);

            if (req.body.type == "UserMessages") {

                let response =  UploadMessageData(_result, req.body.UserId);
                response.then(function (result) {
                    console.log('res',result);    

                    res.send({
                        status: 200,
                        data: result
                    });
                })
              
            }
            else if(req.body.type == "LeadCredit"){
                let response =  UploadLeadCreditData(_result, req.body.UserId);
                response.then(function (result) {
                    console.log('res',result);    

                    res.send({
                        status: 200,
                        data: result
                    });
                })
                 
            }
            else if (req.body.type == "FosAllocationPanel") {
                let response =  UploadAllocationData(_result, req.body.UserId);
                response.then(function (result) {  

                    res.send({
                        status: 200,
                        data: result
                    });
                })
              
            }
            else {
                res.send({
                    status: 200,
                    data: _result
                });
            }

        })

    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}

async function UploadMessageData(res, UserId) {
    try {
        console.log(res.length);

        let results = [];

        if (res) {
            for (let index = 0; index < res.length; index++) {
                console.log(res[index].Ecode);
                let sqlparam = [];
                sqlparam.push({ key: "Ecode", value: res[index].Ecode });
                sqlparam.push({ key: "displaymessage", value: res[index].Message });
                sqlparam.push({ key: "Description", value: res[index].Description });
                sqlparam.push({ key: "CreatedBy", value: UserId });
                //sqlparams.push("@" + k);
                let result = await sqlHelper.sqlProcedure("L", "[MTX].[InsertUserMessages]", sqlparam);
                console.log(result);
                results.push(result.recordset[0]);
            }

            return results;
        }


    }
    catch (e) {
        console.log(e);
        return e;

    }
    finally {

    }
}

async function UploadLeadCreditData(res, UserId) {
    try {
        console.log(res.length);

        let results = [];

        if (res) {
            for (let index = 0; index < res.length; index++) {
                console.log(res[index].Ecode);
                let sqlparam = [];
                sqlparam.push({ key: "EmployeeID", value: res[index].EmployeeID });
                sqlparam.push({ key: "LeadID", value: res[index].LeadID });
                sqlparam.push({ key: "UpdatedBy", value: UserId });
                //sqlparams.push("@" + k);
                let result = await sqlHelper.sqlProcedure("L", "[MTX].[Insert_SecondaryUserLeadCredit]", sqlparam);
                console.log(result);
                results.push(result.recordset[0]);
            }

            return results;
        }


    }
    catch (e) {
        console.log(e);
        return e;

    }
    finally {

    }
}



async function UploadAllocationData(res, UserId) {
    try {
        
        let results = [];

        if (res) {
            for (let index = 0; index < res.length; index++) {
                let sqlparam = [];
                sqlparam.push({ key: "EmployeeId", value: res[index].AssignedTo });
                sqlparam.push({ key: "LeadId", value: res[index].LeadID });
                sqlparam.push({ key: "AssignedBy", value: parseInt(UserId) });
                let result = await sqlHelper.sqlProcedure("L", "[MTX].[Fos_UploadLeadAllocation]", sqlparam);
                results.push(result);
            }

            return results;
        }

    }
    catch (e) {
        console.log(e);
        return e;

    }
    finally {

    }
}