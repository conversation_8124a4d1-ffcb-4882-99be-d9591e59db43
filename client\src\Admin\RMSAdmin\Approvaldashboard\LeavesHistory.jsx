import { Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import {
    GetCommonspData, GetCommonspDataV2, updateLeaveCancellation
} from "../../store/actions/CommonAction";
import moment from 'moment';

const LeavesHistory = (props) => {

    const [userData, setUserData] = useState()

    useEffect(() => {
        getLeaves();
    }, [])

    const getLeaves = () => {
        props.GetCommonspData({
            root: 'LeaveStatus',
            c: "R",
            params: [{ UserId: props.userId }],
        }, function (result) {
            setUserData(result?.data?.data[0]);
        })
    }

    return (
        <>
            <TableContainer component={Paper} className="LeaveStatus">
                <Table aria-label="customized table">
                    <TableHead>
                        <TableRow>
                            <TableCell>Sr.No</TableCell>
                            <TableCell>Created On</TableCell>
                            <TableCell>Leave Date</TableCell>
                            <TableCell>Leave Type</TableCell>
                            <TableCell>Status</TableCell>
                            <TableCell>Comments</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                      
                        {userData && userData.map((row, index) => (
                            <TableRow key=''>
                                <TableCell>{index + 1}</TableCell>
                                <TableCell>{moment(row.CreatedOn).format("DD/MM/YYYY")}</TableCell>
                                <TableCell>{moment(row.StartDate).format("DD/MM/YYYY")}</TableCell>
                                {/* <TableCell>{moment(row.EndDate).format("DD/MM/YYYY")}</TableCell> */}
                                <TableCell>{row.LeaveType}</TableCell>
                                <TableCell>{row.StatusName}</TableCell>
                                                <TableCell >
                                  {`${row.Comment ? row.Comment.length > 15 ? row.Comment.slice(0, 15) + "..." : row.Comment : '-'}`}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
        </>
    );
};

// export default LeavesHistory;

function mapStateToProps(state) {
    return {
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData,
        GetCommonspDataV2
    }
)(LeavesHistory);
