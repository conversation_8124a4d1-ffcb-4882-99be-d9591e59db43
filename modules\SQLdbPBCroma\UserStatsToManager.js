
const sqlHelper = require("../../LibsPbCroma/sqlHelper");
const Utility = require("../../LibsPbCroma/Utility");
var _ = require('underscore');

async function UserStatsToManager(req, res) {
    try {

        let query = `Select 	DISTINCT UD3.EmployeeId AS ManagerId,UD3.UserName AS ManagerName,UD3.Email AS ManagerEmail,
                        UD2.EmployeeId AS TLId,UD2.UserName AS TLName,UD2.UserID
                        from MTX.UserStats (NOLOCK) US
                        JOIN CRM.UserDetails (NOLOCK) UD ON UD.UserID = US.UserId
                        JOIN CRM.UserDetails (NOLOCK) UD2 ON UD.ManagerId = UD2.UserId
                        JOIN CRM.UserDetails (NOLOCK) UD3 ON UD2.ManagerId = UD3.UserId
                        where CAST(US.CreatedOn AS DATE) = CAST(GETDATE() AS DATE)
                        ORDER BY UD3.EmployeeId`;


        let result = await sqlHelper.sqlquery("R", query);

        if (result && result.recordsets && result.recordsets.length > 0) {
            let Managers = result.recordset;
            var GroupByManagerId = _.groupBy(Managers, 'ManagerId');

            //let TLs = GroupByManagerId[Object.keys(GroupByManagerId)[0]];
            //SendManagerData(TLs);
            Object.keys(GroupByManagerId).forEach(function (key) {
                let TLs = GroupByManagerId[key];
                SendManagerData(TLs);
                //return false;
            });

            res.send({
                status: 200,
                data: true
            });
        }
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {
        //Utility.sendEmail({});
        return;
    }
}

async function SendManagerData(TLs) {
    let managerinfo = TLs[0];
    if (managerinfo.ManagerEmail.indexOf("policybazaar") > -1) {
        let array_TL = [];
        TLs.forEach(element => {
            array_TL.push(element.UserID);
        });

        let sqlparam = [];
        sqlparam.push({ key: "UserId", value: 0 });
        sqlparam.push({ key: "ManagerId", value: 0 });
        sqlparam.push({ key: "ManagerIds", value: array_TL.join(',') });

        let spresult = await sqlHelper.sqlProcedure("R", "[MTX].[GetUserStats]", sqlparam);


        let Agents = spresult.recordsets[0];
        let Groups = spresult.recordsets[1];

        let table = `Hi <b>` + managerinfo.ManagerName + `</b><br/ >
                    <i>Data of agents whose Talktime is less than 60% of their group average</i>
                    <br/ >
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="font-size: 12px;    font-family: Arial, Helvetica, sans-serif;    border: 1px solid #ddd;    border-bottom: 0;margin-top: 10px;">
                    <thead>
                        <tr>
                        <th align="left" valign="middle" style="font-size: 12px;font-family: Arial, Helvetica, sans-serif; padding: 10px; background: #2388d4; color: #fff;">Employee Id</th>
                        <th align="left" valign="middle" style="font-size: 12px;font-family: Arial, Helvetica, sans-serif; padding: 10px; background: #2388d4; color: #fff;">User Name</th>
                        <th align="left" valign="middle" style="font-size: 12px;font-family: Arial, Helvetica, sans-serif; padding: 10px; background: #2388d4; color: #fff;">Group Name</th>                        
                        <th align="left" valign="middle" style="font-size: 12px;font-family: Arial, Helvetica, sans-serif; padding: 10px; background: #2388d4; color: #fff;">Login Hours(hrs)</th>
                        <th align="left" valign="middle" style="font-size: 12px;font-family: Arial, Helvetica, sans-serif; padding: 10px; background: #2388d4; color: #fff;">Idle Time (min)</th>
                        <th align="left" valign="middle" style="font-size: 12px;font-family: Arial, Helvetica, sans-serif; padding: 10px; background: #2388d4; color: #fff;">Talk Time (min)</th>
                        <th align="left" valign="middle" style="font-size: 12px;font-family: Arial, Helvetica, sans-serif; padding: 10px; background: #2388d4; color: #fff;">Attempts</th>
                        <th align="left" valign="middle" style="font-size: 12px;font-family: Arial, Helvetica, sans-serif; padding: 10px; background: #2388d4; color: #fff;">Unique Dials</th>
                        <th align="left" valign="middle" style="font-size: 12px;font-family: Arial, Helvetica, sans-serif; padding: 10px; background: #2388d4; color: #fff;">Total Breaks</th>
                        <th align="left" valign="middle" style="font-size: 12px;font-family: Arial, Helvetica, sans-serif; padding: 10px; background: #2388d4; color: #fff;">Missed CB</th>
                        <th align="left" valign="middle" style="font-size: 12px;font-family: Arial, Helvetica, sans-serif; padding: 10px; background: #2388d4; color: #fff;">APE (mtd)</th>
                        <th align="left" valign="middle" style="font-size: 12px;font-family: Arial, Helvetica, sans-serif; padding: 10px; background: #2388d4; color: #fff;">BKGS (mtd)</th>
                        <th align="left" valign="middle" style="font-size: 12px;font-family: Arial, Helvetica, sans-serif; padding: 10px; background: #2388d4; color: #fff;">Missed Attempts</th>
                        <th align="left" valign="middle" style="font-size: 12px;font-family: Arial, Helvetica, sans-serif; padding: 10px; background: #2388d4; color: #fff;">Cancel Attempts</th>
                        </tr>
                    </thead>
                    <tbody>`;
        let tr = [];
        Agents.forEach(element => {
            let ttachive = PerformanceCalculation(element, Groups, "TalkTime");

            if (ttachive <= -40) {
                let trstr = `<tr>
                        <td style="font-size: 12px; font-family: Arial, Helvetica, sans-serif; padding: 10px; color: #424242; border-bottom: 1px solid #ddd; background: #efefef;">`+ element.EmployeeId + `</td>
                        <td style="font-size: 12px; font-family: Arial, Helvetica, sans-serif; padding: 10px; color: #424242; border-bottom: 1px solid #ddd; background: #efefef;">`+ element.UserName + `</td>
                        <td style="font-size: 12px; font-family: Arial, Helvetica, sans-serif; padding: 10px; color: #424242; border-bottom: 1px solid #ddd; background: #efefef;">`+ element.GroupName + `</td>                        
                        <td style="font-size: 12px; font-family: Arial, Helvetica, sans-serif; padding: 10px; color: #424242; border-bottom: 1px solid #ddd; background: #efefef;">`+ (element.LoginHours / 60).toFixed(1) + `</td>
                        <td style="font-size: 12px; font-family: Arial, Helvetica, sans-serif; padding: 10px; color: #424242; border-bottom: 1px solid #ddd; background: #efefef;">`+ (element.IdleTime / 60).toFixed(1) + `</td>
                        <td style="font-size: 12px; font-family: Arial, Helvetica, sans-serif; padding: 10px; color: #424242; border-bottom: 1px solid #ddd; background: #efefef;">`+ (element.TalkTime / 60).toFixed(1) + `</td>
                        <td style="font-size: 12px; font-family: Arial, Helvetica, sans-serif; padding: 10px; color: #424242; border-bottom: 1px solid #ddd; background: #efefef;">`+ element.Attempts + `</td>
                        <td style="font-size: 12px; font-family: Arial, Helvetica, sans-serif; padding: 10px; color: #424242; border-bottom: 1px solid #ddd; background: #efefef;">`+ element.UniqueDials + `</td>
                        <td style="font-size: 12px; font-family: Arial, Helvetica, sans-serif; padding: 10px; color: #424242; border-bottom: 1px solid #ddd; background: #efefef;">`+ element.TotalBreaks + `</td>
                        <td style="font-size: 12px; font-family: Arial, Helvetica, sans-serif; padding: 10px; color: #424242; border-bottom: 1px solid #ddd; background: #efefef;">`+ element.MissedCB + `</td>                        
                        <td style="font-size: 12px; font-family: Arial, Helvetica, sans-serif; padding: 10px; color: #424242; border-bottom: 1px solid #ddd; background: #efefef;">`+ element.APE + `</td>
                        <td style="font-size: 12px; font-family: Arial, Helvetica, sans-serif; padding: 10px; color: #424242; border-bottom: 1px solid #ddd; background: #efefef;">`+ element.BKGS + `</td>                        
                        <td style="font-size: 12px; font-family: Arial, Helvetica, sans-serif; padding: 10px; color: #424242; border-bottom: 1px solid #ddd; background: #efefef;">`+ element.MissedAttempts + `</td>                        
                        <td style="font-size: 12px; font-family: Arial, Helvetica, sans-serif; padding: 10px; color: #424242; border-bottom: 1px solid #ddd; background: #efefef;">`+ element.CancelAttempts + `</td>                        
                    </tr>`;
                tr.push(trstr);
            }
        });

        table = table + tr.join('') + "</tbody></table>";


        var mailOptions = {
            from: '<EMAIL>',
            to: managerinfo.ManagerEmail,
            //to: "<EMAIL>",
            bcc: ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
            subject: 'Performance Defaulter Report',
            html: '<div style="padding: 20px; box-sizing: border-box; border: 1px solid #b7b8b9;">' + table + '</div>'
        };
        if (tr.length > 0) {
            Utility.sendEmail(mailOptions);
        }
    }
}

function PerformanceCalculation(row, AvgGroupList, field) {
    let groupid = row.GroupId;
    let grpavg = {}
    if (AvgGroupList.length > 1) {
        AvgGroupList.forEach(item => {
            if (item.GroupId == groupid) {
                grpavg = item;
            }
        });
    }
    else {
        grpavg = AvgGroupList[0];
    }



    let data = ((row[field] / (grpavg[field] == 0 ? 1 : grpavg[field])) - 1) * 100;
    console.log("--------------------------------")
    console.log(row[field])
    console.log(grpavg[field])
    console.log(data)
    console.log("--------------------------------")


    return data;

    // return <>
    //     <i className={color}> {data.toFixed(0)}%</i>
    // </>
}

module.exports = {
    UserStatsToManager: UserStatsToManager
};