
const conf = require("../env_config");
const nodemailer = require('nodemailer');
const moment = require("moment");
const request = require('request');
const https = require('https');
const http = require('http')
const axios = require('axios')

const fs = require('fs');
const path = require('path');
var jsonxml = require('jsontoxml');

const xlsxFile = require('read-excel-file/node');

async function sendEmail(mailOptions) {

  var transporter = nodemailer.createTransport(conf.SMTPGoogle);
  try {
    transporter.sendMail(mailOptions, function (error, info) {
      if (error) {
        console.log(error);
      } else {
        console.log('Email sent: ' + info.response);
      }
    });

  } catch (err) {
    console.log(err);
  } finally {
  }

}

async function fetchAWSfiles(params, bucket) {
  const AWS = require('aws-sdk');
  // Create S3 service object
  const s3 = new AWS.S3();
  // Set the region 
  AWS.config.update({ region: 'ap-south-1' });
  // **DO THIS**:
  //   Replace BUCKET_NAME with the name of the bucket,  FILE_NAME with the name of the file you want to upload (including relative page), and EXPIRATION with the duration in validaty in seconds (e.g 60 *5)
  // const today = moment(Date.now()).format('YYYY-MM-DD');
  // const newdate = moment('2021-01-22').format('YYYY-MM-DD');
  // const myBucket = (today > newdate) ? 'newcctecbuckt' : 'cctecbuckt';
  var myBucket = bucket;
  if (bucket === undefined) {
    myBucket = "newcctecbuckt";
  }
  console.log("bucket",myBucket);
  //const myBucket = 'cctecbuckt'
  const myKey = params
  const signedUrlExpireSeconds = 3600

  const presignedURL = s3.getSignedUrlPromise('getObject', {
    Bucket: myBucket,
    Key: myKey,
    Expires: signedUrlExpireSeconds
  })
  console.log(presignedURL)
  return presignedURL;

}

async function getAWSfiles(key, bucket) {
  const AWS = require('aws-sdk');
  // Create S3 service object
  const s3 = new AWS.S3();
  // Set the region 
  AWS.config.update({ region: 'ap-south-1' });
  // Call S3 to list the buckets
  s3.listBuckets(function(err, data) {
    if (err) {
      console.log("Error", err);
    } else {
      //console.log("Success", data.Buckets);
    }
  });
  // **DO THIS**:
  //   Replace BUCKET_NAME with the name of the bucket,  FILE_NAME with the name of the file you want to upload (including relative page), and EXPIRATION with the duration in validaty in seconds (e.g 60 *5)
  //const myBucket = 'cctecbuckt'
  const myKey = key
  const myBucket = bucket;
  const signedUrlExpireSeconds = 3600
  console.log("key",myKey,"bucket",myBucket);
  const presignedURL = s3.getSignedUrlPromise('getObject', {
    Bucket: myBucket,
    Key: myKey,
    Expires: signedUrlExpireSeconds
  })
  console.log(presignedURL)
  return presignedURL;

}

async function uploadAWSfiles(req, res){
  //console.log(req);
  const myFile = req.files.myFile;
  var filepath = myFile.tempFilePath;
  var filename = req.body.filename;
  var bucketname = req.body.bucketname;
  const today = moment(Date.now()).format('YYYY-MM-DD');

  // Load the AWS SDK for Node.js

  var AWS = require('aws-sdk');
  // Set the region 
  AWS.config.update({region: 'ap-south-1'});

  // Create S3 service object
  const s3 = new AWS.S3();

  // call S3 to retrieve upload file to specified bucket
  var uploadParams = {Bucket: bucketname, Key: '', Body: ''};
  var file = filepath;
  console.log(filepath);
  // Configure the file stream and obtain the upload parameters
  var fs = require('fs');
  var fileStream = fs.createReadStream(file);
  fileStream.on('error', function(err) {
    console.log('File Error', err);
  });
  uploadParams.Body = fileStream;
  var path = require('path');
  var myFileName = filename.split('.').join('-' + Date.now() + '.');

  uploadParams.Key = "UploadApi/"+today+"/"+myFileName;

  // call S3 to retrieve upload file to specified bucket
  var s3upload = s3.upload(uploadParams).promise();
  // return the `Promise`
  var result = await s3upload
      .then(function(data) {
          console.log(data);
          return data;
      })
      .catch(function(err) {
          return err.message;
      });

  return result;

}

async function uploadAWSfilesCommon(filepath, filename, bucketname){
  // Load the AWS SDK for Node.js
  var AWS = require('aws-sdk');
  // Set the region 
  AWS.config.update({region: 'ap-south-1'});

  // Create S3 service object
  const s3 = new AWS.S3();

  // call S3 to retrieve upload file to specified bucket
  var uploadParams = {Bucket: bucketname, Key: '', Body: ''};
  var file = filepath;
  console.log(filepath);
  // Configure the file stream and obtain the upload parameters
  var fs = require('fs');
  var fileStream = fs.createReadStream(file);
  fileStream.on('error', function(err) {
    console.log('File Error', err);
  });
  uploadParams.Body = fileStream;
  var path = require('path');
  uploadParams.Key = filename;

  // call S3 to retrieve upload file to specified bucket
  var s3upload = s3.upload(uploadParams).promise();
  // return the `Promise`
  var result = await s3upload
      .then(function(data) {
          console.log(data);
          return data;
      })
      .catch(function(err) {
          return err.message;
      });

  return result;

}

async function ExcelToXml(myFile, TypeIsXml) {
  try {
    console.log("myFile", myFile);
    console.log("TypeIsXml", TypeIsXml);
    let result = await xlsxFile(fs.createReadStream(myFile)).then((rows) => {
      var _result = [];
      //let response = [];
      var JsonExcel = {};
      for (var i = 1; i in rows; i++) {
        var obj = {};
        for (j in rows[i]) {
          obj[rows[0][j]] = rows[i][j];
        }
        // var innerObj = {};
        // innerObj['Data'] = obj;
        _result.push(obj);
      }
      //JsonExcel['xml'] = _result;
      //console.log("result", JsonExcel);

      return _result;

    })

    if (TypeIsXml == true) {
      var xml = jsonxml(result);
      return xml;
    } else {
      return result;
    }
  } catch (e) {
    console.log("Error");
    console.log(e);
  }
  finally {

  }
}


async function fileDownload(url, path, callback) {
  return new Promise(resolve => {
    let response = request(url).pipe(fs.createWriteStream(path));
    response.on('finish', resolve);
  });
}

async function API_GET(url, headers) {
  let options = {};
  if (headers) {
    options = {
      headers: headers
    }
  }
  let p = new Promise((resolve, reject) => {
    https.get(url, options, (resp) => {
      let data = '';

      // A chunk of data has been received.
      resp.on('data', (chunk) => {
        data += chunk;
      });

      // The whole response has been received. Print out the result.
      resp.on('end', () => {
        //console.log(JSON.parse(data));
        try {
          console.log('dataaaaaa',data);
          JSON.parse(data);
          resolve(JSON.parse(data));
        } catch (e) {
          return resolve(data);
        }
      });

    }).on("error", (err) => {
      console.log("Error: " + err.message);
      reject(err);
    });
  });

  return await p;
}

async function http_API_GET(url, headers) {
  let options = {};
  if (headers) {
    options = {
      headers: headers
    }
  }
  let p = new Promise((resolve, reject) => {
    http.get(url, options, (resp) => {
      let data = '';

      // A chunk of data has been received.
      resp.on('data', (chunk) => {
        data += chunk;
      });

      // The whole response has been received. Print out the result.
      resp.on('end', () => {
        //console.log(JSON.parse(data));
        try {
          JSON.parse(data);
          resolve(JSON.parse(data));
        } catch (e) {
          return resolve(data);
        }
      });

    }).on("error", (err) => {
      console.log("Error: " + err.message);
      reject(err);
    });
  });

  return await p;
}

async function API_POST(url, params, headers) {

  let p = new Promise((resolve, reject) => {
    axios.post(
      url, 
      params,
      {
        headers: headers,
      },

      )
      .then(function (response) {
        //console.log("^^^^^^^^^^^^^^^^^^^^^^^^^");
        //console.log(response.data);
        return resolve(response.data);
      })
      .catch(function (error) {
        return resolve(error.response.data);
      });
    
  });

  return await p;
}

module.exports = {
  sendEmail: sendEmail,
  fetchAWSfiles: fetchAWSfiles,
  uploadAWSfiles: uploadAWSfiles,
  getAWSfiles: getAWSfiles,
  fileDownload: fileDownload,
  ExcelToXml: ExcelToXml,
  API_GET: API_GET,
  http_API_GET: http_API_GET,
  API_POST: API_POST,
  uploadAWSfilesCommon: uploadAWSfilesCommon
};
