
const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
var _ = require('underscore');
var fs = require('fs');
var path = require('path');
const moment = require("moment");


//Code By Mohit Jain
async function ProcessUploadIncentivefile(req, res) {
    try {
        console.log("Inside ProcessUploadIncentivefile");
        let sqlparam = [];
        let Productid = parseInt((req.query.ProductId)? req.query.ProductId:'');
        let Incentivemonth = (req.query.IncentiveMonth) ? req.query.IncentiveMonth : '';
        sqlparam.push({ key: "rid", value: req.query.rid });

        let query = `Select * from enc.UploadIncentivefile where id = @rid`
        let result = await sqlHelper.sqlquery("L", query, sqlparam);

        let returnJson = [];

        if (result && result.recordsets && result.recordsets.length > 0) {
            let data = result.recordset[0];
            //console.log("data: ", data);
            if (data.StatusId == 1) {

                //download files
                const today = moment(Date.now()).format('YYYY-MM-DD');
                var appDir = path.dirname(require.main.filename);
                var dir = appDir + "\\download\\" + today + "\\";
                //console.log("dir: ", dir);
                fs.existsSync(dir) || fs.mkdirSync(dir, { recursive: true });
                var filePath = dir + data.FileName;

                let awsfile = await Utility.API_GET(data.filePath);


                await Utility.fileDownload(awsfile.data, filePath, true)

                {
                    let query = `UPDATE enc.UploadIncentivefile set StatusId = 2 where id = @rid`
                    await sqlHelper.sqlquery("L", query, sqlparam);
                }
                let json = await Utility.ExcelToXml(filePath, false);
                //console.log("json: ", json);
                let query = `[enc].[ProcessIncentiveData]`
                let typeid = data.TypeId;
                switch (typeid) {
                    case 9:
                        returnJson = SIData(data, json, query);
                        break;
                    case 14:
                        returnJson = E2EData(data, json, query);
                        break;

                    case 15:
                        switch(Productid){
                        case 117: 
                            returnJson = BookingSectionMotor(data, json, '[enc].[UploadIncentiveBookingsMotor]');
                            break;
                        default:   
                            returnJson = BookingSectionData(data, json, '[enc].[UploadIncentiveBookings]');
                            break;
                        }
                        break;
                    case 16:
                        switch(Productid){
                        case 117: 
                            returnJson = AgentSectionMotor(data, json, '[enc].[UploadIncentiveAgentsMotor]',Incentivemonth,Productid);
                            break;
                        default:   
                            returnJson = AgentSectionData(data, json, '[enc].[UploadIncentiveAgents]',Incentivemonth,Productid);
                            break;
                            }
                      break;
                    case 20:
                            console.log(json);
                            returnJson = InsertAgentProcessDetails(data, json,Incentivemonth,Productid);                        
                      break;
                    

                }

                returnJson.then(async function (result) {
                    console.log(result) // "Some User token"
                    returnJson = result;

                    console.log("results: ", returnJson);
                    {
                        let query = `UPDATE enc.UploadIncentivefile set StatusId = 3 where id = @rid`
                        await sqlHelper.sqlquery("L", query, sqlparam);
                    }
                    //read excel file
                    res.send({
                        status: 200,
                        data: returnJson
                    });
                })

            }
        }


    }
    catch (e) {
        console.log("ProcessUploadIncentivefile", e)
        //console.log(e);
        {
            let query = `UPDATE enc.UploadIncentivefile set StatusId = 1 where id = @rid`
            await sqlHelper.sqlquery("L", query, sqlparam);
        }
        res.send({
            status: 500,
            message: e
        });
    }
    finally {
        //Utility.sendEmail({});
        return;
    }
}


async function SIData(data, json, query) {
    let returnJson = [];

    for (let index = 0; index < json.length; index++) {
        const element = json[index];
        // implement call SP
        let sqlProcessIncentiveDataParam = [];
        sqlProcessIncentiveDataParam.push({ key: "LeadID", value: element.LeadID });
        sqlProcessIncentiveDataParam.push({ key: "type", value: "SI" });
        sqlProcessIncentiveDataParam.push({ key: "value", value: element.IsSI });
        sqlProcessIncentiveDataParam.push({ key: "createdBy", value: data.CreatedBy });
        sqlProcessIncentiveDataParam.push({ key: "status", value: 0, type: "out" });
        sqlProcessIncentiveDataParam.push({ key: "message", value: "", type: "out" });


        let result = await sqlHelper.sqlProcedure("L", query, sqlProcessIncentiveDataParam);
        //console.log("resulttttt", result);
        if (result && result.output) {
            element['status'] = result.output.status
            element['message'] = result.output.message
        }
        let output = { ...element }
        returnJson.push(output);
        //console.log("returnJson", returnJson)
    }

    return returnJson;
}

async function BookingSectionData(data, json, query) {
    let returnJson = [];

    console.log("BookingSectionData", json && json.length)

    for (let index = 0; index < json.length; index++) {

        const element = json[index];
        console.log("BookingSectionData", element);
        // implement call SP
        let sqlProcessIncentiveDataParam = [];
        sqlProcessIncentiveDataParam.push({ key: "BookingId", value: element.BookingID });
        sqlProcessIncentiveDataParam.push({ key: "BookingStatus", value: element.BookingStatus });
        sqlProcessIncentiveDataParam.push({ key: "salesAgent", value: element.AgentId });
        sqlProcessIncentiveDataParam.push({ key: "APE", value: element.APE });
        sqlProcessIncentiveDataParam.push({ key: "WeightedAPE", value: element.WeightedAPE });
        sqlProcessIncentiveDataParam.push({ key: "uploadedBy", value: data.CreatedBy });
        sqlProcessIncentiveDataParam.push({ key: "status", value: 0, type: "out" });
        sqlProcessIncentiveDataParam.push({ key: "message", value: "", type: "out" });


        let result = await sqlHelper.sqlProcedure("L", query, sqlProcessIncentiveDataParam);
        console.log("BookingSectionData", "result", result);

        if (result && result.output) {
            element['status'] = result.output.status
            element['message'] = result.output.message
        }
        let output = { ...element }
        returnJson.push(output);
        //console.log("returnJson", returnJson)
    }

    return returnJson;
}

async function AgentSectionData(data, json, query, Incentivemonth, Productid) {
    let returnJson = [];

    for (let index = 0; index < json.length; index++) {
        const element = json[index];
        // implement call SP
        let sqlProcessIncentiveDataParam = [];
        sqlProcessIncentiveDataParam.push({ key: "Ecode", value: element.ECode });
        sqlProcessIncentiveDataParam.push({ key: "Sourcedbkgs", value: element.Sourcedbkgs });
        sqlProcessIncentiveDataParam.push({ key: "WeightedAPE", value: element.WeightedAPE });
        sqlProcessIncentiveDataParam.push({ key: "superGroupName", value: element.superGroupName });
        sqlProcessIncentiveDataParam.push({ key: "IssuedBkgs", value: element.IssuedBkgs });
        sqlProcessIncentiveDataParam.push({ key: "IssuedAPE", value: element.IssuedAPE });
        sqlProcessIncentiveDataParam.push({ key: "Slab", value: element.Slab });
        sqlProcessIncentiveDataParam.push({ key: "AmountMade", value: element.AmountMade });
        sqlProcessIncentiveDataParam.push({ key: "QualityScore", value: element.QualityScore });
        sqlProcessIncentiveDataParam.push({ key: "FinalIncentive", value: element.FinalIncentive });
        sqlProcessIncentiveDataParam.push({ key: "CRTDeduction", value: element.CRTDeduction });
        sqlProcessIncentiveDataParam.push({ key: "Incentivemonth", value: Incentivemonth });
        sqlProcessIncentiveDataParam.push({ key: "ProductId", value: Productid });
        sqlProcessIncentiveDataParam.push({ key: "uploadedBy", value: data.CreatedBy });
        sqlProcessIncentiveDataParam.push({ key: "status", value: 0, type: "out" });
        sqlProcessIncentiveDataParam.push({ key: "message", value: "", type: "out" });


        let result = await sqlHelper.sqlProcedure("L", query, sqlProcessIncentiveDataParam);
        //console.log("resulttttt", result);
        if (result && result.output) {
            element['status'] = result.output.status
            element['message'] = result.output.message
        }
        let output = { ...element }
        returnJson.push(output);
        //console.log("returnJson", returnJson)
    }

    return returnJson;
}
async function InsertAgentProcessDetails(data, json, Incentivemonth, Productid) {
    // {
    //     EmployeeId: 'PW30622',
    //     Tenure: 2021-10-21T12:00:00.000Z,
    //     FloorProcess: 'Health Dedicated',
    //     TLEmployeeId: 'PW07946',
    //     AMEmployeeId: 'PW07946',
    //     ManagerEmployeeId: 'PW07946',
    //     TargetAPE: 255000,
    //     TargetBookings: 15,
    //     ProductId: 2,
    //     Location: 'Gurgaon'
    //   }
    
    let returnJson = [];

    for (let index = 0; index < json.length; index++) {
        const element = json[index];
        // implement call SP
        let sqlProcessIncentiveDataParam = [];
        sqlProcessIncentiveDataParam.push({ key: "EmployeeId", value: element.EmployeeId });
        sqlProcessIncentiveDataParam.push({ key: "FloorProcess", value: element.FloorProcess });
        sqlProcessIncentiveDataParam.push({ key: "TLEmployeeId", value: element.TLEmployeeId });
        sqlProcessIncentiveDataParam.push({ key: "AMEmployeeId", value: element.AMEmployeeId });
        sqlProcessIncentiveDataParam.push({ key: "ManagerEmployeeId", value: element.ManagerEmployeeId });
        sqlProcessIncentiveDataParam.push({ key: "TargetAPE", value: element.TargetAPE });
        sqlProcessIncentiveDataParam.push({ key: "TargetBookings", value: element.TargetBookings });
        sqlProcessIncentiveDataParam.push({ key: "ProductId", value: Productid });
        sqlProcessIncentiveDataParam.push({ key: "Location", value: element.Location });
        sqlProcessIncentiveDataParam.push({ key: "CreatedBy", value: data.CreatedBy });
        

        let result = await sqlHelper.sqlProcedure("L", "mtx.InsertAgentProcessDetails", sqlProcessIncentiveDataParam);
        //console.log("resulttttt", result);
        if (result && result.output) {
            element['status'] = result.output.status
            element['message'] = result.output.message
        }
        let output = { ...element }
        returnJson.push(output);
        //console.log("returnJson", returnJson)
    }

    return returnJson;
}

async function BookingSectionMotor(data, json, query){
    let returnJson = [];

    for (let index = 0; index < json.length; index++) {
        const element = json[index];
            // implement call SP
            let sqlProcessIncentiveDataParam = [];
            sqlProcessIncentiveDataParam.push({ key: "BookingId", value: element.BookingID });
            sqlProcessIncentiveDataParam.push({ key: "BookingStatus", value: element.BookingStatus });
            sqlProcessIncentiveDataParam.push({ key: "salesAgent", value: element.AgentId });
            sqlProcessIncentiveDataParam.push({ key: "APE", value: element.APE });
            sqlProcessIncentiveDataParam.push({ key: "InsurerCategory", value: element.InsurerCategory });
            sqlProcessIncentiveDataParam.push({ key: "EligFlag", value: element.EligFlag });
            sqlProcessIncentiveDataParam.push({ key: "uploadedBy", value: data.CreatedBy });
            sqlProcessIncentiveDataParam.push({ key: "status", value: 0, type: "out" });
            sqlProcessIncentiveDataParam.push({ key: "message", value: "", type: "out" });

            //console.log('sqlprocessincentivedata',sqlProcessIncentiveDataParam);
            let result = await sqlHelper.sqlProcedure("L", query, sqlProcessIncentiveDataParam);
            console.log("resultbookingmotor", result);
            if(result && result.output){
                element['status'] = result.output.status
                element['message'] = result.output.message
            }
            let output = { ...element}
            returnJson.push(output);
            //console.log("returnJson", returnJson)
        }

        return returnJson;
}

async function AgentSectionMotor(data, json, query, Incentivemonth,Productid){
    let returnJson = [];

    for (let index = 0; index < json.length; index++) {
        const element = json[index];
            // implement call SP
            let sqlProcessIncentiveDataParam = [];
            sqlProcessIncentiveDataParam.push({ key: "Ecode", value: element.ECode });
            sqlProcessIncentiveDataParam.push({ key: "Sourcedbkgs", value: element.Sourcedbkgs });
            sqlProcessIncentiveDataParam.push({ key: "superGroupName", value: element.superGroupName });
            sqlProcessIncentiveDataParam.push({ key: "IssuedBkgs", value: element.IssuedBkgs });
            sqlProcessIncentiveDataParam.push({ key: "AmountMade", value: element.AmountMade });
            sqlProcessIncentiveDataParam.push({ key: "QualityScore", value: element.QualityScore });
            sqlProcessIncentiveDataParam.push({ key: "FinalIncentive", value: element.FinalIncentive });
            sqlProcessIncentiveDataParam.push({ key: "PVT", value: element.PVT });
            sqlProcessIncentiveDataParam.push({ key: "PSU", value: element.PSU });
            sqlProcessIncentiveDataParam.push({ key: "Incentivemonth", value: Incentivemonth });
            sqlProcessIncentiveDataParam.push({ key: "ProductId", value: Productid });
            sqlProcessIncentiveDataParam.push({ key: "uploadedBy", value: data.CreatedBy });
            sqlProcessIncentiveDataParam.push({ key: "status", value: 0, type: "out" });
            sqlProcessIncentiveDataParam.push({ key: "message", value: "", type: "out" });

            //console.log('sqlprocessincentivedata',sqlProcessIncentiveDataParam);
            let result = await sqlHelper.sqlProcedure("L", query, sqlProcessIncentiveDataParam);
            console.log("resultagentmotor", result);
            if(result && result.output){
                element['status'] = result.output.status
                element['message'] = result.output.message
            }
            let output = { ...element}
            returnJson.push(output);
            //console.log("returnJson", returnJson)
        }

        return returnJson;
}

async function E2EData(data, json, query) {
    let returnJson = [];

    for (let index = 0; index < json.length; index++) {
        const element = json[index];
        // implement call SP
        let sqlProcessIncentiveDataParam = [];
        sqlProcessIncentiveDataParam.push({ key: "LeadID", value: element.LeadID });
        sqlProcessIncentiveDataParam.push({ key: "type", value: "E2E" });
        sqlProcessIncentiveDataParam.push({ key: "value", value: element.IsE2E });
        sqlProcessIncentiveDataParam.push({ key: "createdBy", value: data.CreatedBy });
        sqlProcessIncentiveDataParam.push({ key: "status", value: 0, type: "out" });
        sqlProcessIncentiveDataParam.push({ key: "message", value: "", type: "out" });


        let result = await sqlHelper.sqlProcedure("L", query, sqlProcessIncentiveDataParam);
        //console.log("resulttttt", result);
        if (result && result.output) {
            element['status'] = result.output.status
            element['message'] = result.output.message
        }
        let output = { ...element }
        returnJson.push(output);
        //console.log("returnJson", returnJson)
    }

    return returnJson;
}

module.exports = {
    ProcessUploadIncentivefile: ProcessUploadIncentivefile
};