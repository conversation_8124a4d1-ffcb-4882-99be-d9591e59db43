<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="110" height="37" viewBox="0 0 110 37">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_361" data-name="Rectangle 361" width="110" height="37" transform="translate(392 363)" fill="#00173e" stroke="rgba(112,112,112,0.12)" stroke-width="1"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <rect id="Rectangle_360" data-name="Rectangle 360" width="96" height="89.36" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-4">
      <rect id="Rectangle_358" data-name="Rectangle 358" width="91.126" height="89.36" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_228543" data-name="Group 228543" transform="translate(-392 -363)">
    <g id="Rectangle_352" data-name="Rectangle 352" transform="translate(392 363)" fill="#00173e" stroke="rgba(112,112,112,0.12)" stroke-width="1">
      <rect width="110" height="37" stroke="none"/>
      <rect x="0.5" y="0.5" width="109" height="36" fill="none"/>
    </g>
    <g id="Mask_Group_2" data-name="Mask Group 2" clip-path="url(#clip-path)">
      <g id="Group_228489" data-name="Group 228489" transform="translate(355 357.618)">
        <g id="Group_228494" data-name="Group 228494" clip-path="url(#clip-path-2)">
          <g id="Group_228493" data-name="Group 228493" transform="translate(0 0)">
            <g id="Group_228492" data-name="Group 228492" clip-path="url(#clip-path-2)">
              <g id="Group_228491" data-name="Group 228491" transform="translate(0 0)" opacity="0.8">
                <g id="Group_228490" data-name="Group 228490">
                  <g id="Group_228489-2" data-name="Group 228489" clip-path="url(#clip-path-4)">
                    <path id="Path_25" data-name="Path 25" d="M81.219,108.372a1.913,1.913,0,0,1,.345-1.013l33.584-48.035a1.93,1.93,0,0,1,2.682-.477L166,92.353a1.917,1.917,0,0,1,.478,2.675L132.9,143.063a1.93,1.93,0,0,1-2.682.477l-48.17-33.506a1.912,1.912,0,0,1-.823-1.662M166.67,94.008a1.759,1.759,0,0,0-.757-1.529L117.742,58.973a1.775,1.775,0,0,0-2.467.438L81.69,107.447a1.764,1.764,0,0,0,.44,2.461L130.3,143.414a1.775,1.775,0,0,0,2.468-.438L166.353,94.94a1.757,1.757,0,0,0,.317-.933" transform="translate(-75.699 -54.526)" fill="#dadada"/>
                    <path id="Path_26" data-name="Path 26" d="M77.44,104a1.863,1.863,0,0,1,.35-1.008l33.331-46.4a1.883,1.883,0,0,1,2.623-.432L160.28,89.405a1.871,1.871,0,0,1,.434,2.616l-33.331,46.4a1.885,1.885,0,0,1-2.623.432L78.223,105.6A1.869,1.869,0,0,1,77.44,104m83.47-12.99a1.715,1.715,0,0,0-.719-1.476L113.654,56.277a1.728,1.728,0,0,0-2.408.4l-33.331,46.4a1.718,1.718,0,0,0,.4,2.4l46.536,33.254a1.731,1.731,0,0,0,2.408-.4l33.331-46.405a1.71,1.71,0,0,0,.321-.926" transform="translate(-72.177 -52.009)" fill="#dadada"/>
                    <path id="Path_27" data-name="Path 27" d="M73.654,99.632a1.815,1.815,0,0,1,.356-1l33.078-44.775a1.839,1.839,0,0,1,2.564-.387l44.9,33a1.826,1.826,0,0,1,.389,2.557L121.864,133.8a1.837,1.837,0,0,1-2.564.387l-44.9-33a1.827,1.827,0,0,1-.744-1.554m81.49-11.617a1.674,1.674,0,0,0-.682-1.423l-44.9-33a1.684,1.684,0,0,0-2.348.355L74.134,98.72a1.671,1.671,0,0,0,.356,2.342l44.9,33a1.683,1.683,0,0,0,2.349-.355l33.078-44.775a1.661,1.661,0,0,0,.326-.919" transform="translate(-68.648 -49.502)" fill="#dadada"/>
                    <path id="Path_28" data-name="Path 28" d="M69.862,95.232a1.768,1.768,0,0,1,.362-1L103.05,51.089a1.791,1.791,0,0,1,2.5-.342L148.821,83.5a1.779,1.779,0,0,1,.344,2.5l-32.826,43.144a1.793,1.793,0,0,1-2.5.342L70.568,96.732a1.782,1.782,0,0,1-.706-1.5m79.51-10.243a1.631,1.631,0,0,0-.645-1.37L105.461,50.87a1.636,1.636,0,0,0-2.289.313L70.347,94.327a1.626,1.626,0,0,0,.314,2.283l43.267,32.749a1.639,1.639,0,0,0,2.288-.313L149.042,85.9a1.618,1.618,0,0,0,.331-.912" transform="translate(-65.114 -46.961)" fill="#dadada"/>
                    <path id="Path_29" data-name="Path 29" d="M66.065,90.846a1.725,1.725,0,0,1,.369-.994L99.006,48.338a1.745,1.745,0,0,1,2.445-.3l41.631,32.5a1.735,1.735,0,0,1,.3,2.439L110.809,124.49a1.746,1.746,0,0,1-2.445.3l-41.632-32.5a1.741,1.741,0,0,1-.667-1.446m77.531-8.87a1.587,1.587,0,0,0-.608-1.318l-41.632-32.5a1.591,1.591,0,0,0-2.229.271L66.554,89.947a1.568,1.568,0,0,0-.336.906,1.587,1.587,0,0,0,.608,1.318l41.632,32.5a1.591,1.591,0,0,0,2.229-.271L143.26,82.882a1.568,1.568,0,0,0,.336-.905" transform="translate(-61.574 -44.433)" fill="#dadada"/>
                    <path id="Path_30" data-name="Path 30" d="M62.263,86.463a1.675,1.675,0,0,1,.375-.989l32.32-39.884a1.7,1.7,0,0,1,2.386-.252l40,32.244a1.7,1.7,0,0,1,.629,1.391,1.678,1.678,0,0,1-.375.989l-32.32,39.883a1.7,1.7,0,0,1-2.386.252l-40-32.244a1.7,1.7,0,0,1-.629-1.391m75.554-7.5a1.544,1.544,0,0,0-.572-1.265l-40-32.244a1.547,1.547,0,0,0-2.169.229L62.758,85.572a1.524,1.524,0,0,0-.341.9,1.544,1.544,0,0,0,.572,1.265l40,32.244a1.545,1.545,0,0,0,2.169-.229l32.32-39.884a1.522,1.522,0,0,0,.341-.9" transform="translate(-58.031 -41.908)" fill="#dadada"/>
                    <path id="Path_31" data-name="Path 31" d="M58.455,82.051a1.629,1.629,0,0,1,.383-.983L90.906,42.815a1.655,1.655,0,0,1,2.327-.207L131.595,74.6a1.644,1.644,0,0,1,.208,2.321L99.735,115.173a1.657,1.657,0,0,1-2.327.207L59.046,83.389a1.635,1.635,0,0,1-.591-1.338m73.576-6.122a1.482,1.482,0,0,0-.536-1.212L93.134,42.725a1.5,1.5,0,0,0-2.11.188L58.956,81.167a1.491,1.491,0,0,0,.189,2.1l38.363,31.992a1.5,1.5,0,0,0,2.11-.188l32.067-38.253a1.476,1.476,0,0,0,.347-.892" transform="translate(-54.482 -39.355)" fill="#dadada"/>
                    <path id="Path_32" data-name="Path 32" d="M54.638,77.644a1.584,1.584,0,0,1,.391-.979L86.843,40.042a1.611,1.611,0,0,1,2.268-.161l36.728,31.739A1.6,1.6,0,0,1,126,73.881L94.187,110.5a1.609,1.609,0,0,1-2.267.162L55.191,78.926a1.594,1.594,0,0,1-.553-1.283m71.6-4.749a1.442,1.442,0,0,0-.5-1.16L89.01,40a1.456,1.456,0,0,0-2.05.146L55.145,76.765a1.447,1.447,0,0,0,.147,2.045L92.02,110.549a1.456,1.456,0,0,0,2.05-.146L125.885,73.78a1.43,1.43,0,0,0,.354-.885" transform="translate(-50.924 -36.805)" fill="#dadada"/>
                    <path id="Path_33" data-name="Path 33" d="M50.815,73.23a1.559,1.559,0,0,1,.4-.974L82.776,37.263a1.567,1.567,0,0,1,2.208-.116l35.093,31.486a1.557,1.557,0,0,1,.117,2.2L88.633,105.828a1.566,1.566,0,0,1-2.208.116L51.331,74.458a1.553,1.553,0,0,1-.517-1.228m69.625-3.375a1.4,1.4,0,0,0-.466-1.107L84.881,37.261a1.412,1.412,0,0,0-1.99.1L51.329,72.358a1.4,1.4,0,0,0,.105,1.985L86.527,105.83a1.412,1.412,0,0,0,1.991-.1L120.08,70.733a1.4,1.4,0,0,0,.36-.878" transform="translate(-47.361 -34.25)" fill="#dadada"/>
                    <path id="Path_34" data-name="Path 34" d="M46.984,68.81a1.516,1.516,0,0,1,.409-.969L78.7,34.478a1.523,1.523,0,0,1,2.149-.071L114.31,65.641a1.513,1.513,0,0,1,.071,2.143L83.072,101.147a1.525,1.525,0,0,1-2.149.07L47.464,69.983a1.514,1.514,0,0,1-.48-1.174m67.651-2a1.361,1.361,0,0,0-.432-1.055L80.746,34.519a1.369,1.369,0,0,0-1.931.064L47.506,67.945a1.36,1.36,0,0,0,.064,1.926L81.028,101.1a1.37,1.37,0,0,0,1.931-.063l31.309-33.363a1.361,1.361,0,0,0,.367-.871" transform="translate(-43.791 -31.688)" fill="#dadada"/>
                    <path id="Path_35" data-name="Path 35" d="M43.145,64.389a1.469,1.469,0,0,1,.419-.964L74.62,31.693a1.482,1.482,0,0,1,2.089-.025l31.824,30.981a1.471,1.471,0,0,1,.025,2.084L77.5,96.465a1.482,1.482,0,0,1-2.09.025L43.589,65.508a1.474,1.474,0,0,1-.444-1.119m65.678-.627a1.321,1.321,0,0,0-.4-1L76.6,31.778a1.328,1.328,0,0,0-1.872.023L43.674,63.532A1.317,1.317,0,0,0,43.7,65.4L75.52,96.38a1.328,1.328,0,0,0,1.872-.023l31.056-31.732a1.317,1.317,0,0,0,.375-.864" transform="translate(-40.212 -29.126)" fill="#dadada"/>
                    <path id="Path_36" data-name="Path 36" d="M39.293,59.95a1.426,1.426,0,0,1,.43-.96l30.8-30.1a1.44,1.44,0,0,1,2.03.021l30.189,30.729a1.429,1.429,0,0,1-.021,2.025l-30.8,30.1a1.44,1.44,0,0,1-2.03-.021L39.7,61.015a1.437,1.437,0,0,1-.409-1.065M103,60.7a1.28,1.28,0,0,0-.365-.95L72.446,29.017A1.286,1.286,0,0,0,70.634,29L39.83,59.1a1.275,1.275,0,0,0-.018,1.807L70,91.637a1.285,1.285,0,0,0,1.812.018l30.8-30.1A1.272,1.272,0,0,0,103,60.7" transform="translate(-36.622 -26.545)" fill="#dadada"/>
                    <path id="Path_37" data-name="Path 37" d="M35.434,55.493a1.38,1.38,0,0,1,.441-.955L66.426,26.066a1.4,1.4,0,0,1,1.971.067L96.95,56.609a1.387,1.387,0,0,1-.067,1.965L66.333,87.046a1.4,1.4,0,0,1-1.971-.067L35.808,56.5a1.378,1.378,0,0,1-.374-1.01m61.737,2.12a1.225,1.225,0,0,0-.333-.9L68.283,26.238a1.242,1.242,0,0,0-1.753-.06L35.98,54.65a1.235,1.235,0,0,0-.06,1.748L64.475,86.875a1.244,1.244,0,0,0,1.753.059L96.779,58.462a1.228,1.228,0,0,0,.392-.85" transform="translate(-33.025 -23.946)" fill="#dadada"/>
                    <path id="Path_38" data-name="Path 38" d="M31.559,51.041a1.338,1.338,0,0,1,.454-.95l30.3-26.841a1.358,1.358,0,0,1,1.911.113l26.92,30.224a1.348,1.348,0,0,1-.113,1.906L60.73,82.334a1.359,1.359,0,0,1-1.911-.114L31.9,52a1.342,1.342,0,0,1-.34-.956m59.768,3.494a1.187,1.187,0,0,0-.3-.846L64.106,23.464a1.2,1.2,0,0,0-1.694-.1l-30.3,26.841a1.194,1.194,0,0,0-.1,1.689l26.92,30.224a1.2,1.2,0,0,0,1.694.1l30.3-26.841a1.184,1.184,0,0,0,.4-.842" transform="translate(-29.414 -21.353)" fill="#dadada"/>
                    <path id="Path_39" data-name="Path 39" d="M27.677,46.56a1.312,1.312,0,0,1,.467-.946L58.189,20.4a1.317,1.317,0,0,1,1.851.159L85.325,50.534a1.308,1.308,0,0,1-.159,1.846L55.12,77.592a1.318,1.318,0,0,1-1.851-.159L27.984,47.461a1.3,1.3,0,0,1-.308-.9m57.8,4.868a1.152,1.152,0,0,0-.271-.795L59.922,20.662a1.162,1.162,0,0,0-1.634-.141L28.243,45.732a1.155,1.155,0,0,0-.141,1.63L53.387,77.334a1.164,1.164,0,0,0,1.634.14L85.067,52.263a1.159,1.159,0,0,0,.412-.835" transform="translate(-25.795 -18.73)" fill="#dadada"/>
                    <path id="Path_40" data-name="Path 40" d="M23.778,42.075a1.269,1.269,0,0,1,.481-.941L54.052,17.553a1.278,1.278,0,0,1,1.792.206l23.65,29.719a1.27,1.27,0,0,1-.206,1.787L49.495,72.845A1.279,1.279,0,0,1,47.7,72.64L24.053,42.921a1.273,1.273,0,0,1-.275-.846m55.837,6.242a1.116,1.116,0,0,0-.242-.743L55.722,17.854a1.123,1.123,0,0,0-1.575-.181L24.355,41.254a1.116,1.116,0,0,0-.181,1.571l23.65,29.719a1.124,1.124,0,0,0,1.575.181L79.191,49.144a1.117,1.117,0,0,0,.423-.828" transform="translate(-22.161 -16.103)" fill="#dadada"/>
                    <path id="Path_41" data-name="Path 41" d="M19.862,37.585a1.23,1.23,0,0,1,.5-.937L49.9,14.7a1.241,1.241,0,0,1,1.732.253L73.646,44.417a1.232,1.232,0,0,1-.253,1.727l-29.54,21.95a1.242,1.242,0,0,1-1.732-.252L20.106,38.375a1.236,1.236,0,0,1-.244-.791M73.736,45.2a1.081,1.081,0,0,0-.214-.692L51.507,15.042a1.087,1.087,0,0,0-1.516-.221L20.452,36.771a1.078,1.078,0,0,0-.222,1.512L42.246,67.75a1.086,1.086,0,0,0,1.516.221L73.3,46.021a1.076,1.076,0,0,0,.435-.82" transform="translate(-18.512 -13.472)" fill="#dadada"/>
                    <path id="Path_42" data-name="Path 42" d="M15.933,33.07a1.188,1.188,0,0,1,.514-.932l29.287-20.32a1.2,1.2,0,0,1,1.672.3L67.787,41.332a1.2,1.2,0,0,1-.3,1.667L38.2,63.319a1.2,1.2,0,0,1-1.672-.3L16.147,33.805a1.184,1.184,0,0,1-.214-.736m51.914,8.991a1.034,1.034,0,0,0-.186-.641L47.279,12.205a1.05,1.05,0,0,0-1.457-.261L16.535,32.265a1.041,1.041,0,0,0-.261,1.453L36.654,62.932a1.05,1.05,0,0,0,1.457.261L67.4,42.873a1.036,1.036,0,0,0,.448-.812" transform="translate(-14.849 -10.816)" fill="#dadada"/>
                    <path id="Path_43" data-name="Path 43" d="M11.983,28.537a1.164,1.164,0,0,1,.533-.927L41.549,8.92a1.169,1.169,0,0,1,1.612.347L61.907,38.228a1.16,1.16,0,0,1-.347,1.607L32.526,58.525a1.169,1.169,0,0,1-1.611-.346L12.168,29.217a1.155,1.155,0,0,1-.185-.68M61.938,38.9a1,1,0,0,0-.161-.59L43.031,9.35a1.015,1.015,0,0,0-1.4-.3L12.6,27.739a1.011,1.011,0,0,0-.462.8,1,1,0,0,0,.161.59L31.044,58.1a1.015,1.015,0,0,0,1.4.3l29.034-18.69a1.011,1.011,0,0,0,.462-.8" transform="translate(-11.167 -8.141)" fill="#dadada"/>
                    <path id="Path_44" data-name="Path 44" d="M8.012,23.984a1.125,1.125,0,0,1,.553-.922L37.346,6A1.135,1.135,0,0,1,38.9,6.4L56.009,35.106a1.127,1.127,0,0,1-.394,1.547L26.833,53.712a1.135,1.135,0,0,1-1.551-.394L8.17,24.609a1.123,1.123,0,0,1-.158-.625m48,11.74a.97.97,0,0,0-.136-.539L38.765,6.475a.981.981,0,0,0-1.34-.34L8.644,23.194A.973.973,0,0,0,8.3,24.53L25.414,53.24a.98.98,0,0,0,1.34.34L55.536,36.52a.972.972,0,0,0,.477-.8" transform="translate(-7.467 -5.447)" fill="#dadada"/>
                    <path id="Path_45" data-name="Path 45" d="M4.02,19.412a1.1,1.1,0,0,1,.574-.917L33.123,3.066a1.1,1.1,0,0,1,1.491.441L50.09,31.964a1.094,1.094,0,0,1-.442,1.487L21.119,48.88a1.1,1.1,0,0,1-1.491-.442L4.152,19.981a1.092,1.092,0,0,1-.132-.569M50.068,32.527a.937.937,0,0,0-.114-.489L34.478,3.58A.948.948,0,0,0,33.2,3.2L4.667,18.63a.941.941,0,0,0-.38,1.278L19.764,48.365a.948.948,0,0,0,1.282.379L49.574,33.315a.935.935,0,0,0,.494-.788" transform="translate(-3.746 -2.734)" fill="#dadada"/>
                    <path id="Path_46" data-name="Path 46" d="M0,14.815A1.066,1.066,0,0,1,.6,13.9L28.875.1a1.072,1.072,0,0,1,1.43.489L44.147,28.8a1.063,1.063,0,0,1-.49,1.426l-28.276,13.8a1.071,1.071,0,0,1-1.43-.489L.109,15.329A1.064,1.064,0,0,1,0,14.815M44.1,29.3a.91.91,0,0,0-.093-.44L30.166.661A.917.917,0,0,0,28.943.242L.667,14.041a.91.91,0,0,0-.419,1.22l13.842,28.2a.917.917,0,0,0,1.223.418l28.276-13.8A.913.913,0,0,0,44.1,29.3" transform="translate(0 0.004)" fill="#dadada"/>
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
