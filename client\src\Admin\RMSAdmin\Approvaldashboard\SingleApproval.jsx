import React, { useEffect, useState } from 'react';

import { Grid, Table, TableBody, TableContainer, TableCell, TableHead, TableRow, Checkbox, Select, FormControl, InputLabel, MenuItem } from '@mui/material';

import "../Approvaldashboard/ApprovalDashboard.scss"
import CircleIcon from '@mui/icons-material/Circle';
import CommentPopup from './CommentPopup';
import {
  GetCommonData, GetCommonspData, GetCommonspDataV2, updateLeaveApproval, updateLeaveRejection
} from "../../store/actions/CommonAction";
import { connect } from "react-redux";
import moment from 'moment';
import _ from 'underscore';
import Spinner from './../../../Agent/views/LeaveManagement/Spinner';
import RosterSection from "./RosterSection";

const SingleApproval = (props) => {

  const [selectedLeave, setSelectedLeave] = useState([]);
  const [currentRoster, setCurrentRoster] = useState([]);
  // const [nextRoster, setNextRoster] = useState([]);
  const [activeRosterData, setActiveRosterData] = useState([]);
  const [nextRosterData, setNextRosterData] = useState([]);
  const [pastData, setPastData] = useState([]);
  const [upcomingData, setUpcomingData] = useState([]);
  const [employeeName, setEmployeeName] = useState();
  const [employeeId, setEmployeeId] = useState()
  const [tlName, setTlName] = useState();
  const [commentPopUp, setCommentPopUp] = useState();
  const [comments, setComments] = useState();
  const [approvalValue, setApprovalValue] = useState();
  const [loader, setLoader] = useState(false);
  const [leavesData, setLeaveseData] = useState(false);

  useEffect(() => {
    getData();
  }, [])

  const getData = () => {
    let userId = props.userId;
    let managerId = props.managerId;
    // console.log(userId);
    setLoader(true);
    try {
      props.GetCommonspDataV2({
        root: 'GetSingleUsersManagerDetails',
        c: "R",
        params: [{ UserIds: userId, ManagerId: managerId || 0 }],

      }, function (errorStatus, response) {
        if (!errorStatus) {
          // console.log(response.data[0]);
          let leaves = response.data[0];
          let currentRoster = response.data[1];
          // console.log(currentRoster)
          let shrinkageRosterData = response.data[2];
          // let GroupedData = _.groupBy(arr, (item) => {
          //   return item.UserId;
          // })

          for (let i = 0; i < leaves.length; i++) {
            for (let j = 0; j < shrinkageRosterData.length; j++) {
              if (leaves[i].ApplicationDate == shrinkageRosterData[j].ApplicationDate) {
                leaves[i] = {
                  ...leaves[i], currentShrinkage: shrinkageRosterData[j].LeaveApproved,
                  onApprovalShrinkage: 0,
                  totalAgents: shrinkageRosterData[j].TotalAdvisors,
                  weekOffs: shrinkageRosterData[j].WeekOffs,
                }
              }
            }
          }

          // for (let i = 0; i < leaves.length; i++) {
          //   if (leaves[i].ApplicationDate > currentRoster[0].StartDate &&
          //     leaves[i].ApplicationDate < currentRoster[0].EndDate) {
          //     activeRosterData.push(leaves[i]);
          //   }
          //   else if (leaves[i].ApplicationDate < currentRoster[0].StartDate) {
          //     pastRosterData.push(leaves[i]);
          //   }
          //   else if (leaves[i].ApplicationDate > currentRoster[0].EndDate) {
          //     upcomingRosterData.push(leaves[i]);
          //   }
          // }

          // setEmployeeName(leaves && leaves[0]?.EmployeeName)
          // setEmployeeId(leaves && leaves[0]?.EmployeeId)
          // setTlName(leaves && leaves[0]?.TLName)
          // setCurrentRoster(currentRoster);
          getDateSet(leaves, currentRoster);

          // setActiveRosterData(activeRosterData);
          // setPastData(pastRosterData);
          // setUpcomingData(upcomingRosterData)
          // setLeaveseData(leaves);
          setLoader(false);
        } else {
          setLoader(false);
          //
        }
      })
    }
    catch (e) {
      setLoader(false);
    }
  }

  const getDateSet = (leaves, currentRoster) => {
    let activeRosterData = [];
    let pastRosterData = [];
    let upcomingRosterData = [];
    let nextRosterData = [];


    // for (let i = 0; i < leaves.length; i++) {
    //   if (leaves[i].ApplicationDate > currentRoster[0].StartDate &&
    //     leaves[i].ApplicationDate < currentRoster[0].EndDate) {
    //     activeRosterData.push(leaves[i]);
    //   }
    //   else if (leaves[i].ApplicationDate > currentRoster[0].StartDate + 7 &&
    //     leaves[i].ApplicationDate < currentRoster[0].EndDate + 7) {
    //     nextRosterData.push(leaves[i]);
    //   }
    //   else if (leaves[i].ApplicationDate < currentRoster[0].StartDate) {
    //     pastRosterData.push(leaves[i]);
    //   }
    //   else if (leaves[i].ApplicationDate > currentRoster[0].EndDate) {
    //     upcomingRosterData.push(leaves[i]);
    //   }
    // }

    for (let i = 0; i < leaves.length; i++) {
      const appDate = new Date(leaves[i].ApplicationDate);
      const startDate = new Date(currentRoster[0].StartDate);
      const endDate = new Date(currentRoster[0].EndDate);

      const nextStartDate = new Date(startDate);
      nextStartDate.setDate(nextStartDate.getDate() + 14);

      const nextEndDate = new Date(endDate);
      nextEndDate.setDate(nextEndDate.getDate() + 14);

      if (appDate >= startDate && appDate <= endDate) {
        activeRosterData.push(leaves[i]);
      } else if (appDate >= nextStartDate && appDate <= nextEndDate) {
        nextRosterData.push(leaves[i]);
      } else if (appDate < startDate) {
        pastRosterData.push(leaves[i]);
      } else if (appDate > endDate) {
        upcomingRosterData.push(leaves[i]);
      }
    }

    setEmployeeName(leaves && leaves[0]?.EmployeeName)
    setEmployeeId(leaves && leaves[0]?.EmployeeId)
    setTlName(leaves && leaves[0]?.TLName)
    setCurrentRoster(currentRoster);
    setActiveRosterData(activeRosterData);
    setNextRosterData(nextRosterData);
    setPastData(pastRosterData);
    setUpcomingData(upcomingRosterData)
    setLeaveseData(leaves);
  }

  const checkedAllLeaveId = (e, dataFlag) => {
    let agentsData = [];
    let updateArray = [...leavesData]
    if (dataFlag == 'Active Roster') {
      agentsData = [...activeRosterData];
    }
    else if (dataFlag == 'Next Roster') {
      agentsData = [...nextRosterData];
    }
    else if (dataFlag == 'Upcoming Roster') {
      agentsData = [...upcomingData];
    }
    else {
      agentsData = [...pastData];
    }
    let leaveArray = [...selectedLeave];
    if (e.target.checked) {
      for (let i = 0; i < agentsData.length; i++) {
        // leaveArray.push(agentsData[i].Id);
        if (agentsData[i] && agentsData[i].Id && !leaveArray.includes(agentsData[i].Id)) {
          leaveArray.push(agentsData[i].Id);
        }
        for (let j = 0; j < updateArray.length; j++) {
          if (updateArray[j].Id == agentsData[i].Id) {
            updateArray[j].onApprovalShrinkage = updateArray[j].currentShrinkage + 1;
          }
        }
      }
    }
    else {
      // ❗Only remove leave IDs related to current dataFlag group
      leaveArray = leaveArray.filter(id => !agentsData.some(agent => agent.Id === id));

      // ❗Update shrinkage for affected agents only
      for (let i = 0; i < agentsData.length; i++) {
        const agentId = agentsData[i]?.Id;
        for (let j = 0; j < updateArray.length; j++) {
          if (updateArray[j].Id === agentId) {
            updateArray[j].onApprovalShrinkage = updateArray[j].currentShrinkage;
            if (updateArray[j].onApprovalShrinkage === updateArray[j].currentShrinkage) {
              updateArray[j].onApprovalShrinkage = 0;
            }
          }
        }
      }
    }
    // else {
    //   leaveArray = [];
    //   // Remove onApprovalShrinkage updates when unchecked
    //   updateArray.forEach(item => {
    //     item.onApprovalShrinkage = item.currentShrinkage;
    //     if(item.onApprovalShrinkage == item.currentShrinkage){
    //       item.onApprovalShrinkage = 0;
    //     }
    //   });
    // }
    getDateSet(updateArray, currentRoster);
    // setActiveRosterData(updateArray);
    setSelectedLeave(leaveArray);
  }

  const checkedLeaveId = (e) => {
    // console.log(shrinkageData);
    // activeRosterData = [...activeRosterData, { approvalShrinkage: 1 }];

    let updateArray = [...leavesData]

    let checkedApprovalCount = 0;
    let leaveArray = [...selectedLeave];
    if (e.target.checked) {
      // leaveArray.push(e.target.value);
      if (e.target.value && !leaveArray.includes(e.target.value)) {
        leaveArray.push(e.target.value);
      }

      for (let i = 0; i < updateArray.length; i++) {
        if (updateArray[i].Id == e.target.value) {
          updateArray[i].onApprovalShrinkage = updateArray[i].currentShrinkage + 1;
        }
      }
    }
    else {
      // for (let i = 0; i < selectedLeave.length; i++) {
      const index = selectedLeave.indexOf(e.target.value);
      if (index > -1) { // only splice array when item is found
        leaveArray.splice(index, 1); // 2nd parameter means remove one item only
      }
      const updateItem = updateArray.find(item => item.Id == e.target.value);
      if (updateItem) {
        updateItem.onApprovalShrinkage = updateItem.onApprovalShrinkage - 1;
        if (updateItem.onApprovalShrinkage == updateItem.currentShrinkage) {
          updateItem.onApprovalShrinkage = 0;
        }
      }
      // }
    }
    getDateSet(updateArray, currentRoster);
    // setActiveRosterData(updateArray);
    setSelectedLeave(leaveArray);
  }

  const openCommentPopUp = (value) => {
    console.log(selectedLeave);
    if (selectedLeave.length > 0) {
      setApprovalValue(value);
      setCommentPopUp(true);
    }
  }

  const handelApproval = () => {
    if (approvalValue == 2) {
      LeaveApproval(2);
    }
    else {
      LeaveRejection(0);
    }
    setCommentPopUp(false);
    props.setLeaveApproved(true);
  }

  const LeaveApproval = () => {
    setLoader(true);
    // setCommentPopUp(true);
    try {
      // let leaveIds = Object.values(selectedLeave).flat();
      // // console.log(leaveIds);
      // let body = {
      //   leaveIds: leaveIds,
      //   approvalValue: approvalValue,
      //   comments: comments
      // }

      const selectedLeaveIds = Object.values(selectedLeave).flat();
      const totalShrinkageMap = new Map((props.totalShrinkage || []).map(leave => [leave.Id, leave]));

      let leaveData = [];

      // Process each selected leave ID
      selectedLeaveIds.forEach(leaveId => {
        const leave = totalShrinkageMap.get(leaveId);
        if (leave) {
          const weekoffValue = leave.IsFallbackRoster ? leave.StandardShrinkage : leave.weekOffShrinkage;
          leaveData.push({
            leaveId,
            weekoffShrinkage: weekoffValue,
            currentShrinkage: leave.TotalShrinkage - weekoffValue
          });
        } else {
          // If leave not found in totalShrinkage, add with default values
          leaveData.push({
            leaveId,
            weekoffShrinkage: 0,
            currentShrinkage: 0
          });
        }
      });

      const body = {
        leaveData,
        approvalValue,
        comments
      };
      // console.log(body);

      updateLeaveApproval(body, function (results) {
        debugger;
        if (results && results.data && results.data.status == 200) {
          // if (results.data.result = 1) {
          setTimeout(function () {
            setLoader(false);
            getData();
          }, 2000);

          // }
        }
      })
    }
    catch (ex) {
      setLoader(false);
    }
  }

  const LeaveRejection = () => {
    setLoader(true);
    try {
      // let leaveIds = Object.values(selectedLeave).flat();
      // // console.log(leaveIds);
      // let body = {
      //   leaveIds: leaveIds,
      //   approvalValue: approvalValue,
      //   comments: comments
      // }
      const selectedLeaveIds = Object.values(selectedLeave).flat();
      const totalShrinkageMap = new Map((props.totalShrinkage || []).map(leave => [leave.Id, leave]));

      let leaveData = [];

      // Process each selected leave ID
      selectedLeaveIds.forEach(leaveId => {
        const leave = totalShrinkageMap.get(leaveId);
        if (leave) {
          const weekoffValue = leave.IsFallbackRoster ? leave.StandardShrinkage : leave.weekOffShrinkage;
          leaveData.push({
            leaveId,
            weekoffShrinkage: weekoffValue,
            currentShrinkage: leave.TotalShrinkage - weekoffValue
          });
        } else {
          // If leave not found in totalShrinkage, add with default values
          leaveData.push({
            leaveId,
            weekoffShrinkage: 0,
            currentShrinkage: 0
          });
        }
      });

      const body = {
        leaveData,
        approvalValue,
        comments
      };

      // console.log(body);
      updateLeaveRejection(body, function (results) {
        debugger;
        if (results && results.data && results.data.status == 200) {
          // if (results.data.result = 1) {
          setTimeout(function () {
            setLoader(false);
            getData();
          }, 2000);
          // }
        }
      })
    }
    catch (ex) {
      setLoader(false);
    }
  }

  const handleClosePopUp = () => {
    setCommentPopUp(false);
  }
  return (
    <>
      {loader && <div className="Backdrop"> <Spinner /></div>}
      <div className="approvalDashboard">
        <Grid container>
          {/* <Grid item xs={12} md={12}>
            <p className="pageStatus mt-1">Dashboard</p>
            <img src="/Lms/vistalogo.svg" />

          </Grid> */}
          <Grid item xs={12} md={12}>
            {/* {agentsData &&  agentsData.length>0 && agentsData.map(()=>{})} */}
            <div className="multiApproval">
              {employeeName && employeeId && tlName && <div className="RosterDetails">
                <ul>
                  <li>Emp Name <strong>{employeeName}</strong></li>
                  <li>E-Code <strong>{employeeId}</strong></li>
                  <li>TL Name <strong>{tlName}</strong></li>
                </ul>
                {/* <button onClick={openCalendar(activeRosterData[0].EmployeeId)}>Calendar <OpenInNewIcon /></button> */}
              </div>}

              <div className="scrollBar">
                {/* <div className="ActiveRoster">
                  {currentRoster && currentRoster.length > 0 &&
                    <div className="ActiveRosterDate">
                      <CircleIcon />
                      <h4>Active Roster: </h4> <p>{moment(currentRoster[0].StartDate).format('DD MMM YY')} - {moment(currentRoster[0].EndDate).format('DD MMM YY')}</p>
                    </div>}
                  <TableContainer className="ApprovalDetails" style={{ maxHeight: '300px' }}>
                    <Table stickyHeader aria-label="customized table" >
                      <TableHead>
                        <TableRow>
                        <TableCell align="left">
                        {activeRosterData && activeRosterData.length > 0 && 
                        <Checkbox onClick={(e) => checkedAllLeaveId(e, 1)} className="RequestCheck"
                        checked={
                          selectedLeave.length > 0 &&
                // activeRosterData.length === selectedLeave.length &&
                activeRosterData.every(user => selectedLeave.includes(user.Id))             
                        } 
                          /> }
                          Request No.</TableCell>
                          <TableCell align="left">Application Date</TableCell>
                          <TableCell align="left">W/O Shrinkage</TableCell>
                          <TableCell align="left">Planned Shrinkage</TableCell>
                          <TableCell align="left">OverAll Shrinkage</TableCell>
                          <TableCell align="left">Leave Type</TableCell>
                          <TableCell align="left">Comments</TableCell>

                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {activeRosterData && activeRosterData.length > 0 ? (
                          activeRosterData.map((User) => (
                            <TableRow key={User.Id}>
                              <TableCell align="left">
                                <Checkbox
                                  value={User.Id}
                                  onClick={checkedLeaveId}
                                  checked={selectedLeave?.includes(User.Id)}
                                />
                                {User.Id}
                              </TableCell>
                              <TableCell align="left">
                                {moment(User.ApplicationDate).format("DD MMMM")}
                              </TableCell>
                              <TableCell align="left">{(User.weekOffs / User.totalAgents * 100).toFixed(2)} %</TableCell>
                              <TableCell align="left">{(User.currentShrinkage / User.totalAgents * 100).toFixed(2)} %</TableCell>
                              <TableCell align="left">{User.onApprovalShrinkage > 0 ?`${((User.onApprovalShrinkage + User.weekOffs) / User.totalAgents * 100).toFixed(2)} %` : '-'} </TableCell>
                              <TableCell align="left">{User.LeaveType}</TableCell>
                              <TableCell align="left">{User.Comment}</TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell align="center" colSpan={6}>
                              No data available
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>

                  </TableContainer>
                </div> */}
                {/* Active Roster */}
                <RosterSection
                  title="Active Roster"
                  className="ActiveRoster"
                  startDate={currentRoster?.[0]?.StartDate}
                  endDate={currentRoster?.[0]?.EndDate}
                  rosterData={activeRosterData}
                  selectedLeave={selectedLeave}
                  checkedAllLeaveId={checkedAllLeaveId}
                  checkedLeaveId={checkedLeaveId}
                  totalShrinkage={props.totalShrinkage}
                />

                {/* Next Roster */}
                <RosterSection
                  title="Next Roster"
                  className="ActiveRoster UpComingRoster"
                  startDate={moment(currentRoster?.[0]?.StartDate).add(14, "days").format("DD MMM YY")}
                  endDate={moment(currentRoster?.[0]?.EndDate).add(14, "days").format("DD MMM YY")}
                  rosterData={nextRosterData}
                  selectedLeave={selectedLeave}
                  checkedAllLeaveId={checkedAllLeaveId}
                  checkedLeaveId={checkedLeaveId}
                  totalShrinkage={props.totalShrinkage}
                />

                {/* Upcoming Roster */}
                <RosterSection
                  title="Upcoming Roster"
                  className="ActiveRoster UpComingRoster"
                  rosterData={upcomingData}
                  selectedLeave={selectedLeave}
                  checkedAllLeaveId={checkedAllLeaveId}
                  checkedLeaveId={checkedLeaveId}
                  totalShrinkage={props.totalShrinkage}
                />

                {/* Past Roster */}
                <RosterSection
                  title="Past Roster"
                  className="ActiveRoster UpComingRoster"
                  rosterData={pastData}
                  selectedLeave={selectedLeave}
                  checkedAllLeaveId={checkedAllLeaveId}
                  checkedLeaveId={checkedLeaveId}
                  totalShrinkage={props.totalShrinkage}
                />

                {/* <div className="UpComingRoster ActiveRoster">
                  <div className="ActiveRosterDate"><CircleIcon /><h4>Upcoming Roster: </h4></div>
                  <TableContainer className="ApprovalDetails" style={{ maxHeight: '300px' }}>
                    <Table stickyHeader aria-label="customized table" >
                      <TableHead>
                        <TableRow>
                          <TableCell align="left">
                          {upcomingData && upcomingData.length > 0 &&
                          <Checkbox onClick={(e) => checkedAllLeaveId(e, 2)} className="RequestCheck" 
                          // checked={selectedLeave.length == upcomingData.length}
                          checked={
                            selectedLeave.length > 0 &&
                            // upcomingData.length === selectedLeave.length &&
                            upcomingData.every(user => selectedLeave.includes(user.Id))
                          } 
                          />}
                              Request No.</TableCell>
                          <TableCell align="left">Application Date</TableCell>
                          <TableCell align="left">W/O Shrinkage</TableCell>
                          <TableCell align="left">Planned Shrinkage</TableCell>
                          <TableCell align="left">OverAll Shrinkage</TableCell>
                          <TableCell align="left">Leave Type</TableCell>
                          <TableCell align="left">Comments</TableCell>

                        </TableRow>
                      </TableHead>


                      <TableBody>
                        {upcomingData && upcomingData.length > 0 ? (
                          upcomingData.map((User) => (
                            <TableRow key={User.Id}>
                              <TableCell align="left">
                                <Checkbox
                                  value={User.Id}
                                  onClick={checkedLeaveId}
                                  checked={selectedLeave?.includes(User.Id)}
                                />
                                {User.Id}
                              </TableCell>
                              <TableCell align="left">{moment(User.ApplicationDate).format("DD MMMM")}</TableCell>
                              <TableCell align="left">{(User.weekOffs / User.totalAgents * 100).toFixed(2)} %</TableCell>
                              <TableCell align="left">{(User.currentShrinkage / User.totalAgents * 100).toFixed(2)} %</TableCell>
                              <TableCell align="left">{User.onApprovalShrinkage > 0 ?`${((User.onApprovalShrinkage + User.weekOffs) / User.totalAgents * 100).toFixed(2)} %` : '-'} </TableCell>
                              <TableCell align="left">{User.LeaveType}</TableCell>
                              <TableCell align="left">{User.Comment}</TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell align="center" colSpan={6}>
                              No data available
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>


                    </Table>

                  </TableContainer>
                </div> */}



                {/* <div className="UpComingRoster ActiveRoster">
                  <div className="ActiveRosterDate"><CircleIcon /><h4>Past Roster: </h4></div>
                  <TableContainer className="ApprovalDetails" style={{ maxHeight: '300px' }}>
                    <Table stickyHeader aria-label="customized table" >
                      <TableHead>
                        <TableRow>
                        <TableCell align="left">
                        {pastData && pastData.length >  0 && <Checkbox onClick={(e) => checkedAllLeaveId(e, 3)} className="RequestCheck" 
                          // checked={selectedLeave.length == pastData.length}
                          checked={
                            pastData.length > 0 &&
                            // pastData.length === selectedLeave.length &&
                            pastData.every(user => selectedLeave.includes(user.Id))
                          } 
                          /> }
                        Request No.</TableCell>
                          <TableCell align="left">Application Date</TableCell>
                          <TableCell align="left">W/O Shrinkage</TableCell>
                          <TableCell align="left">Planned Shrinkage</TableCell>
                          <TableCell align="left">OverAll Shrinkage</TableCell>
                          <TableCell align="left">Leave Type</TableCell>
                          <TableCell align="left">Comments</TableCell>

                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {pastData && pastData.length > 0 ? (
                          pastData.map((User) => (
                            <TableRow key={User.Id}>
                              <TableCell align="left">
                                <Checkbox
                                  value={User.Id}
                                  onClick={checkedLeaveId}
                                  checked={selectedLeave?.includes(User.Id)}
                                />
                                {User.Id}
                              </TableCell>
                              <TableCell align="left">{moment(User.ApplicationDate).format("DD MMMM")}</TableCell>
                              <TableCell align="left">{(User.weekOffs / User.totalAgents * 100).toFixed(2)} %</TableCell>
                              <TableCell align="left">{(User.currentShrinkage / User.totalAgents * 100).toFixed(2)} %</TableCell>
                              <TableCell align="left">{User.onApprovalShrinkage > 0 ?`${((User.onApprovalShrinkage + User.weekOffs) / User.totalAgents * 100).toFixed(2)} %` : '-'} </TableCell>
                              <TableCell align="left">{User.LeaveType}</TableCell>
                              <TableCell align="left">{User.Comment}</TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell align="center" colSpan={6}>
                              No data available
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>


                    </Table>

                  </TableContainer>
                </div> */}
              </div>
            </div>

            {<CommentPopup open={commentPopUp}
              handleClose={handleClosePopUp} setComments={setComments} handelApproval={handelApproval} />}

          </Grid>
          <Grid item xs={12} md={12}>
            <div className="LeaveApproveFooter">
              {/* <button className="Undobtn disableBtn">Undo</button> */}
              <button className="approvebtn" onClick={() => openCommentPopUp(2)} >Approves</button>
              <button className="RejectedBtn" onClick={() => openCommentPopUp(0)}>Reject</button>
              {/* <button  className="RejectedBtn disableBtn">Freeze</button> */}
            </div>
          </Grid>
        </Grid>
      </div>
    </>
  );
};

// export default SingleApproval;

function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    GetCommonspDataV2
  }
)(SingleApproval);
