import React, { useEffect, useState } from 'react';
import "../../../Admin/RMSAdmin/Confirmation/RosterConfirmation.scss"
import { Container, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography } from '@mui/material';
import { GetCommonData, GetCommonspData, GetCommonspDataV2, UpdateData, DeleteData  } from '../../store/actions/CommonAction';
import { connect } from "react-redux";
import { useLocation } from 'react-router-dom';

const History = (props) => {

    const location = useLocation();
    const [userData, setUserData] = useState([]);
    const [activeClass, setActiveClass] = useState('All');
    const [filterData, setFilterData] = useState([]);
    const HistoryData  = location.state || [];

    // console.log(HistoryData);
    useEffect(() => {
        if(HistoryData && HistoryData?.length == 0){
            TLRosterStatusData();
        }
        else{
            setUserData(HistoryData);
            setFilterData(HistoryData);
        }
        
    }, [])

    const TLRosterStatusData = () => {
        props.GetCommonspDataV2({
            root: 'GetTLRosterStatusData',
            c: "R",
        }, function (errorStatus, response) {
            if (!errorStatus) {
                if (response?.data?.[0] && response?.data?.[0]) {
                    setUserData(response?.data?.[0]);
                    setFilterData(response?.data?.[0]);
                }
            }
        })
    }

    const handleAllData = () => {
        setActiveClass('All');
        setFilterData(userData);
    }

    const handlePendingData = () => {
        setActiveClass('Pending');
        let PendingData = [];
        for(let i = 0; i< userData.length; i++){
            if(userData[i].Status == 3){
                PendingData.push(userData[i]);
            }
        }
        setFilterData(PendingData);
    }

    const handleApprovedData = () => {
        setActiveClass('Approved');
        let PendingData = [];
        for(let i = 0; i< userData.length; i++){
            if(userData[i].Status == 2){
                PendingData.push(userData[i]);
            }
        }
        setFilterData(PendingData);
    }

    const handleRejectedData = () => {
        setActiveClass('Rejected');
        let PendingData = [];
        for(let i = 0; i< userData.length; i++){
            if(userData[i].Status == 0){
                PendingData.push(userData[i]);
            }
        }
        setFilterData(PendingData);
    }

    const undoAdvisor = (TransferId, RosterId) => {
        // let userId = e.target.value;
        props.DeleteData({
            root: 'TransferRequest',
            query: {"Id": TransferId, "RosterId": RosterId}

          }, function (data) {
            if(data.data.status === 200) {
                TLRosterStatusData();
            }
           
          });
    }

    return (
        <div className="RoasterConfirmation">
            <Container maxWidth="xl">
                <header>
                    <Typography variant="h2" className="Heading">History</Typography>
                    <p>All requests sent to MIS from your end</p>
                </header>
                <Grid container spacing={1} mt={2}>
                    <Grid item md={12}>
                        <label className="marginBotton0">Filter by</label>
                    </Grid>
                    <Grid item md={1} sm={2} xs={2}>
                        <button className={"BlueBgColor bgFrame " + (activeClass == 'All' ? "Active":"")} onClick={handleAllData} >All</button>
                    </Grid>
                    <Grid item md={1} sm={2} xs={2}>
                        <button className={"BlueBgColor bgFrame " + (activeClass == 'Pending' ? "Active":"")} onClick={handlePendingData} >Pending </button>
                    </Grid>
                    <Grid item md={1} sm={2} xs={2}>
                        <button className={"BlueBgColor bgFrame " + (activeClass == 'Approved' ? "Active":"")} onClick={handleApprovedData} >Approved</button>
                    </Grid>
                    <Grid item md={1} sm={2} xs={2}>
                        <button className={"BlueBgColor bgFrame " + (activeClass == 'Rejected' ? "Active":"")} onClick={handleRejectedData} >Rejected</button>
                    </Grid>


                    <Grid item xs={12} md={12} sm={12}>

                        <TableContainer className="Rostertable History" sx={{ maxHeight: 320 }}>
                            <Table stickyHeader aria-label="sticky table">
                                <TableHead>
                                    <TableRow>
                                        <TableCell align="left">Employee name</TableCell>
                                        <TableCell align="left">E-Code</TableCell>
                                        <TableCell align="left">Process</TableCell>
                                        <TableCell align="left">Request type</TableCell>
                                        <TableCell align="left">Transfer process to</TableCell>
                                        <TableCell align="left">Comments</TableCell>
                                        <TableCell align="left">Status</TableCell>
                                        {/* <TableCell align="left" className="width130px">Undo</TableCell> */}
                                    </TableRow>
                                </TableHead>
                                <TableBody>


                                    {filterData && filterData.map((users) =>
                                        <TableRow>
                                            <TableCell align="left">{users.EmployeeName}</TableCell>
                                            <TableCell align="left">{users.EmployeeId}</TableCell>
                                            <TableCell align="left">{users.ProcessName}</TableCell>
                                            <TableCell align="left">{users.RequestName}</TableCell>
                                            <TableCell align="left" className="Null">{users.TransferProcessTo}</TableCell>
                                            <TableCell align="left" className="Null" title= {users.MisComments}>{users.MisComments}</TableCell>
                                            <TableCell align="left"><button className="StatusButton Approved">{users.StatusName}</button></TableCell>
                                            {/* {users.Status == 3 && <TableCell align="left">
                                                <button className="UndoBtn" onClick = {() => undoAdvisor(users.TransferId, users.RosterId)}> Undo</button>
                                            </TableCell>} */}
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>

                        </TableContainer>

                    </Grid>
                </Grid>
            </Container>

        </div>
    );
};

// export default History;
export default connect(
    null,
    {
        GetCommonData,
        GetCommonspData,
        GetCommonspDataV2,
        UpdateData,
        DeleteData
    }
)(History);
