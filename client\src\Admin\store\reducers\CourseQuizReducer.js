import { ADD_RESPONSE, ADD_QUIZ, ADD_CURRENT_NESTED_QUESTION, ADD_CORRECT_RESPONSES } from '../actions/QuizActionTypes';
import { cloneDeep } from 'lodash/lang';
import { findIndex } from 'lodash/array';

const InitialState = {
    Questions: [],
    Responses: [],
    CurrentNestedQuestion: {},
    CorrectResponses: []
}

const RemoveExistingNestedResponse = ({
    Responses,
    ParentQuestionId
}) => {
    if (ParentQuestionId) {
        let FilteredResponses = Responses.filter(x => x.ParentQuestionId !== ParentQuestionId)
        let DeletedResponse = Responses.filter(x => x.ParentQuestionId === ParentQuestionId)
        return RemoveExistingNestedResponse({
            Responses: FilteredResponses || [],
            ParentQuestionId: DeletedResponse && DeletedResponse.length > 0 && DeletedResponse[0].QuestionID || null
        });
    } else {
        return Responses;
    }

}

const UpdateResponses = ({
    Responses,
    Payload,
    QuestionID,
    OptionID,
    Type,
    Checked
}) => {
    let index = -1;
    const { ParentQuestionId } = Payload;
    let UpdatedResponses = Responses;
    if (Type === 'single select') {
        UpdatedResponses = RemoveExistingNestedResponse({
            Responses,
            ParentQuestionId
        });
    }

    if (Type == 'multi-select') {
        if (UpdatedResponses && UpdatedResponses.length > 0) {
            index = UpdatedResponses && UpdatedResponses.findIndex(x => x.QuestionID === QuestionID && x.OptionID === OptionID);
        }

        if (index === -1) {
            return [...UpdatedResponses, Payload];
        }

        if (Checked) {
            return [...UpdatedResponses.slice(0, index), Payload, ...UpdatedResponses.slice(index + 1)];
        } else {
            return [...UpdatedResponses.slice(0, index), ...UpdatedResponses.slice(index + 1)];
        }

    } else {

        if (UpdatedResponses && UpdatedResponses.length > 0) {
            index = UpdatedResponses.findIndex(x => x.QuestionID === QuestionID)
        }

        // if no other response is present with QuestionID then it is appended
        if (index === -1) {
            return [...UpdatedResponses, Payload];
        }

        // else the deleted response will kill the child responses recursively
        let deletedResParQuestionId = null;
        if (UpdatedResponses && UpdatedResponses[index]) {
            deletedResParQuestionId = UpdatedResponses[index].QuestionID || null;
        }

        const DeletedChildResponses = RemoveExistingNestedResponse({
            ParentQuestionId: deletedResParQuestionId,
            Responses: UpdatedResponses
        });

        index = -1;
        if (DeletedChildResponses && DeletedChildResponses.length > 0) {
            index = DeletedChildResponses.findIndex(x => x.QuestionID === QuestionID)
        }
        if (index === -1) {
            return [...DeletedChildResponses, Payload];
        }
        return [...DeletedChildResponses.slice(0, index), Payload, ...DeletedChildResponses.slice(index + 1)];

    }
}

function QuizReducer(state = InitialState, action = '') {

    switch (action.type) {

        case ADD_QUIZ: {
            const Payload = action.payload;
            return { ...state, Questions: Payload }
        }

        case ADD_CORRECT_RESPONSES: {
            const Payload = action.payload;
            debugger
            console.log(Payload);
            return { ...state, CorrectResponses: Payload }
        }

        case ADD_RESPONSE: {
            const Payload = action.payload;
            const { QuestionID, OptionID, Checked, Type } = Payload;
            const { Responses } = state;
            const UpdatedResponse = UpdateResponses({
                Responses,
                Payload,
                QuestionID,
                OptionID,
                Type,
                Checked
            });
            return { ...state, Responses: UpdatedResponse }
        }

        case ADD_CURRENT_NESTED_QUESTION: {
            const Payload = action.payload;
            return { ...state, CurrentNestedQuestion: Payload }
        }

        default:
            return { ...state };
    }
}

export default QuizReducer;