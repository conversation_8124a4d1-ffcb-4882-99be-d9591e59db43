import React, { useEffect, useState } from 'react';
import { Grid, Table, TableBody, TableContainer, TableCell, TableHead, TableRow, Checkbox, Select, MenuItem } from '@mui/material';
import "../Approvaldashboard/ApprovalDashboard.scss"
import SearchIcon from '@mui/icons-material/Search';
import { connect } from "react-redux";
import {
  GetCommonData, GetCommonspData, GetCommonspDataV2, GetRosterData, RmsRedirectionToUrl, updateLeaveCancellation
} from "../../store/actions/CommonAction";

import moment from 'moment';
import _ from 'underscore';
import { useNavigate } from "react-router-dom";
import { getUrlParameter } from '../../../Agent/utility/utility.jsx'
import SearchAdvisorPopup from './SearchAdvisorPopup';
import { Dialog, DialogContent, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import SingleApproval from './SingleApproval';
import SingleApprovalTabs from './SingleApprovalTabs';
import CommentPopup from './CommentPopup';


const Approval = (props) => {

  const [rosterMaster, setRosterMaster] = useState([]);
  const [openAdvisor, setAdvisorOpen] = useState(false);
  const [selectedRoster, setSelectedRoster] = useState(null);
  const [productMaster, setProductMaster] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [processMaster, setProcessMaster] = useState([]);
  const [selectedProcess, setSelectedProcess] = useState(null);
  const [supervisorData, setSupervisorsData] = useState([]);
  const [selectedSupervisor, setSelectedSupervisor] = useState(null);
  const [advisorsLeaves, setAdvisorsLeaves] = useState(null);
  const [activeClass, setActiveClass] = useState(3);
  const [usersData, setUsersData] = useState([]);
  const [userDetails, setUserDetails] = useState([]);
  const [roleId, setRoleId] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [advisorData, setAdvisorData] = useState([]);
  const [employeeId, setEmployeeId] = useState(""); // State to hold input
  const [filteredAgents, setFilteredAgents] = useState([]);
  const [iframeSrc, setIframeSrc] = useState(""); // State to hold iframe URL
  const [openPopup, setOpenPopup] = useState(false); // Track popup state
  const [approvalOpenPopup, setApprovalOpenPopup] = useState(false);
  const [roster, setRoster] = useState();
  const [userId, setUserId] = useState()
  const [totalShrinkage, setTotalShrinkage] = useState();
  const [totalShrinkageLeaves, setTotalShrinkageLeaves] = useState();
  const [managerId, setManagerId] = useState();
  const [leavesApproved, setLeaveApproved] = useState(false);
  const [commentPopUp, setCommentPopUp] = useState();
  const [comments, setComments] = useState();
  const [cancelLeaveId, setCancelLeaveId] = useState();
  const navigate = useNavigate();

  useEffect(() => {
    RmsRedirectionToUrl(function (results) {
      debugger;
      if (results && results.data && results.data.status == 200) {
        localStorage.setItem('PBRMSToken', results.data.Info)
        let userData = JSON.parse(atob(results.data.Info));
        setRoleId(userData.RoleId);
        setUserDetails(userData);
      }
    })
  }, [])

  useEffect(() => {
    GetAgentsData();
    GetTotalShrinkage();
  }, [])

  useEffect(() => {
    getRoster();
  }, [roster])

  useEffect(() => {
    getProduct();
  }, [selectedRoster, advisorsLeaves])

  useEffect(() => {
    getProcess();
  }, [selectedProduct, advisorsLeaves])

  useEffect(() => {
    getSupervisorData();
  }, [selectedProcess, advisorsLeaves])

  useEffect(() => {
    let RosterId = getUrlParameter('RosterId');
    if (RosterId) {
      setSelectedRoster(RosterId);
    }

    if (selectedRoster) {
      RosterId = selectedRoster;
      setSelectedRoster(selectedRoster);
    }
    window.history.pushState("", "", `?RosterId=${RosterId}`);
  }, [roleId, selectedRoster]);

  // useEffect(() => {
  //   GetAgentsData();
  // }, [selectedRoster, rosterMaster])

  useEffect(() => {
    filterData();
  }, [selectedRoster, advisorsLeaves, selectedProduct, selectedProcess, selectedSupervisor, activeClass])

  const handleChangeRoster = (e) => {
    setSelectedRoster(e.target.value);
  }

  const handleChangeProduct = (e) => {
    setSelectedSupervisor(null);
    setSelectedProcess(null);
    setSelectedProduct(e.target.value);
  }

  const handleChangeProcess = (e) => {
    setSelectedSupervisor(null);
    setSelectedProcess(e.target.value);
  }

  const handleChangeSupervisor = (e) => {
    setSelectedSupervisor(e.target.value);
  }

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleAdvisorClickOpen = () => {
    setAdvisorOpen(true);
  };
  const handleAdvisorClose = () => {
    setAdvisorOpen(false);
  };

  const handleItemChange = (e) => {
    setUsersData([]);
    setActiveClass(e.target.value);
  }

  const filterData = () => {
    let filteredList = advisorsLeaves;

    if (selectedRoster === "all") {
      filteredList = advisorsLeaves;
    }
    else if (selectedRoster === "before") {
      filteredList = filteredList && filteredList.filter(item => {
        return item.ApplicationDate < roster[0].StartDate; // Added return statement
      });
    }
    else if (selectedRoster === "after") {
      filteredList = filteredList && filteredList.filter(item => {
        return item.ApplicationDate > roster[0].EndDate; // Added return statement
      });
    }
    else {
      filteredList = filteredList && filteredList.filter(item => {
        return item.ApplicationDate >= roster[0].StartDate && item.ApplicationDate <= roster[0].EndDate; // Added return statement
      });
    }

    if (roleId == 12) {
      if (activeClass == 6) {
        filteredList = filteredList && filteredList.filter(item =>
          (selectedProduct === "all" || item.ProductId === selectedProduct) &&
          (selectedProcess === "all" || item.ProcessId === selectedProcess)
        );
      } else {
        filteredList = filteredList && filteredList.filter(item =>
          (selectedProduct === "all" || item.ProductId === selectedProduct) &&
          (selectedProcess === "all" || item.ProcessId === selectedProcess) &&
          (activeClass === "all" || (activeClass === 0 ? (item.Status === 0 || item.Status === 4) : item.Status === activeClass))
        );
      }
    } else {
      if (activeClass == 6) {
        filteredList = filteredList && filteredList.filter(item =>
          (selectedProduct === "all" || item.ProductId === selectedProduct) &&
          (selectedProcess === "all" || item.ProcessId === selectedProcess) &&
          (selectedSupervisor === "all" || item.ManagerId === selectedSupervisor)
        );
      } else {
        filteredList = filteredList && filteredList.filter(item =>
          (selectedProduct === "all" || item.ProductId === selectedProduct) &&
          (selectedProcess === "all" || item.ProcessId === selectedProcess) &&
          (selectedSupervisor === "all" || item.ManagerId === selectedSupervisor) &&
          (activeClass === "all" || (activeClass === 0 ? (item.Status === 0 || item.Status === 4) : item.Status === activeClass))
        );
      }
    }

    setAdvisorData(filteredList);
    setFilteredAgents(filteredList);
  };


  const getRoster = () => {
    debugger
    if (advisorsLeaves) {
      setRosterMaster(roster);
      setSelectedRoster('all');
    }
    // setIsLoading(false);
  }

  const getProduct = () => {
    if (advisorsLeaves) {
      let ProductList = _.uniq(advisorsLeaves, (item) => {
        return item.ProductId
      })
      setProductMaster(ProductList);
      // setSelectedProduct(ProductList[0]?.ProductId);
      setSelectedProduct('all');
    }
  }

  // const getProcess = () => {
  //   let filteredList = advisorsLeaves && advisorsLeaves.filter(item => item.ProductId === selectedProduct);
  //   let uniqueProcessIds =
  //     _.uniq(filteredList, (item) => {
  //       return item.ProcessId
  //     })
  //   setProcessMaster(uniqueProcessIds);
  //   // setSelectedProcess(uniqueProcessIds[0]?.ProcessId);
  //   setSelectedProcess('all');
  // }

  const getProcess = () => {
    let filteredList;

    if (roleId === 12) {
      // Get all unique processes from advisorsLeaves, regardless of product
      filteredList = advisorsLeaves;
    } else {
      filteredList = advisorsLeaves && advisorsLeaves.filter(item => item.ProductId === selectedProduct);
    }

    let uniqueProcessIds = _.uniq(filteredList, item => item.ProcessId);

    setProcessMaster(uniqueProcessIds);
    // setSelectedProcess(uniqueProcessIds[0]?.ProcessId);
    setSelectedProcess('all');
  };


  const getSupervisorData = () => {
    let filteredList = advisorsLeaves && advisorsLeaves.filter(
      item => item.ProductId === selectedProduct && item.ProcessId === selectedProcess
    ); let uniquesupervisors =
      _.uniq(filteredList, (item) => {
        return item.ManagerId
      })
    setSupervisorsData(uniquesupervisors);
    // setSelectedSupervisor(uniquesupervisors[0]?.ManagerId);
    setSelectedSupervisor('all');
  }

  useEffect(() => {
    const filteredAgents = advisorData
      ? advisorData.filter((agent) =>
        // agent.EmployeeName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        // agent.EmployeeId.toString().includes(searchQuery)
        agent.EmployeeName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        agent.EmployeeId.toLowerCase().includes(searchQuery.toLowerCase())
      )
      : [];
    setFilteredAgents(filteredAgents);
  }, [searchQuery])

  const GetAgentsData = () => {
    // if ((roleId == 12 && selectedRoster > 0) ||
    //   selectedRoster > 0) {

    // if (startDate != '') {
    props.GetCommonspDataV2({
      root: 'GetAgentRequestedLeave',
      c: "R",
    }, function (errorStatus, response) {
      if (!errorStatus) {
        console.log(response.data);
        setAdvisorsLeaves(response.data[0]);
        setRoster(response.data[1]);
      } else {
        //
      }
      // setIsLoading(false);
    })
    // }
  }

  const GetTotalShrinkage = () => {
    props.GetCommonspDataV2({
      root: 'GetTotalShrinkage',
      c: "R",
    }, function (errorStatus, response) {
      if (!errorStatus) {
        // console.log(response.data);
        setTotalShrinkage(response.data[0]);
        setTotalShrinkageLeaves(response.data[1]);
      } else {
        //
      }
    })
    // }
  }

  // const checkUsers = (e, LeaveId) => {
  //   let checkUsersData = [...usersData];
  //   if (e.target.checked) {
  //     if (checkUsersData.length == 0) {
  //       checkUsersData.push(LeaveId)
  //     }

  //     let len = checkUsersData.length
  //     for (let i = 0; i < len; i++) {
  //       if (!checkUsersData.includes(LeaveId)) {
  //         checkUsersData.push(LeaveId)
  //       }
  //     }
  //   }
  //   else if (!e.target.checked && checkUsersData.length > 0) {
  //     // let data = checkUsersData[LeaveId];
  //     for (let i = 0; i < usersData.length; i++) {
  //       const index = checkUsersData.indexOf(LeaveId);
  //       if (index > -1) { // only splice array when item is found
  //         checkUsersData.splice(index, 1); // 2nd parameter means remove one item only
  //       }
  //     }
  //   }
  //   setUsersData(checkUsersData);
  // }

  // const getDetails = () => {
  //   let userIds = []
  //   for (let i = 0; i < usersData.length; i++) {
  //     for (let j = 0; j < advisorsLeaves.length; j++) {
  //       if (usersData[i] == advisorsLeaves[j].Id) {
  //         if (!userIds.includes(advisorsLeaves[j].UserId)) {
  //           userIds.push(advisorsLeaves[j].UserId);
  //           break;
  //         }
  //       }
  //     }
  //   }

  //   if (userIds.length == 1) {
  //     navigate(`/admin/Rms/SingleApproval?UserId=${userIds}&ManagerId=${selectedSupervisor || 0}`);
  //   }
  //   else if (userIds.length > 1) {
  //     navigate(`/admin/Rms/multiApproval?UserId=${userIds}&ManagerId=${selectedSupervisor}`);
  //   }
  // }

  const checkSingleUser = (e, LeaveId) => {
    let checkUsersData = [];
    let userIds = 0;
    let managerId = 0;
    // for (let j = 0; j < advisorsLeaves.length; j++) {
    //   if (LeaveId == advisorsLeaves[j].Id) {
    //     userIds = advisorsLeaves[j].UserId;
    //     managerId = advisorsLeaves[j].ManagerId;
    //     break;
    //   }
    // }
    const foundLeave = advisorsLeaves.find(leave => leave.Id === LeaveId);

    if (foundLeave) {
      userIds = foundLeave.UserId;
      managerId = foundLeave.ManagerId;
    }
    setUserId(userIds);
    setManagerId(managerId);
    setApprovalOpenPopup(true); // Open the modal
  }

  // const checkAllUsers = (e) => {
  //   let ArrayData = [];
  //   if (e.target.checked) {
  //     for (let i = 0; i < advisorsLeaves.length; i++) {
  //       if (!ArrayData.includes(advisorsLeaves[i].Id)) {
  //         ArrayData.push(advisorsLeaves[i].Id);
  //       }
  //     }
  //   }
  //   else if (!e.target.checked) {
  //     ArrayData = [];
  //   }
  //   setUsersData(ArrayData)
  // }
  // 11601	
  const updateLeaveCancellationAdvisor = () => {
    // let leaveIds = [parseInt(cancelLeaveId)];

    const leaveData = totalShrinkage?.find(leave => leave.Id === cancelLeaveId) || {
      Id: cancelLeaveId,
      IsFallbackRoster: 0,
      weekOffShrinkage: 0,
      StandardShrinkage: 0,
      TotalShrinkage: 0
    };

    let body = {
      leaveIds: [parseInt(cancelLeaveId)],
      weekoffShrinkage: leaveData.IsFallbackRoster == 0 ? leaveData.weekOffShrinkage : leaveData.StandardShrinkage,
      currentShrinkage: leaveData.IsFallbackRoster == 0 ? leaveData.TotalShrinkage - leaveData.weekOffShrinkage :
        leaveData.TotalShrinkage - leaveData.StandardShrinkage
    }

    try {
      updateLeaveCancellation(body, function (results) {
        if (results && results.data && results.data.status == 200) {
          GetAgentsData();
          GetTotalShrinkage();
          setCommentPopUp(false);
        }
      })
    }
    catch (ex) {

    }
  }

  const handleEmployeeChange = (e) => {
    setEmployeeId(e.target.value);
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && employeeId.trim() !== "") {
      let source = 'Approval'
      let url = `/agent/LeaveManagement?EmployeeId=${employeeId}&source=${source}`
      setIframeSrc(url);
      setOpenPopup(true); // Open the modal
    }
  };

  const handleClosePopup = () => {
    setOpenPopup(false);
  };

  const handleApprovalClosePopup = () => {
    setApprovalOpenPopup(false);
    if (leavesApproved) {
      GetAgentsData();
      GetTotalShrinkage();
    }
  }

  const openCommentPopUp = (LeaveId) => {
    // if(selectedLeave.length>0) {
    setCancelLeaveId(LeaveId);
    setCommentPopUp(true);
    // }
  }

  const handleClosePopUp = () => {
    setCommentPopUp(false);
  }

  // const handelApproval = () => {
  //   if (approvalValue == 2) {
  //     LeaveApproval(2);
  //   }
  //   else {
  //     LeaveRejection(0);
  //   }
  //   setCommentPopUp(false);
  //   props.setLeaveApproved(true);
  // }

  return (
    <>
      <div className="approvalDashboard">
        <Grid container spacing={2}>
          <Grid item xs={12} md={2}>
            <div className="Approvalmenu">
              <p className="pageStatus">Dashboard</p>
              <img src="/Lms/vistalogo.svg" />
              <ul onClick={handleItemChange}>
                <li value="3" className={activeClass === 3 ? "active" : ""} >Pending</li>
                <li value='2' className={activeClass === 2 ? "active" : ""} >Approved</li>
                <li value='0' className={activeClass === 0 ? "active" : ""} >Rejected</li>
                <li value='6' className={activeClass === 6 ? "active" : ""} >All</li>
              </ul>
              <div className="SearchInput">
                <SearchIcon />
                <input
                  type="text"
                  placeholder="Enter Employee Code"
                  name="search2"
                  value={employeeId}
                  onChange={handleEmployeeChange}
                  onKeyDown={handleKeyDown} // Detect Enter key press
                />
              </div>
              {/* Iframe to Load Employee Leave Management */}
            </div>
          </Grid>
          <Grid item xs={12} md={10}>
            <Grid container spacing={1}>
              <Grid item xs={12} md={3}>
                <label className="RosterLabels">Select Roster</label>

                <Select
                  options={[
                    { label: "All", value: "all" },
                    { label: "Before", value: "before" },
                    { label: "After", value: "after" },
                    ...rosterMaster
                  ]}
                  onChange={handleChangeRoster}
                  components={{
                    IndicatorSeparator: () => null
                  }}
                  className="RosterMaster"
                  value={selectedRoster}
                >
                  <MenuItem value="all">All</MenuItem>
                  {rosterMaster && rosterMaster.map((rosterMaster) => {
                    return (
                      <MenuItem value={rosterMaster.Id}>Current Roster ({moment(rosterMaster.StartDate).format('DD MMM YYYY')} - {moment(rosterMaster.EndDate).format('DD MMM YYYY')})</MenuItem>
                    )
                  })
                  }
                  {rosterMaster && rosterMaster.map((rosterMaster) => {
                    return (
                      <MenuItem value="before">Past Rosters ({moment(rosterMaster.StartDate).subtract(2, 'months').date(21).format('DD MMM YYYY')} - {moment(rosterMaster.StartDate).subtract(1, 'days').format('DD MMM YYYY')})</MenuItem>
                    )
                  })
                  }
                  {rosterMaster && rosterMaster.map((rosterMaster) => {
                    return (
                      <MenuItem value="after">Future Rosters ({moment(rosterMaster.EndDate).add(1, 'days').format('DD MMM YYYY')} - {moment().add(45, 'days').format('DD MMM YYYY')})</MenuItem>
                    )
                  })
                  }
                  {/* <MenuItem value="before">Past Rosters</MenuItem> */}
                  {/* <MenuItem value="after">Future Rosters</MenuItem> */}
                </Select>
              </Grid>

              {userDetails && userDetails.RoleId != 12 && <Grid item xs={12} md={2}>
                <label className="RosterLabels">Select Product</label>
                <Select
                  options={[{ label: "All", value: "all" }, ...productMaster]}
                  onChange={(e) => handleChangeProduct(e)}
                  className="RosterMaster"
                  value={selectedProduct}
                >
                  <MenuItem value="all">All</MenuItem>
                  {productMaster && productMaster.map((product) => (
                    <MenuItem key={product.ProductId} value={product.ProductId}>{product.ProductName}</MenuItem>
                  ))}
                </Select>
              </Grid>}

              {userDetails && <Grid item xs={12} md={2}>
                <label className="RosterLabels">Select Process</label>
                <Select
                  options={[{ label: "All", value: "all" }, ...processMaster]}
                  onChange={(e) => handleChangeProcess(e)}
                  className="RosterMaster"
                  value={selectedProcess}
                >
                  <MenuItem value="all">All</MenuItem>
                  {processMaster && processMaster.map((process) => (
                    <MenuItem key={process.ProcessId} value={process.ProcessId}>{process.ProcessName}</MenuItem>
                  ))}
                </Select>
              </Grid>}

              {userDetails && userDetails.RoleId != 12 && <Grid item xs={12} md={2}>
                <label className="RosterLabels">Select Supervisor</label>
                <Select
                  options={[{ label: "All", value: "all" }, ...supervisorData]}
                  onChange={(e) => handleChangeSupervisor(e)}
                  className="RosterMaster"
                  value={selectedSupervisor}
                >
                  <MenuItem value="all">All</MenuItem>
                  {supervisorData && supervisorData.map((supervisor) => (
                    <MenuItem key={supervisor.ManagerId} value={supervisor.ManagerId}>{supervisor.ManagerName}</MenuItem>
                  ))}
                </Select>
              </Grid>}

              <Grid item xs={12} md={3}>
                <div className="SearchInput">
                  <SearchIcon />
                  <input type="text" placeholder="Enter Advsior Name/EmployeeId" name="search2"
                    value={searchQuery}
                    onChange={handleSearchChange}
                  />

                </div>
              </Grid>
            </Grid>

            <Dialog open={openPopup} onClose={handleClosePopup}
              maxWidth="md"
              fullWidth
              className="LeaveManagementPopup "
            >
              <DialogContent>
                <IconButton
                  onClick={handleClosePopup}
                  className="closeIcon"
                >
                  <CloseIcon />
                </IconButton>

                <iframe
                  src={iframeSrc}
                  title="Employee Leave Management"
                  style={{ width: "100%", height: "600px", border: "none" }}
                  onLoad={(e) => {
                    const doc = e.target.contentDocument || e.target.contentWindow.document;
                    if (doc) {
                      const style = doc.createElement("style");
                      style.textContent = `
        .leftSide ul li { width: 20% !important; }
        .leftSide ul li span { width:100% !important;}
        .leftSide ul li span svg { width: 17px !important; }
        .leftSide ul li:nth-of-type(2n) { width: 72% !important; font-size: 15px !important; }
        .my-calendar .fc-toolbar-title { font-size: 18px !important; }
        .my-calendar th { font-size: 13px !important; }
        .LeaveApplicationPopup {width:55%; margin:auto}
      `;
                      doc.head.appendChild(style);
                    }
                  }}
                />

              </DialogContent>
            </Dialog>

            <Dialog open={approvalOpenPopup} onClose={handleApprovalClosePopup} maxWidth="md" fullWidth className="SingleApprovalPopup">
              <DialogContent>
                <IconButton
                  className="closeIcon"
                  onClick={handleApprovalClosePopup}
                >
                  <CloseIcon />
                </IconButton>
                {/* <SingleApproval UserId={userId} ManagerId={selectedSupervisor} /> */}
                <SingleApprovalTabs userId={userId} managerId={managerId} setLeaveApproved={setLeaveApproved} totalShrinkage={totalShrinkage} />
              </DialogContent>
            </Dialog>
            {/* style={{ maxHeight: '400px' }} */}
            <TableContainer className="ApprovalDetails" >
              <Table stickyHeader aria-label="customized table" >
                <TableHead>
                  <TableRow>
                    {/* <TableCell align="left"><Checkbox className="RequestCheck" onChange = {checkAllUsers}/>  Request No.</TableCell> */}
                    <TableCell align="left"> No.</TableCell>
                    <TableCell align="left">E-Code</TableCell>
                    <TableCell align="left">Emp Name</TableCell>
                    <TableCell align="left">TL Name</TableCell>
                    <TableCell align="left">CreatedOn</TableCell>
                    <TableCell align="left">Leave Date</TableCell>
                    <TableCell align="left">Leave Type</TableCell>
                    <TableCell align="left" style={{ width: '120px' }}>Total Shrinkage</TableCell>
                    <TableCell align="left">AppliedBy</TableCell>
                    <TableCell align="left">Status</TableCell>
                    <TableCell align="left"></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredAgents && filteredAgents.length > 0 ? filteredAgents.map((user) => {
                    return (
                      <TableRow>
                        {/* <TableCell align="left"><Checkbox onClick={(e)=>checkUsers(e,Users.Id)} value={Users.Id} checked = {(usersData?.includes(Users.Id))} />{Users.Id}</TableCell> */}
                        <TableCell align="left">
                          {/* {user.Status == 3 && <Checkbox onClick={(e) => checkSingleUser(e, user.Id)} value={user.Id} checked={(usersData?.includes(user.Id))} />} */}
                          {user.Id}
                        </TableCell>
                        <TableCell align="left" style={{ color: user.PendingLeaveColor }}>{user.EmployeeId}</TableCell>
                        <TableCell align="left">{user.EmployeeName}</TableCell>
                        <TableCell align="left">{user.ManagerName}</TableCell>
                        {/* <TableCell align="left">{(user.CreatedOn)}</TableCell> */}
                        <TableCell align="left">{moment.utc(user.CreatedOn).format("DD-MMM-YYYY")}</TableCell>
                        <TableCell align="left">{moment(user.ApplicationDate).format('DD-MMM-YYYY')}</TableCell>
                        <TableCell align="left">{user.LeaveType}</TableCell>
                        <TableCell align="left" style={{
                          color: (() => {
                            const shrinkage = [2, 3].includes(user.Status)
                              ? totalShrinkage?.find(s => s.Id == user.Id)?.TotalShrinkage
                              : totalShrinkageLeaves?.find(s => s.Id == user.Id)?.TotalShrinkage;

                            if (shrinkage === undefined) return 'inherit';
                            const value = parseFloat(shrinkage);
                            if (value > 0) return 'red';
                            if (value < 0) return 'green';
                            return 'inherit';
                          })()
                        }}>
                          {
                            [2, 3].includes(user.Status)
                              ? totalShrinkage?.find((shrinkage) => shrinkage.Id == user.Id)?.TotalShrinkage ?? ""
                              : totalShrinkageLeaves?.find((shrinkage) => shrinkage.Id == user.Id)?.TotalShrinkage ?? ""
                          }
                        </TableCell>
                        <TableCell align="left">{user.AppliedBy}</TableCell>
                        <TableCell align="left"><button className="Approved">{user.StatusName}</button></TableCell>
                        {/* updateLeaveCancellationAdvisor(user.Id) */}
                        {user.Status == 2 && <TableCell align="left"> <button className="cancelbtn" onClick={() => openCommentPopUp(user.Id)}>Cancel</button></TableCell>}


                        {user.Status == 3 && <TableCell align="left"> <button className="cancelbtn" onClick={(e) => checkSingleUser(e, user.Id)} value={user.Id}>
                          Action</button></TableCell>}
                      </TableRow>
                    )
                  }) : (
                    <TableRow>
                      <TableCell colSpan={6} style={{ textAlign: "center" }}>
                        No matching records found
                      </TableCell>
                    </TableRow>
                  )
                  }
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>
        </Grid>
        <CommentPopup open={commentPopUp}
          handleClose={handleClosePopUp} setComments={setComments} handelApproval={updateLeaveCancellationAdvisor}
        />
        {/* <SearchAdvisorPopup open={openAdvisor} onClose={handleAdvisorClose} /> */}
      </div>
    </>
  );
};

// export default Approval;
function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    GetCommonspDataV2
  }
)(Approval);

