
import React from "react";
import { Alert } from 'react-bootstrap';
import moment from 'moment';
import DataTable from "react-data-table-component";
import exportFromJSON from 'export-from-json';


const PrintExcelColumn = (props) =>{
    const { fileName, columnNames, data } = props;
    

    const handleDownload = ()=>{
        const exportType =  exportFromJSON.types.xls;
        exportFromJSON({ data, fileName, exportType });
    }
    
    return (
        <div>
        {data && data.length!=0?<span className="downloadExcel" onClick={handleDownload}></span>:""}
        </div>
    )

}

// class PrintExcelColumn extends React.Component {
//     constructor(props) {
//         super(props);
//         this.state = {
//             columns: this.props.columnNames
//         }
//     }
//     componentDidUpdate() {

//     }
//     componentDidMount() {
//     }
//     componentWillReceiveProps(nextProps) {
//         this.setState({ columns: nextProps.columnNames });        
//     }

//     renderValue(columns){
//         console.log("columns")
//             console.log(columns)
//         if(columns.type == 'datetime'){
//             return (col) => moment.utc(col[columns.selector]).local(true).format(columns.format)    
//         }
//         else
//         return columns.selector;
//     }

//     renderExcelColumnData(columns){
//         let ExcelColumns = [];

//         let index = 0;
//             if(columns.length) {
//                 columns.forEach(element => {
//                     ExcelColumns.push(
//                         <ExcelColumn key={index} 
//                         label={element.name} 
//                         value={this.renderValue(element)}/>      
//                     )
//                     });
//                 }
//             return ExcelColumns;
//     }
//     render() {

//         if (this.props.columnNames && this.props.columnNames.length > 0) {
//            return(
//             <ExcelFile filename={this.props.fileName} element={<span className="downloadExcel"></span>}>
//             <ExcelSheet data={this.props.data} name="Conference">
//             {this.renderExcelColumnData(this.props.columnNames)}
//             </ExcelSheet>
//             </ExcelFile>
//            );   
           
//         }
//         else {
//             return null;
//         }
//     }
// }

export default PrintExcelColumn;
