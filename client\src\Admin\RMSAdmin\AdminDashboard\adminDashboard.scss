

.footerBtn {
    text-align: right;

    span {
        border: 1px solid #ddd;
        padding: 9px 10px;
        border-radius: 6px;
        background-color: #FFFDE7;
        font: normal normal 500 14px/19px Roboto;
    }

    button {
        border-radius: 4px;
        color: #fff;
        border: none;
        padding: 8px;
        font: normal normal 500 14px/19px Roboto;
        cursor: pointer;
        width: 116px;
        margin: 0px 5px;
    }

    .FreezeBtn {
        float: none;
    }

    .ResetBtn {
        float: none;
    }

    .Approvebtn {
        background: #48a648;

    }

    .AddCommentBtn {
        background-color: #CEB351;
    }
}

.CommentPopup {
    z-index: 99999999;

    .MuiDialog-paperWidthSm {
        width: 394px;

        .TextArea {
            background: #FFFFFF 0% 0% no-repeat padding-box;
            border: 1px solid #7070703D;
            border-radius: 8px;
            opacity: 1;
            width: 100%;
            margin: 5px 0px 15px 0px;

        }

        label {
            text-align: left;
            font: normal normal bold 14px/19px Roboto;
            letter-spacing: 0px;
            color: #253858;
            opacity: 1;
        }

        .MuiDialogContent-dividers {
            border-bottom: none;
        }

        .MuiDialogActions-root {
            justify-content: center;
        }

        .saveButton {
            background: #0065FF 0% 0% no-repeat padding-box;
            border-radius: 4px;
            font: normal normal 500 14px/19px Roboto;
            letter-spacing: 0px;
            width: 250px;
            padding: 10px;
            color: #FFFFFF;
            opacity: 1;
        }

        .CommonComment {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;

            p {
                display: flex;
                text-align: left;
                font: normal normal 600 12px/16px Roboto;
                letter-spacing: 0px;
                color: #253858;
                opacity: 1;
                align-items: center;

                svg {
                    background-color: #0065ff;
                    padding: 4px;
                    width: 20px;
                    height: 20px;
                    margin-right: 5px;
                    border-radius: 13px;
                    color: #fff;
                }
            }

            .MuiSwitch-root {
                width: 30px;
                height: 17px;
                border-radius: 20px !important;
                padding: 0px;

                .MuiSwitch-switchBase {
                    top: 1px;
                    left: 1px;
                    padding: 0px;
                }

                .MuiSwitch-thumb {
                    width: 14px;
                    height: 14px;
                    border-radius: 50%;
                }

                .Mui-checked {
                    transform: translateX(14px);
                    color: #0065FF;
                }

            }
        }
    }

    .header {
        display: flex;
        align-items: center;
        flex-direction: column-reverse;
        float: left;
        padding: 0px;
        font: normal normal 500 10px/13px Roboto;
        letter-spacing: 0px;
        color: #253858;
        opacity: 1;

        svg {
            font-size: 15px;
        }
    }

    .MuiTypography-h6 {
        text-align: center;
        font: normal normal 500 21px/28px Roboto;
        letter-spacing: 0px;
        color: #253858;
        opacity: 1;
    }
}