import react,{useEffect, useState} from 'react';
import { GetCommonData, InsertData, UpdateData, GetCommonspData, DownloadSurveyData, DeleteData, InsertCourseData , UpdateCourseData} from "../../../store/actions/CommonAction";
import { connect } from "react-redux";
import Comments from "./Comments.jsx";

const RMSAdmin=()=>{

   const [Comment, setComment]= useState(false);

   
    useEffect(()=>{
      console.log("msmsj");
    },[])

  const handleClick=()=>{
    setComment(true);
  }

  const handleClose=()=>{
    setComment(false);
  }

    return(
        <div>
            Hola
            <button onClick={handleClick}>Comments</button>
            {Comment && <Comments onClose={handleClose}/>}
        </div>
    )
}


function mapStateToProps(state) {
    return {
      CommonData: state.CommonData
    };
  }
  
  export default connect(
    mapStateToProps,
    {
      GetCommonData,
      GetCommonspData,
      InsertData,
      UpdateData,
      DeleteData
    }
  )(RMSAdmin);