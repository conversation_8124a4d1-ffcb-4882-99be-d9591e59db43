var express = require("express");
const router = express.Router();
const controller = require("./SQLdbController");

router.post("/insert/*", controller.insert);
router.post("/update/*", controller.update);
router.get("/list/*", controller.getdata);
router.post("/delete", controller.delete);
router.get("/listsp/*", controller.getdatasp);
router.get("/listspV2/:root*", controller.getdataspV2);
router.post("/AgentSurvey", controller.AgentSurveyExcel);
router.get("/InsertSurveyMapping/*", controller.InsertSurveyMapping);
router.get("/InsertQuizMapping/*", controller.InsertQuizMapping);
router.get("/GetAnswerQuizDetails", controller.GetAnswerQuizDetails);
router.post("/QuizRedirection", controller.QuizRedirection);
router.get("/DownloadSurveyData", controller.DownloadSurveyData);
router.get("/DownloadQuizData", controller.DownloadQuizData);
router.post("/QuizInsertResponsesData", controller.QuizInsertResponsesData);
router.post("/CourseContentData", controller.AddUpdateCourseContentData);
router.post("/insertCourseData",controller.insertCourseData);
router.post("/updateCourseData", controller.UpdateCourseData);
router.post("/PBSchoolQuizInsertResponsesData", controller.PBSchoolQuizInsertResponsesData);
router.post("/HealthCheck", controller.HealthCheck);

module.exports = router;
