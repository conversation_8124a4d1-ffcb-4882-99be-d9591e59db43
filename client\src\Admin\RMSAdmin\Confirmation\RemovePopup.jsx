import React, { useEffect, useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { Grid, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import {
    InsertTransferRequest
} from "../../store/actions/CommonAction";

const RemovePopup = (props) => {

    const { open, onClose, transferData, processList, getRosterData, TLRosterStatusData } = props;
    // console.log(props);
    const [EmployeeData, setEmployeeData] = useState();
    // const [selectedProcess, setSelectedProcess] = useState();

    useEffect(() => {
        if (transferData.length > 0) {
            // console.log(JSON.parse(transferData));
            setEmployeeData(JSON.parse(transferData));
        }
    }, [transferData])

    const removeAdvisor = () => {
        // console.log(EmployeeData.UserId, EmployeeData.ProcessId, selectedProcess)
        // if (selectedProcess > 0) {
            try {
                let transferData = {
                    UserId: EmployeeData.UserId,
                    CurrentProcess: EmployeeData.ProcessId,
                    MovedToProcess: 0,
                    RequestType: 2
                }
                InsertTransferRequest(transferData, function (result) {
                    if (result && result.data && result.data.status == 200) {
                        // console.log(result);
                        getRosterData();
                        TLRosterStatusData();
                        onClose();
                    }
                })
            }
            catch (ex) {

            }
        // }        
    }


    return (
        <>
        {EmployeeData &&
        <Dialog open={open} onClose={onClose} maxWidth="xs" className="RemovePopup">
            <DialogTitle>
                {"Remove advisor"}
                <IconButton
                    aria-label="close"
                    onClick={onClose}
                    sx={{ position: 'absolute', right: 8, top: 8, color: (theme) => theme.palette.grey[500] }}
                >
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent>
                <Grid container>
                    <Grid item md={12} sm={12} xs={12}>
                    <p>You're about to remove an advisor. Please confirm before proceeding</p>
                    <h5> Agent details:  <b>  {EmployeeData.EmployeeName}, {EmployeeData.EmployeeId}; </b></h5>
                    </Grid>

                    <Grid item md={12} sm={12} xs={12} className="alignCenter">
                        <button className="RemoveRequestbutton" onClick={removeAdvisor}>Send remove request</button>
                    </Grid>
                </Grid>
            </DialogContent>

        </Dialog>}
        </>
    );
}

export default RemovePopup;
