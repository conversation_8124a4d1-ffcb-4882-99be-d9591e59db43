import React, { useState, useEffect } from "react";
import { connect } from "react-redux";
import { GetCommonData, InsertData, UpdateData, GetCommonspData, DeleteData } from "../../store/actions/CommonAction";
import {
    List,
    ListItem,
    ListItemText,
    Menu,
    MenuItem,
    Box,
    styled,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableRow
} from '@mui/material';
import TextField from '@mui/material/TextField';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import moment from "moment";
import Button from '@mui/material/Button';

const MenuRoot = styled('div')(({ theme }) => ({
    width: '100%',
    maxWidth: 360,
    backgroundColor: theme.palette.background.paper
}));

const AddConfiguration = (props) => {
    const [keyName, setKeyName] = useState('');
    const [valueType, setValueType] = useState('');
    const [value, setValue] = useState('');
    const [types, setTypes] = useState(['select a data type', 'number', 'text', 'date']);
    const [anchorEl, setAnchorEl] = useState(null);
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [selectedDate, setSelectedDate] = useState(null);

    const handleChange = (e) => {

        if (e.target.id == 'keyName') {
            setKeyName(e.target.value);
        }
        else if (e.target.id == 'valueType') {
            
            setValueType(e.target.value);
        }
        else if (e.target.id == 'value') {
            setValue(String(e.target.value));
        }
    }

    const handleClickListItem = (event) => {

        setAnchorEl(event.currentTarget);

    }

    const handleMenuItemClick = (event, index) => {
        setValueType(types[index]);
        setSelectedIndex(index);
        setAnchorEl(null);

    }

    const processClose = () => {
        setAnchorEl(null);
    }

    const handleDateChange = (date) => {
        setSelectedDate(date);

    };

    const handleSave = () => {
        // debugger;
        if (keyName.length > 0 && valueType.length > 0) {
        
            let formvalue = {};
            if (valueType == 'date') {
                if (selectedDate) {
                    formvalue.Value = moment(selectedDate).format('YYYY-MM-DD');
                }
                else {
                    alert('Please Choose a Date');
                }
            }
            else {
                if (value) {
                    formvalue.Value = value;
                }
                else {
                    alert('Please enter a value');
                }
            }


            formvalue.KeyName = keyName;
            formvalue.valueType = valueType;

            props.InsertData({
                root: 'Configuration',
                body: formvalue
            }, (data) => {
                if (data.data.status != 200) {
                    alert(data.data.message);
                } else {
                    alert("Configuration Added Successfully!");
                }
                setKeyName('');
                setValue('');
                setValueType('');
                setSelectedDate(null);
                setSelectedIndex(0);
            })
        }
    }


    return (
        <Box width="100%" overflow="auto">
            <TextField
                autoFocus
                id='keyName'
                type='text'
                margin="dense"
                label='Enter Key Name'
                onChange={handleChange}
                value={keyName}
            />
            <MenuRoot>
                {types && types.length > 0 &&
                    <List component="nav" aria-label="Device settings">
                        <ListItem
                            button
                            aria-haspopup="true"
                            aria-controls="lock-menu"
                            onClick={handleClickListItem}
                        >
                            <ListItemText primary secondary={types[selectedIndex]} />
                        </ListItem>
                    </List>
                }
                <Menu
                    id="lock-menu"
                    anchorEl={anchorEl}
                    keepMounted
                    open={Boolean(anchorEl)}
                    onClose={processClose}
                >

                    {types.length > 0 &&
                        types.map((type, index) => (
                            <MenuItem
                                key={type}
                                disabled={index === 0}
                                selected={index === selectedIndex}
                                onClick={(event) => handleMenuItemClick(event, index)}
                            >
                                {type}
                            </MenuItem>
                        ))}
                </Menu>
            </MenuRoot>
            {valueType.length > 0 && valueType != 'date' &&
                < TextField
                    autoFocus
                    id='value'
                    type={valueType}
                    margin="dense"
                    label='Enter Value'
                    onChange={handleChange}
                    value={value}

                />
            }
            {valueType.length > 0 && valueType == 'date' &&
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker

                        // disableFuture
                        // openTo="year"
                        // format="yyyy-MM-dd"
                        label="YYYY-MM-DD"
                        value={selectedDate}
                        onChange={handleDateChange}
                        renderInput={(params) => <TextField {...params} />}
                    />
                </LocalizationProvider>
            }

            <Button onClick={handleSave} color="primary">
                SAVE
            </Button>

        </Box>
    )
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData,
        InsertData,
        UpdateData,
        DeleteData
    }
)(AddConfiguration);