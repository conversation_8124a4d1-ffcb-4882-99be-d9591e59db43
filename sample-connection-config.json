{"SQL_URL_RMS": {"user": "your_username", "password": "your_password", "server": "RMSSQL-DB.Etechaces.com", "database": "your_database_name", "port": 1433, "encrypt": true, "trustServerCertificate": true, "connectionTimeout": 30000, "requestTimeout": 30000, "pool": {"max": 10, "min": 0, "idleTimeoutMillis": 30000, "acquireTimeoutMillis": 30000}}, "RSQL_URL_RMS": {"user": "your_username", "password": "your_password", "server": "RMSSQL-DB.Etechaces.com", "database": "your_database_name", "port": 1433, "encrypt": true, "trustServerCertificate": true, "connectionTimeout": 30000, "requestTimeout": 30000, "pool": {"max": 10, "min": 0, "idleTimeoutMillis": 30000, "acquireTimeoutMillis": 30000}}, "MONGO_URL_MATRIX": {"connectionString": "mongodb://localhost:27017/your_mongo_database"}}