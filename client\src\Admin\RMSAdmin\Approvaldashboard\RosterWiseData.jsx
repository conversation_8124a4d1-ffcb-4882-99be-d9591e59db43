import React from "react";
import { TableContainer, Table, TableHead, TableRow, TableCell, TableBody, Checkbox } from "@mui/material";
import moment from "moment";

const RosterWiseData = ({ title, rosterData, selectedLeave, checkedAllLeaveId, checkedLeaveId, totalShrinkage }) => {
  return (
    <TableContainer className="ApprovalDetails" style={{ maxHeight: "300px" }}>
      <Table stickyHeader aria-label="customized table">
        <TableHead>
          <TableRow>
            <TableCell align="left">
              {rosterData?.length > 0 && (
                <Checkbox 
                  onClick={(e) => checkedAllLeaveId(e, title)}
                  className="RequestCheck"
                  checked={selectedLeave.length > 0 && rosterData.every(user => selectedLeave.includes(user.Id))}
                />
              )}
             Req No.
            </TableCell>
            <TableCell align="left">Leave Date</TableCell>
            {/* <TableCell align="left">W/O Shrinkage</TableCell>
            <TableCell align="left">Planned Shrinkage</TableCell>
            <TableCell align="left">OverAll Shrinkage</TableCell> */}
            <TableCell align="left">Total Shrinkage</TableCell>

            <TableCell align="left">Leave Type</TableCell>
            <TableCell align="left">Approver1 Comment</TableCell>
            <TableCell align="left">Comments</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {rosterData?.length > 0 ? (
            rosterData.map((User) => (
              <TableRow key={User.Id}>
                <TableCell align="left">
                  <Checkbox 
                    value={User.Id}
                    onClick={checkedLeaveId}
                    checked={selectedLeave?.includes(User.Id)}
                  />
                  {User.Id}
                </TableCell>
                <TableCell align="left">{moment(User.ApplicationDate).format("DD MMMM")}</TableCell>
                {/* <TableCell align="left">{((User.weekOffs / User.totalAgents) * 100).toFixed(2)} %</TableCell>
                <TableCell align="left">{((User.currentShrinkage / User.totalAgents) * 100).toFixed(2)} %</TableCell>
                <TableCell align="left">
                  {User.onApprovalShrinkage > 0 ? `${(((User.onApprovalShrinkage + User.weekOffs) / User.totalAgents) * 100).toFixed(2)} %` : "-"}
                </TableCell> */}
                <TableCell align="left">
                {
                  totalShrinkage?.find((shrinkage) => shrinkage.Id === User.Id && User.Status == 3)?.TotalShrinkage ?? ""
                }
                </TableCell>
                <TableCell align="left">{User.LeaveType}</TableCell>
                <TableCell align="left">{User.Approver1Comment}</TableCell>
                <TableCell align="left">{User.Comment}</TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell align="center" colSpan={6}>No data available</TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default RosterWiseData;
