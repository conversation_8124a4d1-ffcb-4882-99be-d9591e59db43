import React, { useEffect, useState } from "react";
import { styled } from '@mui/material/styles';
import Button from '@mui/material/Button';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { connect } from "react-redux";
import { GetCommonData, InsertData, UpdateData, GetCommonspData, DeleteData, InsertRosterHistory } from "../store/actions/CommonAction";
import Select from 'react-select';
import * as XLSX from 'xlsx';
import { Typography, Box, Grid } from "@mui/material";
import { Link } from "react-router-dom";
import "../app/css/AttendanceDashboard.scss"
import Snackbar from '@mui/material/Snackbar';
import MuiAlert from '@mui/material/Alert';
import moment from "moment";
import _ from 'underscore';
import CircularProgress from '@mui/material/CircularProgress';

const VisuallyHiddenInput = styled('input')({
    clip: 'rect(0 0 0 0)',
    clipPath: 'inset(50%)',
    height: 1,
    overflow: 'hidden',
    position: 'absolute',
    bottom: 0,
    left: 0,
    whiteSpace: 'nowrap',
    width: 1,
});
const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const styles = {
    box: {
        width: 'auto',
        height: 'auto',
        maxWidth: '100%',
    },
};

const RosterUserHistoryUpload = (props) => {

    const [rosterProducts, setRosterProducts] = useState({
        rosterData: [],
        products: [],
        states: []
    });

    const sampleFile = "/RMSTemplate/SampleAgentList.xlsx";
    const stateMaster = "/RMSTemplate/StateMaster.xlsx";
    const Allindia = "/RMSTemplate/AllindiaSheet.xlsx";

    const [uploadDetails, setUploadDetails] = useState({
        uploadFile: null,
        uploadData: []
    });

    const [notify, setNotify] = useState({
        alertMsg: "",
        open: false,
        severity: ""
    });

    const [productSelected, setProductSelected] = useState(null);
    const [rosterSelected, setRosterSelected] = useState(null);
    const [stateSelected, setStateSelected] = useState(null);
    const [loaderOnOff, setLoaderOnOff] = useState(false);

    useEffect(() => {
        props.GetCommonspData({
            root: 'FetchRosterProducts',
            c: "R",
        }, (data) => {

            if (Array.isArray(data?.data?.data)) {
                let roster = data.data?.data[0];
                let products = data.data?.data[1];
                let states = data.data?.data[2];

                let allIndia = {StateId: 0, StateName: "All India"};

                states.push(allIndia);
                states.sort(function (a, b) {
                    return a.StateId - b.StateId;  // Ascending order
                  });
                
                // console.log(states);
                let rosterData = [];
                let productData = [];
                let stateData = [];

                for (let i = 0; i < roster.length; i++) {

                    rosterData.push({
                        label: moment(roster[i].StartDate).format('DD-MM-YY') + ' | ' + moment(roster[i].EndDate).format('DD-MM-YY'),
                        value: roster[i].Id
                    })
                }

                for (let i = 0; i < products.length; i++) {
                    productData.push({
                        label: products[i].ProductName || 'NULL',
                        value: products[i].ProductId
                    })
                }

                for (let i = 0; i < states.length; i++) {
                    stateData.push({
                        label: states[i].StateName || 'NULL',
                        value: states[i].StateId
                    })
                }

                setRosterProducts({
                    rosterData: rosterData,
                    products: productData,
                    states: stateData
                });
            }
        })
    }, [])



    const handleSave = () => {
        // console.log(uploadDetails.uploadData);
        const grpByProcessId = _.groupBy(uploadDetails.uploadData, 'ProcessId');
        const processIds = [];
        for (let key in grpByProcessId) {
            processIds.push(key);
        }
        if (stateSelected.value != 0 && uploadDetails?.uploadData.length > 0 && productSelected?.value && rosterSelected?.value 
            && rosterSelected?.value) {
            // console.log(processNames);
            setLoaderOnOff(true);
            InsertRosterHistory({
                excelData: uploadDetails.uploadData,
                RosterId: parseInt(rosterSelected.value),
                ProductId: parseInt(productSelected.value),
                StateId: parseInt(stateSelected.value),
                ProcessList: processIds
            }, (data) => {

                // console.log("The InsertRosterData is ", data);
                if (data?.data?.status && data.data.status == 200) {
                    setNotify({
                        alertMsg: "Roster History Uploaded",
                        open: true,
                        severity: 'success'
                    });
                    // alert("Roster History Uploaded");
                    setUploadDetails({
                        ...uploadDetails,
                        uploadData: [],
                        uploadFile: null
                    })
                    setProductSelected(null);
                    setRosterSelected(null);
                    setStateSelected(null);
                }
                else if (data?.response?.data?.status && data?.response?.data?.status == 500) {
                    setNotify({
                        alertMsg: `Unable to upload Roster History Please Check row number ${data?.response?.data?.sheetRow}` ,
                        open: true,
                        severity: 'error'
                    });
                    setUploadDetails({

                        uploadData: [],
                        uploadFile: null
                    })
                    setProductSelected(null);
                    setRosterSelected(null);
                    setStateSelected(null);
                }
                else {
                    // alert("Unable to upload Roster History");
                    setNotify({
                        alertMsg: "Unable to upload Roster History",
                        open: true,
                        severity: 'error'
                    });
                    setUploadDetails({

                        uploadData: [],
                        uploadFile: null
                    })
                    setProductSelected(null);
                    setRosterSelected(null);
                    setStateSelected(null);
                }
                setLoaderOnOff(false);
            })
        }
        else if (stateSelected.value == 0 && uploadDetails?.uploadData.length > 0 && productSelected?.value && rosterSelected?.value 
            && rosterSelected?.value) {
            // console.log(processNames);
            InsertRosterHistory({
                excelData: uploadDetails.uploadData,
                RosterId: parseInt(rosterSelected.value),
                ProductId: parseInt(productSelected.value),
                ProcessList: processIds,
                AllIndia: 1
            }, (data) => {

                // console.log("The InsertRosterData is ", data);
                if (data?.data?.status && data.data.status == 200) {
                    setNotify({
                        alertMsg: "Roster History Uploaded",
                        open: true,
                        severity: 'success'
                    });
                    // alert("Roster History Uploaded");
                    setUploadDetails({
                        ...uploadDetails,
                        uploadData: [],
                        uploadFile: null
                    })
                    setProductSelected(null);
                    setRosterSelected(null);
                    setStateSelected(null);
                }
                else {
                    // alert("Unable to upload Roster History");
                    setNotify({
                        alertMsg: "Unable to upload Roster History",
                        open: true,
                        severity: 'error'
                    });
                    setUploadDetails({

                        uploadData: [],
                        uploadFile: null
                    })
                    setProductSelected(null);
                    setRosterSelected(null);
                    setStateSelected(null);
                }
            })
        }
        else {
            if (!uploadDetails?.uploadData.length > 0) {
                setNotify({
                    alertMsg: "No Employee Records Present in the Excel Uploaded",
                    open: true,
                    severity: 'error'
                });
            }
            else if (!productSelected) {
                setNotify({
                    alertMsg: "Do select a product before saving",
                    open: true,
                    severity: 'error'
                })
            }
            else if (!rosterSelected) {
                setNotify({
                    alertMsg: "Do select a roster before saving",
                    open: true,
                    severity: 'error'
                })
            }
            else if (!rosterSelected) {
                setNotify({
                    alertMsg: "Do select a State before saving",
                    open: true,
                    severity: 'error'
                })
            }
            else {
                setNotify({

                    alertMsg: "Upload Issues",
                    open: true,
                    severity: 'error'
                })
            }
        }
    }

    const handleFileUpload = (event) => {

        const file = event.target.files[0];

        if (file) {
            const fileName = file.name;

            if (/\.(xls|xlsx)$/.test(fileName.toLowerCase())) {

                const reader = new FileReader();

                reader.onload = (e) => {
                    try {
                        const data = e.target.result;

                        const workbook = XLSX.read(data, { type: 'array' });

                        const sheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[sheetName];

                        const excelData = XLSX.utils.sheet_to_json(worksheet);

                        const uploadData = [];


                        for (let i = 0; i < excelData.length; i++) {

                            if(stateSelected.value == 0 && excelData[i].StateId &&
                                excelData[i].EmployeeId && excelData[i].ProcessId && excelData[i].TL_Ecode
                                && excelData[i].EmployeeId != 'null' && excelData[i].ProcessId != 'null' 
                                && excelData[i].TL_Ecode != 'null'){
                                    uploadData.push({
                                        EmployeeId: excelData[i].EmployeeId,
                                        // ProcessName: excelData[i].Process.toUpperCase(),  
                                        ProcessId: excelData[i].ProcessId,
                                        ManagerId: excelData[i].TL_Ecode,
                                        StateId: excelData[i].StateId,
                                    })
                            }
                            else if (stateSelected.value != 0 && excelData[i].EmployeeId && excelData[i].ProcessId && excelData[i].TL_Ecode
                                && excelData[i].EmployeeId != 'null' && excelData[i].ProcessId != 'null' && excelData[i].TL_Ecode != 'null') {
                                uploadData.push({
                                    EmployeeId: excelData[i].EmployeeId,
                                    // ProcessName: excelData[i].Process.toUpperCase(),  
                                    ProcessId: excelData[i].ProcessId,
                                    ManagerId: excelData[i].TL_Ecode,
                                })
                            }
                            else {

                                event.target.value = null;
                                setUploadDetails({

                                    uploadData: [],
                                    uploadFile: null
                                })

                                setNotify({

                                    open: true,
                                    alertMsg: "Make sure the excel file uploaded has all columns and values mentioned in the sample file",
                                    severity: 'error'
                                })
                                // alert('Make sure the excel file uploaded has all columns mentioned in the sample file');
                                return;

                            }
                        }

                        if (uploadData.length > 0) {

                            setUploadDetails({
                                ...uploadDetails,
                                uploadData: uploadData,
                                uploadFile: event
                            })
                            // event.target.value=null;

                        }
                        // console.log('Excel data:', excelData);
                    } catch (error) {

                        console.error('Error parsing Excel file:', error);
                    }
                };

                reader.readAsArrayBuffer(file);
            } else {
                setNotify({

                    open: true,
                    alertMsg: "Invalid file type. Please upload an Excel file.",
                    severity: 'error'
                });
                setUploadDetails({
                    uploadFile: null,
                    uploadData: []
                })
                // alert('Invalid file type. Please upload an Excel file.');
                event.target.value = null;
            }

        }

    };

    const handleClose = (event, reason) => {
        if (reason === 'clickaway') {
            return;
        }
        setNotify(
            {
                ...notify,
                open: false
            }
        )
    }

    const handleChangeRoster = (e) => {
        setRosterSelected(e);
    }

    const handleChangeProduct = (e) => {
        setProductSelected(e)
    }

    const handleChangeState = (e) => {
        setStateSelected(e)
    }

    return (

        <Box
            component="div"
            display="flex"
            flexDirection="column"
            // height="40%"
            // width="40%"
            border={1}
            borderColor="black"
            p={2}
            m={2}
            style={styles.box}
        >
            <Grid container spacing={4}>
                <Grid item xs={3}>
                    <label className="RosterLabels">Select Products</label>
                    <Select
                        options={rosterProducts.products || []}
                        onChange={handleChangeProduct}
                        components={{
                            IndicatorSeparator: () => null
                        }}
                        className="RosterMaster"
                        value={productSelected}
                    />
                </Grid>
                <Grid item xs={3}>
                    <label className="RosterLabels">Select Roster</label>
                    <Select
                        options={rosterProducts.rosterData || []}
                        onChange={handleChangeRoster}
                        components={{
                            IndicatorSeparator: () => null
                        }}
                        className="RosterMaster"
                        value={rosterSelected}
                    />
                </Grid>

                <Grid item xs={3}>
                    <label className="RosterLabels">Select State</label>
                    <Select
                        options={rosterProducts.states || []}
                        onChange={handleChangeState}
                        components={{
                            IndicatorSeparator: () => null
                        }}
                        className="RosterMaster"
                        value={stateSelected}
                    />
                </Grid>

                <Grid item xs={3}>
                    <Grid container justifyContent="flex-end">
                        <Grid item style={{ margin: '5px'}}>
                            {sampleFile && (
                                <Link to={sampleFile} target="_blank" download>
                                    <Button variant="contained" startIcon={<FileDownloadIcon />}>
                                        Download Sample Excel File
                                    </Button>
                                </Link>
                            )}
                        </Grid>
                        
                        <Grid item style={{ margin: '5px'}}>
                            {Allindia && (
                                <Link to={Allindia} target="_blank" download>
                                    <Button variant="contained" startIcon={<FileDownloadIcon />}>
                                        Download All India Sample File
                                    </Button>
                                </Link>
                            )}
                        </Grid>

                        <Grid item style={{ margin: '5px'}}>
                            {stateMaster && (
                                <Link to={stateMaster} target="_blank" download>
                                    <Button variant="contained" startIcon={<FileDownloadIcon />}>
                                        Download State File
                                    </Button>
                                </Link>
                            )}
                        </Grid>
                    </Grid>
                </Grid>
                <Grid item xs={12}>
                    <h2 className="heading">Upload Roster User Details</h2>
                </Grid>

                <Grid item xs={12} sm={4}>
                    <Button component="label" variant="contained" startIcon={<CloudUploadIcon />}>
                        {uploadDetails?.uploadFile
                            ? uploadDetails.uploadFile.target.files[0].name
                            : "Upload File"}
                        <VisuallyHiddenInput key={Math.random()} onChange={handleFileUpload} type="file" />
                    </Button>
                </Grid>
                {uploadDetails?.uploadData.length > 0 &&
                    <Grid item xs={12} sm={4}>
                        <Button component="label" variant="contained" onClick={handleSave}>
                            Save 
                            {loaderOnOff && <CircularProgress color="secondary" size = "30px" /> }
                        </Button>
                    </Grid>
                } 
            </Grid>
            {notify && notify.alertMsg.length > 0 &&
                <Snackbar open={notify.open} autoHideDuration={2800} onClose={handleClose}
                anchorOrigin={{ vertical: 'top', horizontal : 'right'}} >
                    <Alert onClose={handleClose} severity={notify.severity} sx={{ width: '100%' }}>
                        {notify.alertMsg}
                    </Alert>
                </Snackbar>
            }
        </Box>

        // <div>     
        //       <Box
        //   component="div" 
        //   display="flex"   
        //   flexDirection="column"  

        //   height="80%"          
        // width="80%"
        //     border={1}  
        //  borderColor="black"
        //   p={2}                  
        //   m={2}                  
        // >
        //     <Grid Container spacing={2}>
        //     <Grid item xs={12}>
        //     {
        //         sampleFile &&

        //         <Link to={sampleFile} target="_blank" download>

        //         <Button component="label" variant="contained" startIcon={<FileDownloadIcon/>}>
        //             Download Sample Excel File
        //         </Button>
        //         </Link>

        //         }
        //     </Grid>
        //     <Grid item xs={12}>
        //         <Typography>Upload Roster User Details </Typography>
        //     </Grid>


        //         <Grid item xs={2}>
        //         <Button component="label" variant="contained" startIcon={<CloudUploadIcon />}>
        //             {uploadDetails?.uploadFile ? uploadDetails.uploadFile.target.files[0].name : "Upload File"}
        //             <VisuallyHiddenInput onChange={handleFileUplaod} type="file" />
        //         </Button>
        //         </Grid>
        //         <Grid item xs={6}>
        //         <Button component="label" variant="contained" onClick={handleSave}>
        //             Save
        //         </Button>
        //         </Grid>

        //     </Grid>
        //         </Box>

        // </div>
    )
}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData,
        InsertData,
        UpdateData,
        DeleteData
    }
)(RosterUserHistoryUpload);
