const ExcelFileColumns = {
    UploadIncentiveData: {
        15 : ['ECode', 'superGroupName', 'Sourcedbkgs', 'IssuedBkgs', 'WeightedAPE', 'IssuedAPE', 'Slab', 'Slab11', 'AmountMade', 'QualityScore', 'QualityScore1', 'CRTDeduction', 'FinalIncentive'],
        17 : [ 'SuperGroupId', 'LowerRange', 'UpperRange', 'Weightage', 'Type', 'SubCategoryID' ],
        18 : [ 'FloorProcess', 'SuperGroupId' ],
        20 : [ 'EmployeeId', 'Tenure', 'FloorProcess', 'TLEmployeeId', 'AMEmployeeId', 'ManagerEmployeeId','TargetAPE', 'TargetBookings', 'ProductId', 'Location', 'RoleType']
    }
}

module.exports = {
    ExcelFileColumns
};