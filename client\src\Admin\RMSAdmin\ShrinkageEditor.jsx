import React, { useState, useEffect, useRef } from 'react';
import { connect } from "react-redux";
import { GetCommonData, InsertData, UpdateData, GetCommonspData, DownloadSurveyData, DeleteData, InsertCourseData, UpdateCourseData } from "../store/actions/CommonAction";

import {
  List,
  ListItem,
  ListItemText,
  Menu,
  MenuItem,
  Box,
  styled,
  Table,
  TableBody,
  TableHead,
  TableRow,
  Container,
  TableContainer,
  Paper,
} from '@mui/material';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import Snackbar from '@mui/material/Snackbar';
import MuiAlert from '@mui/material/Alert';

import moment from 'moment';
import ShrinkagePopup from './Components/ShrinkagePopup';
import { ShrinkageContext } from './Context/ConfigContext';

const MenuRoot = styled('div')(({ theme }) => ({
  width: '100%',
  maxWidth: 360,
  backgroundColor: theme.palette.background.paper
}));

const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

// const StyledTable = styled(Table)(({ theme }) => ({
//   whiteSpace: "pre",
//   "& thead": {
//     "& tr": { "& th": { paddingLeft: 0, paddingRight: 0 } },
//   },
//   "& tbody": {
//     "& tr": { "& td": { paddingLeft: 0, textTransform: "capitalize" } },
//   },
// }));



const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.common.black,
    color: theme.palette.common.white,
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:nth-of-type(odd)': {
    backgroundColor: theme.palette.action.hover,
  },
  // hide last border
  '&:last-child td, &:last-child th': {
    border: 0,
  },
}));

function createData(name, calories, fat, carbs, protein) {
  return { name, calories, fat, carbs, protein };
}

const rows = [
  createData('Frozen yoghurt', 159, 6.0, 24, 4.0),
  createData('Ice cream sandwich', 237, 9.0, 37, 4.3),
  createData('Eclair', 262, 16.0, 24, 6.0),
  createData('Cupcake', 305, 3.7, 67, 4.3),
  createData('Gingerbread', 356, 16.0, 49, 3.9),
];

const ShrinkageEditor = (props) => {

  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [processData, setProcessData] = useState([]);
  const [rosterData, setRosterData] = useState([{ text: "Select the Roster" }]);
  const [processShrinkage, setProcessShrinkage] = useState([]);
  const [rosterSelected, setRosterSelected] = useState(0);
  const [rosterAnchor, setRosterAnchor] = useState(null);
  const [validRange, setValidRange] = useState(
    { start: '', end: '' }
  );
  const [validDates, setValidDates] = useState([]);
  const shrinkageData = useRef([]);
  const LeaveCategory = ['Select a Leave Type', 'Weekly Off', 'Other Leaves'];
  const shrinkageDataByCategory = useRef({});
  const [shrinkageDisplay, setShrinkageDisplay] = useState({});
  const [popup, setPopup] = useState(0);
  const [popupData, setPopupData] = useState({});
  const [alertMsg,setAlertMsg]= useState("");
  const [open,setOpen]= useState(false);
  const [severity, setSeverity]=useState('');

  useEffect(() => {

    props.GetCommonspData({
      root: "GetProcessRosterDefaultShrinkage",
      c: "R",
    },
      (data) => {
        // console.log("The data is ", data);
        if (Array.isArray(data?.data?.data)) {
          let prodata = data.data.data[2];


          let processdata = [{ Processid: 0, ProcessName: 'Select a process' }];
          for (let i = 0; i < prodata.length; i++) {
            processdata.push(prodata[i]);
          }
          shrinkageData.current = data.data.data[3];
          setProcessData(processdata);
          let newRoster = [...rosterData, ...data.data.data[0]];
          // console.log("The newRoster is ", newRoster);
          setRosterData(newRoster);
          setProcessShrinkage(data.data.data[1]);

        }
      })
  }, [])

  useEffect(() => {
    const dates = [];
    let currentDate = new Date(validRange.start);

    let endDate = new Date(validRange.end)
    while (currentDate <= endDate) {
      dates.push(new Date(currentDate));

      currentDate.setDate(currentDate.getDate() + 1);
    }

    setValidDates(dates);
  }, [validRange])


  const handleClickListItem = (event) => {

    setAnchorEl(event.currentTarget);

  }

  const handleMenuItemClick = (event, index) => {

    setSelectedIndex(index);
    setAnchorEl(null);

  }

  // useEffect(()=>{
  //   console.log("alert ms")
  //    if(alertMsg && alertMsg.length>0)
  //    {
  //     setOpen(true);
  //    }
  // },[alertMsg])

  const handleClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }

    setOpen(false);
  };

  useEffect(() => {
    if (selectedIndex == 0 && Object.keys(shrinkageDataByCategory.current).length > 0) {
      alert('Select a Leave Category');
    }
    else if (selectedIndex == 1 && shrinkageDataByCategory.current.hasOwnProperty('1')) {

      let tempArray = shrinkageDataByCategory.current['1'];
      debugger;
      let tempObj = rosterShrinkageByProperty(tempArray, 'ProcessId');

      setShrinkageDisplay(tempObj);
    }
    else if (selectedIndex == 2 && shrinkageDataByCategory.current.hasOwnProperty('2')) {
      let tempArray = shrinkageDataByCategory.current['2'];
      let tempObj = rosterShrinkageByProperty(tempArray, 'ProcessId');

      setShrinkageDisplay(tempObj);
    }
  }, [shrinkageDataByCategory.current, selectedIndex])


  const handleRosterListClick = (event) => {
    setRosterAnchor(event.currentTarget);
  }

  const rosterShrinkageByProperty = (array, property1) => {
    return array.reduce((result, item) => {
      const combinedKey = `${item[property1]}`;
      if (!result[combinedKey]) {
        result[combinedKey] = [];
      }
      result[combinedKey].push(item);
      return result;
    }, {});
  }

  const validRoster = (selectedRoster) => {

    let validDate = {
      start: moment(selectedRoster.StartDate).format('YYYY-MM-DD'),
      end: moment(selectedRoster.EndDate).format('YYYY-MM-DD')
    };
    setValidRange(validDate);
    let gtShrinkage = shrinkageData.current;
    let rosterShrinkage = [];
    for (let i = 0; i < gtShrinkage.length; i++) {
      if (gtShrinkage[i].RosterId == selectedRoster.Id) {
        rosterShrinkage.push(gtShrinkage[i]);
      }
    }

    shrinkageDataByCategory.current = rosterShrinkageByProperty(rosterShrinkage, 'LeaveCategoryId');
    //  console.log("SOME DATA ", shrinkageDataByCategory);

  }

  const handleMenuRosterClick = (event, index) => {
    validRoster(rosterData[index]);
    setRosterSelected(index);
    setRosterAnchor(null);
  }

  const processClose = () => {
    setAnchorEl(null);
  }
  const rosterClose = () => {
    setRosterAnchor(null);
  }


  const openPopup = (e, dataShrinkage) => {
    setPopupData(dataShrinkage);
    setPopup(1);
  }

  const closePopup = () => {
    setPopup(0);
  }

  const saveNewShrinkage = (data, newShrinkage, alertMsg) => {
    // console.log(alertMsg);
    if(alertMsg?.success)
    {
    if (shrinkageDataByCategory.current && shrinkageDataByCategory.current.hasOwnProperty(data.LeaveCategoryId)) {
      let tempArray = shrinkageDataByCategory.current[data.LeaveCategoryId];

      for (let i = 0; i < tempArray.length; i++) {
        if (data.Id == tempArray[i].Id) {
          // console.log("Lets check it ",shrinkageDataByCategory.current[data.LeaveCategoryId][i].Shrinkage)
          shrinkageDataByCategory.current[data.LeaveCategoryId][i].Shrinkage = newShrinkage;
          break;
        }
      }


    }
    setSeverity('success');
    }
    else{
      setSeverity('error');
    }
     
    setAlertMsg(alertMsg?.Msg);
    setOpen(true);
    setPopup(0);
  }

  return (
    <ShrinkageContext.Provider value={{ popup: popup, setPopup: closePopup, handleSave: saveNewShrinkage }}>
      <Container>
        <MenuRoot>
          {LeaveCategory && LeaveCategory.length > 0 &&
            <List component="nav" aria-label="Device settings">
              <ListItem
                button
                aria-haspopup="true"
                aria-controls="lock-menu"

                onClick={handleClickListItem}
              >
                <ListItemText primary secondary={LeaveCategory[selectedIndex]} />
              </ListItem>
            </List>
          }
          <Menu
            id="lock-menu"
            anchorEl={anchorEl}
            keepMounted
            open={Boolean(anchorEl)}
            onClose={processClose}
          >

            {LeaveCategory.length > 0 &&
              LeaveCategory.map((leave, index) => (
                <MenuItem
                  key={leave}
                  disabled={index === 0}
                  selected={index === selectedIndex}
                  onClick={(event) => handleMenuItemClick(event, index)}
                >
                  {leave}
                </MenuItem>
              ))}
          </Menu>

          {rosterData.length > 0 &&
            <List component="nav" aria-label="Device settings">
              <ListItem
                button
                aria-haspopup="true"
                aria-controls="lock-menu"

                onClick={handleRosterListClick}
              >
                <ListItemText primary secondary={rosterSelected == 0 ? rosterData[rosterSelected].text : moment(rosterData[rosterSelected].StartDate).format('MMM Do YY') + " - " + moment(rosterData[rosterSelected].EndDate).format('MMM Do YY')} />
              </ListItem>
            </List>
          }
          <Menu
            id="lock-menu"
            anchorEl={rosterAnchor}
            keepMounted
            open={Boolean(rosterAnchor)}
            onClose={rosterClose}
          >

            {rosterData.length > 0 &&
              rosterData.map((roster, index) => (
                <MenuItem
                  key={roster.Id}
                  disabled={index === 0}
                  selected={index === rosterSelected}
                  onClick={(event) => handleMenuRosterClick(event, index)}
                >
                  {index == 0 ? roster.text : moment(roster.StartDate).format('MMM Do YY') + " - " + moment(roster.EndDate).format('MMM Do YY')}
                </MenuItem>
              ))}
          </Menu>

        </MenuRoot>
        {
          validDates?.length > 0 &&
          <TableContainer component={Paper} className="rostershrinkage">
            <Table sx={{ minWidth: 700 }} aria-label="customized table">
              <TableHead>
                <TableRow>
                  <StyledTableCell>Process</StyledTableCell>
                  {
                    validDates.map((date, index) => {

                      return (
                        <StyledTableCell align="right">{moment(date).format('DD/MM/YY')}</StyledTableCell>


                      )

                    })
                  }


                </TableRow>
              </TableHead>
              <TableBody>

                {

                  processData.length > 0 && processData.map((process, index) => {
                    if (index == 0) {
                      return null;
                    }
                    else {
                      return (
                        <StyledTableRow key={process.Processid} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                          <StyledTableCell component="th" scope="row">{process.ProcessName}</StyledTableCell>
                          {
                            shrinkageDisplay.hasOwnProperty(process.Processid) &&
                            shrinkageDisplay[process.Processid].map((shrinkageDaily) => {
                              return (

                                <StyledTableCell align="right" onClick={(e) => openPopup(e, shrinkageDaily)}>{shrinkageDaily.Shrinkage + "%"}</StyledTableCell>

                              )
                            })

                          }
                        </StyledTableRow>

                      )
                    }
                  })
                }

              </TableBody>
            </Table>
          </TableContainer>

        }
      </Container>
      {popup && <ShrinkagePopup data={popupData}></ShrinkagePopup>}
      {alertMsg && alertMsg.length>0 &&
      <Snackbar open={open} autoHideDuration={6000} onClose={handleClose}>
      <Alert onClose={handleClose} severity={severity} sx={{ width: '100%' }}>
        {alertMsg}
      </Alert>
    </Snackbar>
      }
    </ShrinkageContext.Provider>
  )


}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    GetCommonspData,
    InsertData,
    UpdateData,
    DeleteData
  }
)(ShrinkageEditor);