import { React, useState, useEffect } from 'react';
import { Grid, Table, TableBody, TableContainer, TableCell, TableHead, TableRow, Checkbox } from '@mui/material';
import "../app/css/ApprovalDashboard.scss"
import Button from '@mui/material/Button';
import { ApplyAutoLeave } from '../store/actions/CommonAction';


const SystemApplyLeaves = () => {

    const [status, setStatus] = useState(false);
    
    const ApplyLeaves = () => {
        setStatus(true);
        ApplyAutoLeave(function (results) {
            if (results && results.data && results.data.status == 200) {
                setStatus(false);
            }
          });
    }

    return(
        <>
            <h1 className="mt-20">ApplyAutoLeave</h1>
            <Button className="mt-20" variant="contained" onClick={ApplyLeaves}>{status?'Inserting Records...':'Apply'}</Button>
        </>
    )
}

export default SystemApplyLeaves;