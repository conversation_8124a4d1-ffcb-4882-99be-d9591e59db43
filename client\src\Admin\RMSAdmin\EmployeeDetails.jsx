import React, { useState, useEffect } from 'react';

import moment from 'moment';
import "../app/css/AttendanceDashboard.scss"

import { GetAgentAttendanceOnDate, GetAllManagerIds, GetAgentEligibleOnDate } from './Common/Utilities';
import LeaveDetails from './LeaveDetails';



const EmployeeDetails = (props) => {
    // console.log(props);
    const { agentInfo, rosterDates, agentLeavesData, attendanceAgentWiseData, eligibleDates, toggleView  } = props;

    return <tr key={agentInfo.EmployeeId}>
          <td>{agentInfo.EmployeeId || '-'}</td>
          {/* <td>{EmployeeName?.substring(0, 20) + '...' || '-'}</td> */}
          <td>{agentInfo.EmployeeName || '-'}{agentInfo.UserType == 'GOLD' || agentInfo.UserType == 'SILVER' ? '*' : ''}</td>
          <td className="HideColumn">{agentInfo.TLName || '-'}</td>
          <td className="HideColumn">{agentInfo.TLEcode || '-'}</td>
    
          {Array.isArray(rosterDates) && rosterDates.map((date, ind2) => {

            return <LeaveDetails agentInfo={agentInfo} rosterDates={rosterDates} agentLeavesData = {agentLeavesData}
            attendanceAgentWiseData = {attendanceAgentWiseData} eligibleDates = {eligibleDates} 
            toggleView = {toggleView} date = {date}/>
           
            // let data = agentLeavesData[agentInfo.EmployeeId] || [];
            
    
            // let isPresent = GetAgentAttendanceOnDate(attendanceAgentWiseData[agentInfo.EmployeeId], date);
            // let isEliglible = toggleView && GetAgentEligibleOnDate(eligibleDates[agentInfo.EmployeeId], date);
    
            // let isLeave = false, leaveType = '';
            // let isDatePast = moment(date).isSameOrBefore(agentInfo.currentDate)
    
            // for (let index = 0; index < data.length; index++) {
            //   const element = data[index];
            //   const applicationDate = moment(element?.ApplicationDate).format('YYYY-MM-DD');
            //   if (date === applicationDate) {
            //     isLeave = true;
            //     leaveType = element.LeaveTypeId;
            //   }
            // }
            // let Rank = 0;
            // if (data[0]?.Week1) {
            //   Rank = data[0]?.Week1;
            // } else {
            //   Rank = data[0]?.Week2;
            // }
    
            // if (isLeave) {
            //   switch (leaveType) {
            //     case 1:
            //       return <td className={isPresent ? "weeklyOf AgentUnplanned" : "weeklyOf"}>{isPresent ? `W/O / P ${toggleView && Rank>0 ? `(${Rank})` : ''}` : `W/O ${toggleView ? `(${Rank})` : ''}`}</td>
            //     case 2:
            //       return <td className={isPresent ? "earnedLeave AgentUnplanned" : "earnedLeave"}>{isPresent ? "EL / P" : "EL"}</td>
            //     case 3:
            //       return <td>E. W/O</td>
            //     case 4:
            //       return <td>SL</td>
            //     case 5:
            //       return <td className={isPresent ? "causalLeave AgentUnplanned" : "causalLeave"} >{isPresent ? "CL / P" : "CL"}</td>
            //     // default:
            //     //   return <td className='AgentPresent'>P</td>
            //   }
    
            // } else {
            //   if (toggleView && isEliglible) {
            //     // return <td className={isEliglible && "eligibleDates" } >{isPresent ? "P" : "AB"}</td>
            //     if (isDatePast && isPresent !== null) {
            //       return <td className='eligibleDates' >{isPresent ? "P*" : "AB*"}</td>
            //     } else {
            //       // return <td className={isPresent ? "AgentPresent" : "AgentAbsent" } >{isPresent ? "P" : "AB" }</td>
            //       return <td className='eligibleDates'>P*</td>
            //     }
    
            //   }
            //   else {
            //     if (isDatePast && isPresent !== null) {
            //       return <td className={isPresent ? "AgentPresent" : "AgentAbsent"} >{isPresent ? "P" : "AB"}</td>
            //     } else {
            //       // return <td className={isPresent ? "AgentPresent" : "AgentAbsent" } >{isPresent ? "P" : "AB" }</td>
            //       return <td className='AgentPresent'>P</td>
            //     }
            //   }
    
            // }
          })
          }
        </tr>
}

export default EmployeeDetails;

