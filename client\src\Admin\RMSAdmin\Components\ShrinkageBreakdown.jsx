import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import moment from "moment";
import _ from 'underscore';

const GetUniqueElementsCount = (value) => {
  const data = _.groupBy(value, 'EmployeeId');
  const count = Object.keys(data)?.length || 0;
  return count;
} 

const GetAttendanceCount = (value) => {
  // console.log(value)
  // const data = _.groupBy(value, 'EmployeeId');
  // const count = Object.keys(data)?.length || 0;
  let count = 0;
  for(let i=0; i < value.length; i++){
    if(value[i].IsPresent){
      count++;
    }
  }
  return count;
} 

const ShrinkageBreakdown = (props) => {
  const [overallShrinkage, setOverallShrinkage] = useState(null);
  const {
    rosterDates,
    leavesDateWise,
    agentsData,
    attendanceDateWiseData
  } = props;

  const currentDate = moment(new Date()).format('YYYY-MM-DD');

  useEffect(() => {

    let totalShrinkage = 0;
    for (let index = 0; index < rosterDates.length; index++) {
      let date = rosterDates[index];
      let totalEmployees = agentsData.length || 0;
      let count = moment(date).isSameOrBefore(currentDate) ?  
       (totalEmployees -  GetAttendanceCount(attendanceDateWiseData[date] ||  []) )
        :
        GetUniqueElementsCount(leavesDateWise[date] ||  []);
      
      // let count = leavesDateWise[date]?.length || 0;
      let estimatedShrinkage = totalEmployees > 0 ? (count * 100 / totalEmployees) : 0;
      totalShrinkage += estimatedShrinkage;

    }
    if (rosterDates.length > 0) {
      setOverallShrinkage(parseFloat(totalShrinkage / rosterDates.length).toFixed(2))
    }

  }, [props]);


  return (
    <>
      <tr>
        <td rowSpan="2">OVERALL SHRINKAGE <span>{overallShrinkage}%</span></td>
        <td>Present <br /> Shrinkage%</td>
        <td className="HideColumn"></td>
        <td className="HideColumn"></td>
       
        {Array.isArray(rosterDates) && rosterDates.map((item, ind) => {
          let date = item;
          // let count = leavesDateWise[date]?.length || 0;
          let count = GetUniqueElementsCount(leavesDateWise[date] ||  [])
          let totalEmployees = agentsData.length || 0;
          let estimatedShrinkage = (totalEmployees > 0 && count > 0) ? parseFloat((count) * 100 / totalEmployees).toFixed(2) : 0;

          return <td key={ind}>({totalEmployees - count}/{totalEmployees})  <br /> {estimatedShrinkage}% </td>
        }

        )}
      </tr>
      <tr>
      <td>Present <br /> Actual Shrinkage%</td>
      <td className="HideColumn"></td>
      <td className="HideColumn"></td>

        {Array.isArray(rosterDates) && rosterDates.map((date, ind) => {
          // let count = attendanceDateWiseData[date]?.length || 0;
          let count = GetAttendanceCount(attendanceDateWiseData[date] ||  [])
          let AppliedLeaves = GetUniqueElementsCount(leavesDateWise[date] ||  [])
          let totalEmployees = agentsData.length || 0;
          let absentEmployees = totalEmployees - count;
          let actualShrinkage = (absentEmployees > 0) ? parseFloat(absentEmployees * 100 / totalEmployees).toFixed(2) : 0;
          // let actualShrinkage = (count > 0) ? parseFloat((totalEmployees - count) * 100 / totalEmployees).toFixed(2) : 0;
          let defaultShrinkage = (AppliedLeaves > 0) ? parseFloat((AppliedLeaves) * 100 / totalEmployees).toFixed(2) : 0;
          if(moment(date).isSameOrBefore(currentDate)){
            // return <td key={ind}>({count}/{totalEmployees}) <br /> {actualShrinkage}% </td>
            if(count > 0){
              return <td key={ind}>({count}/{totalEmployees}) <br /> {actualShrinkage}% </td>
            }
            else{
              return <td key={ind}>({totalEmployees-AppliedLeaves}/{totalEmployees}) <br /> {defaultShrinkage}% </td>
            }
              
          } else {
            return  <td>N/A <br /> -</td>
          }
        }

        )}
      </tr>

    </>
  )

}

function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
  }
)(ShrinkageBreakdown);