
import React from "react";
import { useState, useEffect } from "react";
import { ButtonGroup, Button, Modal, Form  } from 'react-bootstrap';
import { connect } from "react-redux";
import { If, Then } from 'react-if';
// reactstrap components
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Table,
    Row,
    Col
  } from "reactstrap";

import {
    GetCommonData,GetCommonspData
} from "../../store/actions/CommonAction";
import DropDownAgent from "./DropDownAgent";

import { fnRenderfrmControl, fnDatatableCol, fnCleanData, GetJsonToArray, getUrlParameter, getuser } from '../../utility/utility.jsx';


const ModalFosAllocationPanel = (props)=>{
        let item;
        //const [SelectedValue,setSelectedValue] = useState();

        let { value, Group, onChange, visible, items } = props;
    
        let [showModal, setshowModal] = useState(true);
        let FormTitle = "Assigned Lead";
        //let url= `http://localhost:3000/admin/AgentList?Group=${Group}&handleClick=${handleClick}`        

        useEffect(()=>{
           
        },[])


        const AgentList = {
            config:
            {
              root: "GetFosAllocationAgent",
              //cols: ["UserId"],
              con: [{ "Isactive": 1 }],
              //state: false
            }
            
          }

    const handleClose = () => {
        props.handleClose();
      }

      const handledropChange = (e) => {
        props.setSelectedValue(e.target.value);
      }

      const handleClick = () => {
          props.handleClick();
          handleClose();
      }

    return (

        <div>

<Modal show={showModal} onHide={props.handleClose} >
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form >
                <Row>
                <Col md={11}>
                <Form.Label><i>*</i>Select Agent To Assign</Form.Label>
                <Form.Group controlId="Select Agent" >
                <DropDownAgent firstoption="Select" Group={Group}  col={AgentList} onChange={handledropChange}  >
                </DropDownAgent>
                </Form.Group>
                      
            </Col>

                <Col md={1}>
               
            </Col>
                
                </Row>
              </Form>
            </Modal.Body>
            <Modal.Footer>
            <Button variant="secondary" onClick={handleClick}>
                Save
          </Button>
              <Button variant="secondary" onClick={handleClose}>
                Close
          </Button>
              
            </Modal.Footer>
          </Modal>
        
            
        </div>
        
    );
    
    
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData
    }
)(ModalFosAllocationPanel);


