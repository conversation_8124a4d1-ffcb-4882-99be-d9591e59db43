
import React from "react";
import { Form } from 'react-bootstrap';

// reactstrap components
import moment from 'moment';

import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css'


import { Row, Col } from 'react-bootstrap';



class Date extends React.Component {
    constructor(props) {
        super(props);
        this.handleStartDateChange = this.handleStartDateChange.bind(this);
        this.handleEndDateChange = this.handleEndDateChange.bind(this);

        this.state = {
            startdate: moment().format("YYYY-MM-DD"),            
        }
    }
    componentDidMount() {

    }
    componentWillReceiveProps(nextProps) {

    }

    handleStartDateChange = (e, props) => {
        if (e._isAMomentObject) {
            this.props.onStartDate(e.format("YYYY-MM-DD"));
            this.setState({ startdate: e.format("YYYY-MM-DD"), enddate: e.add(30, 'days').format("YYYY-MM-DD") }, function () {
            });
        }
    }

    handleEndDateChange = (e, props) => {
        if (e._isAMomentObject) {
            this.props.onEndDate(e.format("YYYY-MM-DD"));
            this.setState({ enddate: e.format("YYYY-MM-DD") }, function () {
                //this.fetchCallBackData();
            });
        }
    }
    validation = (currentDate) => {
        return currentDate.isBefore(moment());
    };

    validationEndDate = (currentDate) => {
        

        if (!currentDate.isBefore(moment(this.state.enddate))) {
            return false;
        }

        if(currentDate.isBefore(moment(this.state.startdate))) {
            return false;
        }
        
        return true;
        
    };

    getSelectedDateRange() {
        return {
            startdate: this.state.startdate,
            enddate: this.state.enddate
        }
    }

    render() {


        let { visible } = this.props;

        if (visible == false) {
            return null;
        }
        return (

            <>
                    <Form.Group controlId="startdate_field">
                        <Datetime value={new Date()}
                            dateFormat="YYYY-MM-DD"
                            value={this.state.startdate}
                            isValidDate={this.validation.bind(this)}
                            onChange={moment => this.handleStartDateChange(moment)}
                            utc={true}
                            timeFormat={false}
                            className="form-group"
                        />
                    </Form.Group>
            </>

        );
    }
}


export default Date;

