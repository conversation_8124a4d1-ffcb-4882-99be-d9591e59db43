import * as React from 'react';
import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import SingleApproval from './SingleApproval';
import LeavesHistory from './LeavesHistory';

const SingleApprovalTabs = (props) => {
  const [value, setValue] = React.useState('1');

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <Box sx={{ width: '100%', typography: 'body1' }}>
      <TabContext value={value}>
       
          <TabList onChange={handleChange} aria-label="lab API tabs example">
            <Tab label="Approval" value="1" />
            <Tab label="Leaves History" value="2" />
            {/* <Tab label="Item Three" value="3" /> */}
          </TabList>
        

        <TabPanel value="1">
          <SingleApproval userId={props.userId} managerId={props.managerId} setLeaveApproved = {props.setLeaveApproved} 
          totalShrinkage = {props.totalShrinkage} />
        </TabPanel>

        <TabPanel value="2">
          <LeavesHistory userId={props.userId} />
        </TabPanel>
        {/* <TabPanel value="3">Item Three</TabPanel> */}
      </TabContext>
    </Box>
  );
};

export default SingleApprovalTabs;
