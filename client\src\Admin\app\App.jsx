
import { React, useState, useEffect } from "react";
import { CssBaseline } from '@mui/material';
import { useRoutes } from 'react-router-dom';
import { MatxTheme } from './components';
import { AuthProvider } from './contexts/JWTAuthContext';
import { SettingsProvider } from './contexts/SettingsContext';
import routes from './routes';
import '../fake-db';
import { connect } from "react-redux";
import {
  MenuAuthentication, GetCommonspDataV2
} from './../store/actions/CommonAction';


const App = (props) => {
  const content = useRoutes(routes);
  const [status, setStatus] = useState(false)
  let incomingURL = content.props.match.params['*'];
  let urls = content.props.match.route.children;

  useEffect(()=>{
    for(let i = 0 ; i< urls.length; i++) {
      let url = urls[i].path.slice(1);
      if(url === incomingURL){
        let MenuId = urls[i].MenuId;
        console.log(url);
        try{
          // MenuAuthentication(MenuId, function (result) {
          //   if(result.data.results){
          //     setStatus(true);
          //   }else{
          //     setStatus(false);
          //   }
            
          // });
          props.GetCommonspDataV2({
            root: 'GetPermissions',
            // params: [{ MenuId: MenuId }],
            c: "R",
          }, function (errorStatus, result) {
            if (!errorStatus) {
              let arr = []
              // console.log(result)
              if (result && result.data && result.data[0].length > 0) {
                let menus = result.data[0];
                for(let i = 0 ; i < menus.length; i++){
                  if(menus[i].MenuId == MenuId){
                      setStatus(true);
                      break;
                  }
                }
                // setStatus(true);
              } else {
                setStatus(false);
              }
            }
          });
      }
      catch (ex) {
      }
      }
    }
  
  },[incomingURL])


  return (
    <SettingsProvider>
      <AuthProvider>
        <MatxTheme>
          <CssBaseline />
          {status? content : content}
        </MatxTheme>
      </AuthProvider>
    </SettingsProvider>
  );
};

// export default App;

function mapStateToProps(state) {
  return {
  };
}

export default connect(
  mapStateToProps,
  {
    // GetCommonData,
    // GetCommonspData,
    GetCommonspDataV2
  }
)(App);
