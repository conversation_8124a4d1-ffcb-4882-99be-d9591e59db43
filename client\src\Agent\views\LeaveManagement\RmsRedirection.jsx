import React, { useState, useEffect } from 'react';
import { connect } from "react-redux";
import {
    GetCommonData, GetCommonspData,RmsRedirectionToUrl
} from "../../store/actions/CommonAction";
import { getUrlParameter } from '../../utility/utility.jsx';
import ComingSoon from './Comingsoon/ComingSoon';
import { useNavigate } from 'react-router-dom';

const RmsRedirection = (props) => {

    const [comingSoon, setComingSoon] = useState(false);
    let EmployeeId = getUrlParameter('EmployeeId');
    let Target = getUrlParameter('target');
    let Source = getUrlParameter('Source'); 
    const navigate = useNavigate();  

    useEffect(()=>{ 
        RmsRedirectionToUrl(EmployeeId, Source, function (results) {
            debugger;
            if (results && results.data && results.data.status == 200) {
                debugger;
                localStorage.setItem('PBRMSToken', results.data.Info)
                
                // console.log(document.location.origin +window.atob(Target));
                // window.open(document.location.origin + window.atob(Target), "_self");
                if(Target ==='L2FkbWluL1Jtcy9BdHRlbmRhbmNlRGFzaGJvYXJk'){
                    navigate('/admin/Rms/AttendanceDashboard');
                }
                else{
                    navigate('/agent/LeaveManagement/');
                }

            }
            else{
                setComingSoon(true);
            }
        })
    },[])

    return (
        <>
            {comingSoon ? <ComingSoon />
            : <h4>Redirecting....</h4> }
        </>
    )
};

// export default QuizComplete;

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData,
    };
}

export default connect(mapStateToProps,  {
    GetCommonspData,
    GetCommonData
})(RmsRedirection);