import React, { useState, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import {  Grid, IconButton, TextField, Button } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import {
    GetCommonData, GetCommonspData, GetCommonspDataV2, RmsRedirectionToUrl
} from "../../store/actions/CommonAction";
import { connect } from "react-redux";

const SearchAdvisorPopup = (props) => {

    const { open, onClose } = props
    const [typedValue, setTypedValue] = useState();
    
    const getAdvisor = () => {
        console.log(typedValue);
        let EmployeeId = typedValue;
        let source = 'Approval'
        let url = `/agent/LeaveManagement?EmployeeId=${typedValue}&source=${source}`

        window.open(document.location.origin + url, "_blank")
    }

    const TextChange = (e) => {
        setTypedValue(e.target.value);
    }

    return (
        <Dialog open={open} onClose={onClose} maxWidth="xs" className="SearchAdvisorPopup">
            <DialogTitle>
                {"Search Advisor"}
                <IconButton
                    aria-label="close"
                    onClick={onClose}
                    sx={{ position: 'absolute', right: 8, top: 8, color: (theme) => theme.palette.grey[500] }}
                >
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent>
                <Grid container spacing={2}>                    
                    
                    <Grid item md={12} sm={12} xs={12}>
                    <label className="RosterLabels">Employee code</label>
                    <TextField
                        autoFocus
                        onChange={TextChange}
                        required
                        margin="dense"
                        id="name"
                        name="email"
                        // label="Email Address"
                        type="email"
                        className="RosterMaster"
                        fullWidth
                        variant="outlined"
                    />
                    </Grid>

                    <Grid item md={12} sm={12} xs={12} className="alignCenter">
                        <Button className="SearchAdvisorBtn" onClick = {getAdvisor}>Search advisor</Button>
                    </Grid>
                </Grid>
            </DialogContent>

        </Dialog>
    );
}

// export default AddAdvisorPopup;

function mapStateToProps(state) {
    return {
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData,
        GetCommonspDataV2
    }
)(SearchAdvisorPopup);
