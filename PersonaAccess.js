const axios = require("axios");
const cache = require('memory-cache');
const CryptoJS = require("crypto-js");
const conf = require("./env_config");
const { GetParsedConfigFromCache, GetParsedConfigLocal } = require('./modules/common/CommonMethods');
const sqlHelper = require("./Libs/sqlHelper");
// const {LocalStorage} =  require('node-localstorage'); 

// constructor function to create a storage directory inside our project for all our localStorage setItem.
// var localStorage = new LocalStorage('./scratch'); 

// async function checkSettings() {

//   let cachedSetting = cache.get('Settings');
//   if (cachedSetting) {
//     return cachedSetting;
//   } else {
//     let data = await matrixdb.collection('Settings').findOne({ 'application': 'matrixdashboard' });
//     cache.put('Settings', data, (60 * 60 * 1000));
//     return data;
//   }
// }

function parseCookies(request) {
  var cookies = {};
  request.headers && request.headers.cookie && request.headers.cookie.split(';').forEach(function (cookie) {
    var parts = cookie.match(/(.*?)=(.*)$/);
    cookies[parts[1].trim()] = (parts[2] || '').trim();
  });
  return cookies;
}

// async function fetchAllowURLS() {
//   let cachedAllowURLs = cache.get('AllowURLs');
//   if (cachedAllowURLs) {
//     return cachedAllowURLs;
//   } else {
//     let data = await matrixdb.collection('AllowURL').find({}).toArray();
//     cache.put('AllowURLs', data, (60 * 60 * 1000));
//     return data;
//   }
// }

function createLog(agentId, Method, Channel, query, ResponseText, err, IP) {
  try {
    let logData = {};
    let responseTime = new Date().toISOString();

    logData.TrackingID = agentId
    logData.Exception = err.toString();
    logData.Method = Method;
    logData.Application = "MatrixDashboard";
    logData.Channel = Channel;
    logData.RequestText = JSON.stringify(query);
    logData.ResponseText = JSON.stringify(ResponseText)
    logData.Requesttime = responseTime;
    logData.Responsetime = responseTime;
    logData.CreatedOn = responseTime;
    logData.CreatedBy = "MatrixDashboard";
    logData.IP = IP;

    loggerdb.collection('Log_Collection').insertOne(logData);
  }
  catch (e) {
    // console.log(e);
  }
}

function Base64Decoding(value) {
  if (value) {
    const decoded = CryptoJS.enc.Base64.parse(value);
    const details = decoded.toString(CryptoJS.enc.Utf8);
    return details;
  } else {
    return "{}";
  }

}

function Base64Encode(value) {
  if (value) {
    const encoded = CryptoJS.enc.Utf8.parse(value);
    const details = CryptoJS.enc.Base64.stringify(encoded);
    return details;
  } else {
    return "{}";
  }
}

async function ValidateToken(user, EmployeeId) {
  try {
    let userId = user;
    let sqlparam = [];
    sqlparam.push({ key: "userId", value: userId });
    sqlparam.push({ key: "PersonaId", value: 5 });
    sqlparam.push({ key: "EmployeeId", value: EmployeeId });
    let query = `
    
	  Select userId from Persona.Personausermapping P 
	  Where P.isActive = 1 And P.userId = @userId AND PersonaId = @PersonaId
    
    Select * from Rms.UserMaster Where EmployeeId = @EmployeeId
    
    `;

    let result = await sqlHelper.sqlquery("R", query, sqlparam);
    console.log(result.recordsets[0])
    return result;

  } catch (err) {
    console.log('Inside ValidateToken', err);
    return false;
  }

}

function getMtxPayload(cookie) {
  try {
    let details = "{}", body = {};
    if (cookie) {
      details = Base64Decoding(cookie);
    }

    // console.log("getMtxPayload", cookie, details)

    const { UserId, AsteriskToken, EmployeeId } = JSON.parse(details);
    return {
      userId: UserId,
      employeeId: EmployeeId,
      token: AsteriskToken
    }
  } catch (err) {
    console.log(err);
    return {
      userId: "",
      employeeId: "",
      token: ""
    }
  }
}

function getBmsPayload(cookie, userId) {
  try {
    // console.log("getBmsPayload", cookie, userId)
    return {
      userId: userId,
      employeeId: "BMS_" + userId,
      token: cookie
    }

  } catch (err) {
    console.log(err);
    return {
      userId: "",
      employeeId: "",
      token: ""
    }
  }

}

function getDialerPayload({ payload, userId }) {
  // console.log("getDialerPayload", payload, userId)
  return {
    userId: userId,
    employeeId: userId,
    token: payload
  }
}

function getClaimPayload({ payload, userId }) {

  //console.log("getClaimPayload", payload, userId)

  return {
    userId: userId,
    employeeId: userId,
    token: payload
  }
}

const advisorUnderManager = async (req) => {
  try {
    let sqlparam = [];
    sqlparam.push({ key: "EmployeeId", value: req.headers.employeeid });
    sqlparam.push({ key: "ApproverId", value: req.user.userId });
    let query = `
    DECLARE @ManagerId BIGINT = 0;
    DECLARE @index SMALLINT = 0;
    DECLARE @RoleId INT;
    Declare @UserId BIGINT;
    Declare @AdvisorId BIGINT;
    Declare @ProcessId INT;
    DECLARE @Status Bit = 0;
  
    SELECT @RoleId = RoleId, @UserId = UserId, @AdvisorId = UserId from Rms.UserMaster Where EmployeeId = @EmployeeId   
  
    IF(@RoleId = 13)
    BEGIN
      Select @ManagerId = ManagerId from Rms.RosterUserHistory where UserId = @UserId
      
    END
    
  
  
    While (@ManagerId IS NOT NULL AND @ManagerId <> 75 AND @index < 15 AND @RoleId = 13)
    BEGIN
  
      IF EXISTS (SELECT * from Persona.PersonaUserMapping where UserId = @ManagerId AND IsActive = 1 AND @ManagerId = @ApproverId)
  
      BEGIN
        SET @Status = 1
        BREAK
      END
      SET @UserId = @ManagerId
      Select @ManagerId = ManagerId from Rms.UserMaster where UserId = @UserId
  
      SET @index = @index + 1
    END
  
    Select @Status As Status, @AdvisorId As userId
    `;

    let result = await sqlHelper.sqlquery("R", query, sqlparam);
    console.log(result.recordsets[0][0].Status)
    return result.recordsets[0][0]; 

  } catch (err) {
    console.log('Inside User', err);
    return false;
  }
} 

async function ValidateUser(req, res, AgentId) {
  let err;
  let IP = (req.headers['x-forwarded-for'] || '').split(',').pop().trim() || req.socket.remoteAddress;

  try {
    let userId = '', token = '', EmployeeId = '';

    // userId = getMtxPayload(mtxCookie).userId;

    userId = req.user.userId;

    if(req.query.EmployeeId && req.query.EmployeeId!=''){
      res.cookie(`PersonaEmployeeId`, req.query.EmployeeId, {domain: '.policybazaar.com',secure: true, httpOnly: true, maxAge: 60 * 60 * 1000});
      // localStorage.setItem('PersonaEmployeeId', req.query.EmployeeId)
    }

    // res.cookie(`PersonaEmployeeId`, req.query.EmployeeId, {domain: '.policybazaar.com',secure: true, httpOnly: true, maxAge: 60 * 1000});

    EmployeeId = req.query.EmployeeId || parseCookies(req)['PersonaEmployeeId'];
    // EmployeeId = req.query.EmployeeId || localStorage.getItem('PersonaEmployeeId');
    
    if(req.headers.source == 'Approval'){
      // req['user']= { userId: 91201 };
      let result = await advisorUnderManager(req)
      if(result.Status){
        req['user']= { userId: result.userId };
      }
      return result.Status;
    }

    // console.log('Cookies', parseCookies(req)['PersonaEmployeeId']);
    if(!EmployeeId){
      return true;
    }

    isValid = await ValidateToken(userId, EmployeeId);
    //   createLog(userId, "VERIFY_TOKEN", url, body, IsVpalid, "", IP);
    if (isValid.recordsets[0][0] && isValid.recordsets[0][0].userId) {
      req['PersonaUserId'] = { userId: isValid.recordsets[0][0].userId };
      req['user']= { userId: isValid.recordsets[1][0].UserId };
      return true;
    }
    else{return false;}
    // if (isValid.recordsets[0][0].userId && req.query.EmployeeId) {
    //   req['PersonaUserId'] = { userId: isValid.recordsets[0][0].userId  };
    //   req['user'] = { userId: isValid.recordsets[1][0].UserId };
    // }
    // else {
    //   req['PersonaUserId'] = { userId: isValid.recordsets[1][0].UserId};
    //   req['user'] = { userId: isValid.recordsets[1][0].UserId };;
    // }

    // return isValid;
  } catch (e) {
    console.log(e);
    err = e;
    return false;
  }

}

async function PersonaAccess(req, res, next) {
  try {
    if (process.env.ENVIRONMENT_MTX_DASH === 'DEV_MTX_DASH') {
      const SecretConfig = await GetParsedConfigLocal();
      global.SecretConfig = SecretConfig;
      // console.log('Inside Auth ENV: ', process.env.ENVIRONMENT_MTX_DASH)
    } else {
      const SecretConfig = await GetParsedConfigFromCache({ key: 'ENV_CONFIG' });
      global.SecretConfig = SecretConfig;
    }

    let isAllowURL = false;

    let isValid = false;
    let AgentId = null;
    AgentId = parseCookies(req)['AgentId'];
    let IP = (req.headers['x-forwarded-for'] || '').split(',').pop().trim() || req.socket.remoteAddress;

    let reqdata = {
      headers: req.headers,
      method: req.method,
      url: req.url,
      httpVersion: req.httpVersion,
      body: req.body,
      cookies: req.cookies,
      path: req.path,
      protocol: req.protocol,
      query: req.query,
      hostname: req.hostname,
      ip: req.ip,
      originalUrl: req.originalUrl,
      params: req.params,
    }

    // let AllowURL = await fetchAllowURLS();
    // AllowURL.forEach(async function (val, key) {

    //   if (req.url.includes(val.url)) {
    //     isAllowURL = true;
    //   }
    // });
    // createLog(parseCookies(req)['AgentId'], "ValidateUser", "Authentication", reqdata, isAllowURL, "", IP)

    // if (isAllowURL) {
    //   isValid = true;
    // }
    // else {
      // if(req.query.EmployeeId){
        isValid = await ValidateUser(req, res, AgentId);
      // }
     
    // }

    // console.log("env", process.env.ENVIRONMENT);
    if (isValid) {
      next();
    }
    else {
      res.status(401).send("No Access")

    }

  } catch (err) {
    console.log('Inside Auth', err);
    res.status(500).send("Auth Error");
  }
}

module.exports = {
  PersonaAccess: PersonaAccess,
  Base64Decoding: Base64Decoding,
  Base64Encode: Base64Encode,
  ValidateUser: ValidateUser,
  ValidateToken: ValidateToken,
  // fetchAllowURLS: fetchAllowURLS,
  createLog: createLog,
  parseCookies: parseCookies,
  // checkSettings: checkSettings
}