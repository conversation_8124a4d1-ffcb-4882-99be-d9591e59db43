import React, { useState, useEffect, useRef, useMemo } from 'react';
import Select from 'react-select';
import { connect } from "react-redux";
import { GetCommonData, GetCommonspData, GetCommonspDataV2, DownloadRotaData } from '../store/actions/CommonAction';
import moment from 'moment';
import "../app/css/AttendanceDashboard.scss"
import { Card, Grid, styled } from '@mui/material';
import Loader from './Components/Loader';
import _ from 'underscore';
import ShrinkageBreakdown from './Components/ShrinkageBreakdown';
import { getRMSUser, onexport } from '../../Agent/utility/utility';
import { ALL_SUPERVISORS, ALL_PROCESS, ALL_STATE, ALL_CATEGORY } from './Common/Constants';
import { GetAllManagerIds } from './Common/Utilities';
import DownloadIcon from '@mui/icons-material/Download';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Modal from '@mui/material/Modal';
import Tooltip, { tooltipClasses } from '@mui/material/Tooltip';
import Switch from '@mui/material/Switch';
import AttendanceAgentDetails from './AttendanceAgentDetails';

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  p: 4,
};


const HtmlTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: '#f5f5f9',
    color: 'rgba(0, 0, 0, 0.87)',
    maxWidth: 220,
    fontSize: theme.typography.pxToRem(12),
    border: '1px solid #dadde9',
  },
}));


const AttendanceDashboard = (props) => {
  const [rosterMaster, setRosterMaster] = useState([]);
  const [processMaster, setProcessMaster] = useState([]);
  const [product, setProduct] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [selectedRoster, setSelectedRoster] = useState(null);
  const [selectedProcess, setSelectedProcess] = useState(0);
  const [user, setUser] = useState(null);
  const [supervisorData, setSupervisorsData] = useState([]);
  const [selectedSupervisor, setSelectedSupervisor] = useState(null);
  const [agentLeavesData, setAgentLeavesData] = useState(null);
  const [attendanceAgentWiseData, setAttendanceAgentWiseData] = useState(null);
  const [rosterDates, setRosterDates] = useState([]);
  const [leavesDateWise, setLeavesDateWise] = useState({});
  const [attendanceDateWiseData, setAttendanceDateWiseData] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const refAgents = useRef([]);
  const [processData, setProcessData] = useState(null);
  const currentDate = moment(new Date()).format('YYYY-MM-DD');
  const [rosterId, setRosterId] = useState(null);
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const [openModal, setOpenmodal] = useState(false);
  const [toggleView, setToggleView] = useState(false);
  const [eligibleDates, setEligibleDates] = useState();
  const [scrollPositions, setScrollPositions] = useState({ top: 0, left: 0 });
  const elementRef = useRef(null);
  const [stateMaster, setStateMaster] = useState([]);
  const [selectedState, setselectedState] = useState();
  const [categoryList, setcategoryList] = useState([]);
  const [selectedCategory, setselectedCategory] = useState();

  const NonToggledValue = useMemo(() => {
    let toggleView = false;
    return AttendanceAgentDetails({
      refAgents: refAgents.current,
      rosterDates: rosterDates,
      agentLeavesData: agentLeavesData,
      attendanceAgentWiseData,
      eligibleDates, toggleView
    })

  }, [refAgents.current, agentLeavesData, rosterDates, attendanceAgentWiseData, eligibleDates, currentDate])

  const ToggledValue = useMemo(() => {
    let toggleView = true;
    return AttendanceAgentDetails({
      refAgents: refAgents.current,
      rosterDates: rosterDates,
      agentLeavesData: agentLeavesData,
      attendanceAgentWiseData,
      eligibleDates, toggleView
    })

  }, [refAgents.current, agentLeavesData, rosterDates, attendanceAgentWiseData, eligibleDates, currentDate])

  useEffect(() => {
    if (elementRef.current) {
      elementRef.current.scrollTop = scrollPositions.top;
      elementRef.current.scrollLeft = scrollPositions.left;
    }
  }, [scrollPositions]);

  const handleModalOpen = () => {
    setOpenmodal(true);
  }
  const handleModalClose = () => {
    setOpenmodal(false);
  }

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const HandleChangeRoster = (e) => {
    setSelectedRoster(e);
    let startDate = moment(e?.value?.StartDate?.substring(0, 10));
    let endDate = moment(e?.value?.EndDate?.substring(0, 10));
    let currentDate = startDate.clone();
    let dates = [];
    while (currentDate.isSameOrBefore(endDate)) {
      dates.push(currentDate.format('YYYY-MM-DD'))
      currentDate.add(1, 'day');
    }
    //  console.log(dates);
    setRosterDates(dates);
    setRosterId(e.value.Id);
  }

  const HandleChangeCategory = (data) => {
    setselectedCategory(data);
  }

  const HandleChangeProduct = (data) => {
    setSupervisorsData([]);
    setAttendanceDateWiseData({});
    setLeavesDateWise({});
    setSelectedProduct(data);
  }
  const GetAllProcess = (e) => {
    console.log(e)
  }

  const HandleChangeState = (e) => {
    // setSupervisorsData([]);
    // setAttendanceDateWiseData({});
    // setLeavesDateWise({});
    setselectedState(e);
  }

  const HandleChangeProcess = (data) => {
    setSelectedProcess(data);
    // if (data?.value === ALL_PROCESS) {
    // setSelectedProcess(data);
    // let managerIds = GetAllManagerIds(supervisorData);
    // setSelectedSupervisor({ ...data, value: managerIds });
    // if (data.Process && data.Process.length > 0) {
    //   let managerIds = GetAllProcess(data.Process);
    //   console.log({ ...data, value: managerIds });
    //   setSelectedProcess({ ...data, value: managerIds });
    // } else {
    //   let managerIds = GetAllProcess(supervisorData);
    //   setSelectedProcess({ ...data, value: managerIds });
    //   console.log({ ...data, value: managerIds });
    // }


  }

  const HandleChangeSupervisor = (data) => {
    if (data?.value === ALL_SUPERVISORS) {
      // let managerIds = GetAllManagerIds(supervisorData);
      // setSelectedSupervisor({ ...data, value: managerIds });
      if (data.supervisors && data.supervisors.length > 0) {
        let managerIds = GetAllManagerIds(data.supervisors);
        setSelectedSupervisor({ ...data, value: managerIds });
      } else {
        let managerIds = GetAllManagerIds(supervisorData);
        setSelectedSupervisor({ ...data, value: managerIds });
      }
    } else {
      setSelectedSupervisor(data);

    }
  }

  const GetRosterData = () => {
    props.GetCommonData({
      limit: 10,
      skip: 0,
      root: 'RosterMaster',
      cols: ["TOP(10) Id AS Id", "StartDate AS StartDate", "EndDate AS EndDate"],
      order: ["Id"],
      con: [{ "IsActive": 1 }],
      c: "R",
    }, function (data) {
      const FormattedRoster = Array.isArray(data) && data.map(roster => {
        let status = "";
        let start = moment(roster?.StartDate).format("YYYY-MM-DD");
        let end = moment(roster?.EndDate).format("YYYY-MM-DD");
        if (moment(start).isSameOrBefore(currentDate) &&
          moment(end).isSameOrAfter(currentDate)) {
          status = " -[ONGOING]"
        }

        let label = moment(roster?.StartDate).format('DD MMM YYYY') + ' - ' +
          moment(roster?.EndDate).format('DD MMM YYYY') + status;
        return {
          label,
          value: roster
        }
      }) || [];

      // Add 2 manual rosters of 14 days each after the last roster
      let manualRosters = [];
      if (FormattedRoster.length > 0) {
        const lastRoster = FormattedRoster[0].value;
        let lastEndDate = moment(lastRoster.EndDate, "YYYY-MM-DD");
        for (let i = 1; i <= 2; i++) {
          const rosterId = parseInt(lastRoster.Id) + i;
          const manualStart = lastEndDate.clone().add(1, 'day');
          const manualEnd = manualStart.clone().add(13, 'days');
          const manualRoster = {
            Id: rosterId,
            StartDate: manualStart.format('YYYY-MM-DD'),
            EndDate: manualEnd.format('YYYY-MM-DD'),
            // Add other fields if needed
          };
          manualRosters.push({
            label: `${manualStart.format('DD MMM YYYY')} - ${manualEnd.format('DD MMM YYYY')}`,
            value: manualRoster
          });
          lastEndDate = manualEnd;
        }
        // Prepend manual rosters to the top
        FormattedRoster.unshift(...manualRosters.reverse());
      }

      // setRosterMaster(FormattedRoster);

      // Find the first ongoing roster (label includes '[ONGOING]')
      const ongoingIndex = FormattedRoster.findIndex(r => r.label.includes('[ONGOING]'));
      const ongoingRoster = ongoingIndex !== -1 ? FormattedRoster[ongoingIndex] : FormattedRoster[0];


      setRosterMaster(FormattedRoster);
      setSelectedRoster(ongoingRoster || null);
      HandleChangeRoster(ongoingRoster || {});
      // setSelectedRoster(FormattedRoster[0] || null);
      // HandleChangeRoster(FormattedRoster[0] || {})
      setIsLoading(false);
    })
  }

  const GetProcessCategoryMaster = () => {
    props.GetCommonData({
      limit: 10,
      skip: 0,
      root: 'ProcessCategoryMaster',
      cols: ["Id", "Category"],
      order: ["Id"],
      direction: ["ASC"],
      con: [{ "IsActive": 1 }],
      c: "R",
    }, function (data) {
      let CategoryList = _.uniq(data, (item) => {
        return item.Id
      })

      let FormattedCategory = Array.isArray(CategoryList) && CategoryList.map(category => {
        if (category?.Id) {
          return { label: category?.Category, value: category?.Id }
        } else {
          return { label: "NA", value: 0 }
        }
      }) || [];

      FormattedCategory = [
        { label: "ALL CATEGORY", value: ALL_CATEGORY },
        ...FormattedCategory
      ];

      setcategoryList(FormattedCategory);
      setIsLoading(false);
    })
  }

  const GetProductData = () => {
    props.GetCommonData({
      limit: 10,
      skip: 0,
      root: 'ProcessMaster',
      cols: ["ProcessId", "ProcessName", "ProductId", "ProductName"],
      order: ["ProcessName"],
      direction: ['ASC'],
      con: [{ "IsActive": 1 }],
      c: "R",
    }, function (data) {
      debugger;

      // setProcessData(data);
      let ProductList = _.uniq(data, (item) => {
        return item.ProductId
      })

      const FormattedProduct = Array.isArray(ProductList) && ProductList.map(product => {
        if (product?.ProductId) {
          return { label: product?.ProductName, value: product?.ProductId }
        } else {
          return { label: "NA", value: 0 }
        }
      }) || [];

      setProduct(FormattedProduct);
      setIsLoading(false);
    })
  }

  // const GetProcessData = () => {
  //   if (selectedProduct) {
  //     props.GetCommonData({
  //       limit: 10,
  //       skip: 0,
  //       root: 'ProcessMaster',
  //       cols: ["ProcessId", "ProcessName", "ProductId", "ProductName"],
  //       order: ["ProcessName"],
  //       direction: ['ASC'],
  //       con: [{ "IsActive": 1, "ProductId": selectedProduct.value }],
  //       c: "R",
  //     }, function (data) {
  //       debugger
  //       const FormattedProcess = Array.isArray(data) && data.map(process => {
  //         if (process?.ProcessId) {
  //           return { label: process?.ProcessName + " (Product - " + process?.ProductName + ")", value: process?.ProcessId }
  //         } else {
  //           return { label: "NA", value: 0 }
  //         }
  //       }) || [];

  //       setProcessMaster(FormattedProcess);
  //       setIsLoading(false);
  //       // setSelectedProcess(FormattedProcess[0] || {});
  //     })
  //   }
  // }

  const GetProcessData = () => {
    // selectedState?.value > 0
    if (selectedState?.value === 'ALL_STATE') {
      selectedState.value = 0;
    }
    if (selectedCategory?.value === 'ALL_CATEGORY') {
      selectedCategory.value = 0;
    }
    if (selectedProduct?.value > 0 && selectedRoster?.value?.Id > 0) {
      setIsLoading(true);
      setSelectedSupervisor(null);
      setSupervisorsData([]);
      setAttendanceDateWiseData({});
      setLeavesDateWise({});
      props.GetCommonspData({
        root: 'GetProcessOnStateAndProduct',
        c: "R",
        // , ProcessId: param?.value || 0
        params: [{
          ProductId: selectedProduct?.value || 0, StateId: selectedState?.value, RosterId: selectedRoster?.value?.Id,
          CategoryId: selectedCategory?.value
        }],

      }, function (response) {
        if (response.status === 200) {
          if (response?.data?.data[0] && response?.data?.data[0].length > 0) {
            // let processArr = [];
            // for (let i = 0; i < response?.data?.data[0].length; i++) {
            //   if (selectedProduct && selectedProduct?.value && processData[i].ProductId == selectedProduct.value) {
            //     processArr.push(processData[i]);
            //   }
            // }
            let processArr = response?.data?.data[0];
            let FormattedProcess = Array.isArray(processArr) && processArr.map(process => {
              if (process?.ProcessId) {
                return { label: process?.ProcessName + " (Product - " + process?.ProductName + ")", value: process?.ProcessId }
              } else {
                return { label: "NA", value: 0 }
              }
            }) || [];

            FormattedProcess = [
              { label: "ALL PROCESS", value: ALL_PROCESS },
              ...FormattedProcess
            ];
            setProcessMaster(FormattedProcess);
            HandleChangeProcess({ label: "ALL PROCESS", value: ALL_PROCESS, label: "ALL PROCESS", Process: FormattedProcess });

            setIsLoading(false);
          }
          else {
            setSelectedSupervisor(null);
            setSupervisorsData([]);
            setProcessMaster([]);
          }
        }
        setIsLoading(false);
      })
    }
  }

  const GetStateData = () => {
    props.GetCommonspData({
      root: 'GetStates',
      c: "R",
      params: [{ RosterId: selectedRoster?.value?.Id }],
    }, function (data) {
      debugger;

      // setStateData(data);
      let StateList = _.uniq(data.data.data[0], (item) => {
        return item.StateId
      })

      let FormattedState = Array.isArray(StateList) && StateList.map(state => {
        if (state?.StateId) {
          return { label: state?.State, value: state?.StateId }
        } else {
          return { label: "NA", value: 0 }
        }
      }) || [];

      FormattedState = [
        { label: "ALL STATE", value: ALL_STATE },
        ...FormattedState
      ];
      setStateMaster(FormattedState);
      setIsLoading(false);
    })

  }

  const GetSupervisorData = (ProductId, ProcessId) => {

    // if(selectedState?.value  === 'ALL_STATE'){
    //   selectedState.value = 0;
    // }
    if (ProcessId?.value === 'ALL_PROCESS') {
      ProcessId.value = 0;
    }
    if (selectedCategory?.value === 'ALL_STATE') {
      selectedCategory.value = 0;
    }
    if (ProductId?.value > 0) {
      setIsLoading(true);
      props.GetCommonspData({
        // root: 'GetSupervisorsByProcessAndProduct',
        root: 'GetSupervisorsOnState',
        c: "R",
        // , ProcessId: param?.value || 0
        params: [{
          ProductId: ProductId?.value || 0, ProcessId: ProcessId?.value, RosterId: rosterId, StateId: selectedState?.value || 0,
          CategoryId: selectedCategory.value
        }],

      }, function (response) {
        if (response.status === 200 && response?.data?.data[0].length > 0) {
          const data = response?.data?.data?.[0] || [];
          let FormattedSupervisorData = Array.isArray(data) && data.map(item => {
            return { label: item?.ManagerName + '-' + item?.ManagerEmployeeId, value: item?.ManagerId || 0 }
          }) || [];

          FormattedSupervisorData = [
            { label: "ALL SUPERVISORS", value: ALL_SUPERVISORS },
            ...FormattedSupervisorData
          ];
          setSupervisorsData(FormattedSupervisorData);
          HandleChangeSupervisor({ label: "ALL SUPERVISORS", value: ALL_SUPERVISORS, label: "ALL SUPERVISORS", supervisors: FormattedSupervisorData })

        } else {
          setSupervisorsData([]);
          setIsLoading(false);
        }
        setIsLoading(false);
      })
    }
  }

  useEffect(() => {
    const data = getRMSUser();
    setUser(data);
    GetRosterData();
    GetProcessCategoryMaster();
    GetProductData();
  }, []);

  useEffect(() => {

    if (selectedRoster) {
      GetStateData();
    }
  }, [selectedRoster]);

  useEffect(() => {
    if (selectedProduct && selectedState) {
      refAgents.current = [];
      setSelectedProcess(null)
      GetProcessData();
    }
  }, [selectedProduct, selectedState, rosterId, selectedCategory])


  useEffect(() => {
    if (selectedProduct && selectedProcess) {
      refAgents.current = [];
      setSupervisorsData([])
      setSelectedSupervisor(null)
      setLeavesDateWise({});
      setAgentLeavesData({});

      setAttendanceAgentWiseData({});
      setAttendanceDateWiseData({});
      GetSupervisorData(selectedProduct, selectedProcess)
    }
  }, [selectedProcess, selectedState]);

  useEffect(() => {
    if (selectedSupervisor && user?.RoleId != 12) {
      GetAgentLeavesByRoster();
    }
  }, [selectedSupervisor])

  useEffect(() => {
    if (user?.RoleId === 12) {
      GetAgentLeavesByRoster();
    }
  }, [rosterId])

  const GetAgentLeavesByRoster = () => {
    if (selectedCategory?.value === 'ALL_CATEGORY') {
      selectedCategory.value = 0;
    }
    // && selectedState?.value
    if (selectedSupervisor?.value && selectedRoster?.value?.Id && selectedProduct?.value
      || user?.RoleId === 12) {

      setIsLoading(true);
      setLeavesDateWise({});
      setAgentLeavesData({});
      setAttendanceAgentWiseData({});
      setAttendanceDateWiseData({});
      props.GetCommonspDataV2({
        root: 'GetAgentLeavesByRoster',
        c: "R",
        params: [{
          RoleId: user?.RoleId || 0,
          ManagerIds: selectedSupervisor?.value || '',
          RosterId: parseInt(selectedRoster?.value?.Id),
          ProcessId: parseInt(selectedProcess?.value || 0),
          StateId: parseInt(selectedState?.value || 0),
          ProductId: parseInt(selectedProduct?.value || 0),
          CategoryId: parseInt(selectedCategory?.value || 0),
          RosterStartingDate : moment(selectedRoster?.value?.StartDate).format('YYYY-MM-DD'),
          RosterEndingDate : moment(selectedRoster?.value?.EndDate).format('YYYY-MM-DD')

        }],

      }, function (errorStatus, response) {
        if (!errorStatus) {
          const appliedLeavesData = response?.data && response.data[0] || [];
          const attendanceData = response?.data && response.data[1] || [];
          const agentsData = response?.data && response.data[2] || [];
          const EligibleDatesAgent = response?.data && response.data[3] || [];

          agentsData?.sort((a, b) => {
            const nameA = a.EmployeeName?.toUpperCase(); // ignore upper and lowercase
            const nameB = b.EmployeeName?.toUpperCase(); // ignore upper and lowercase
            if (nameA < nameB) {
              return -1;
            }
            if (nameA > nameB) {
              return 1;
            }

            // names must be equal
            return 0;
          });

          refAgents.current = agentsData;
          let leavesAgentWise = _.groupBy(appliedLeavesData, 'EmployeeId') || {}
          let leavesApplicationDateWise = _.groupBy(appliedLeavesData, (item) => {
            let date = moment(item?.ApplicationDate || '').format('YYYY-MM-DD')
            return date;
          });

          let attendanceAgentWise = _.groupBy(attendanceData, 'EmployeeId') || {}
          let attendanceDateWise = _.groupBy(attendanceData, (item) => {
            let date = moment(item?.AttendanceDate || '').format('YYYY-MM-DD')
            return date;
          });

          let EligibleDateWise = _.groupBy(EligibleDatesAgent, 'EmployeeId') || {}

          setLeavesDateWise(leavesApplicationDateWise);
          setAgentLeavesData(leavesAgentWise);
          setAttendanceAgentWiseData(attendanceAgentWise);
          setAttendanceDateWiseData(attendanceDateWise);
          setEligibleDates(EligibleDateWise);
        }
        setIsLoading(false);
        setToggleView(false);
      })
    }
  }



  const ContentBox = styled('div')(({ theme }) => ({
    margin: '20px',
    [theme.breakpoints.down('sm')]: { margin: '16px' },
  }));

  const exportToExcel = (tableID, filename = '') => {
    var downloadLink;
    var dataType = 'application/vnd.ms-excel';
    var tableSelect = document.getElementById(tableID);
    var tableHTML = tableSelect.outerHTML.replace(/ /g, '%20');

    // Specify file name
    filename = filename ? filename + '.xls' : 'excel_data.xls';

    // Create download link element
    downloadLink = document.createElement("a");

    document.body.appendChild(downloadLink);

    if (navigator.msSaveOrOpenBlob) {
      var blob = new Blob(['\ufeff', tableHTML], {
        type: dataType
      });
      navigator.msSaveOrOpenBlob(blob, filename);
    } else {
      // Create a link to the file
      downloadLink.href = 'data:' + dataType + ', ' + tableHTML;

      // Setting the file name
      downloadLink.download = filename;

      //triggering the function
      downloadLink.click();
      handleClose();
    }
  }

  const exportData = () => {
    // selectedState?.value > 0
    if (selectedProduct && selectedProduct?.value > 0 && selectedState) {
      // DownloadRotaData(rosterId, selectedProduct.value, selectedState?.value, function (results) {
      //   if (results && results.data && results.data.status == 200) {
      //     // setdownload(false)
      //     onexport(results.data.results, "Rota")
      //     handleClose();
      //   }
      // });
      props.GetCommonspData({
        root: 'GetRotaWeeklySheet_v2',
        c: "R",
        // , ProcessId: param?.value || 0
        params: [{ RosterId: rosterId || 0, ProductId: selectedProduct?.value, StateId: selectedState?.value }],

      }, function (response) {
        if (response.status === 200) {
          onexport(response.data.data[0], "Rota")
          handleClose();
        }
      })
    }
    else {
      // alert("Please Select Product");
      handleModalOpen();
      handleClose();
    }
  }

  const toggleSwitch = (e) => {
    setToggleView(e.target.checked);
    if (elementRef.current) {
      // Update the scroll position in the state
      setScrollPositions({
        top: elementRef.current.scrollTop,
        left: elementRef.current.scrollLeft,
      });
    }
  }


  return (
    <>
      <ContentBox className="analytics">
        <Modal
          open={openModal}
          onClose={handleModalClose}
          aria-labelledby="modal-modal-title"
          aria-describedby="modal-modal-description"
          className="AlartPopup"
        >
          <Box sx={style}>
            <Typography id="modal-modal-title" variant="h6" component="h2">
              Please Select Product and State
            </Typography>
          </Box>
        </Modal>
        <Grid container spacing={2}>
          <Grid item xs={12} md={9}>
            <p className="Caption">DASHBOARD</p>
            <h2 className="heading">Leave Management System</h2>
          </Grid>


          <Grid item xs={12} md={3}>
            <img src="/Lms/calendar.png" className="calendarIcon" />
            <span className="day">  TODAY</span>
            <h3 className="calendar">{moment().format('DD MMM, YYYY')}</h3>
          </Grid>

        </Grid>


        <Grid container spacing={1}>
          {/* <Grid item xs={12} md={user?.RoleId && ![12].includes(user.RoleId) ? 3 : 3}> */}
          <Grid item xs={12} md={3}>
            <label className="RosterLabels">Select Roster</label>
            <Select
              //menuIsOpen = {true}
              options={rosterMaster}
              onChange={(e) => HandleChangeRoster(e)}
              components={{
                IndicatorSeparator: () => null
              }}
              className="RosterMaster"
              value={selectedRoster}
            />
          </Grid>
          {
            user?.RoleId && ![12].includes(user.RoleId) && <>
              <Grid item xs={12} md={3}>
                <label className="RosterLabels">Select Category</label>

                <Select
                  options={categoryList}
                  onChange={(e) => HandleChangeCategory(e)}
                  components={{
                    IndicatorSeparator: () => null
                  }}
                  className="RosterMaster"
                  value={selectedCategory}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <label className="RosterLabels">Select Product</label>

                <Select
                  options={product}
                  onChange={(e) => HandleChangeProduct(e)}
                  components={{
                    IndicatorSeparator: () => null
                  }}
                  className="RosterMaster"
                  value={selectedProduct}
                />
              </Grid>

              {/* <Grid item xs={12} md={1} >
                <button onClick={() => exportToExcel("TableAttendanceDashboard")} className="downloadBtn">
                  <DownloadIcon></DownloadIcon></button>
              </Grid>
              <Grid item xs={12} md={1} >
                <button onClick={exportData} className="downloadBtn">
                  <DownloadIcon></DownloadIcon></button>
              </Grid> */}

              <Grid item xs={12} md={1} className="textCenter">
                {/* <button className="downloadBtn"
                  aria-controls={open ? 'basic-menu' : undefined}
                  aria-haspopup="true"
                  aria-expanded={open ? 'true' : undefined}
                  onClick={handleClick}>
                  <DownloadIcon></DownloadIcon></button> */}
                {/* <Button
                    id="basic-button"
                    aria-controls={open ? 'basic-menu' : undefined}
                    aria-haspopup="true"
                    aria-expanded={open ? 'true' : undefined}
                   // onClick={handleClick}
                  >
                    Dashboard
                  </Button> */}
                {/* <Menu
                  id="basic-menu"
                  anchorEl={anchorEl}
                  className="downloadDropdown"
                  open={open}
                  onClose={handleClose}
                  MenuListProps={{
                    'aria-labelledby': 'basic-button',
                  }}
                >
                  <MenuItem onClick={() => exportToExcel("TableAttendanceDashboard")}>RosterData</MenuItem>
                  <MenuItem onClick={exportData}>ROTA</MenuItem>
                </Menu> */}
                <HtmlTooltip className="downloadDropdown"
                  title={
                    <React.Fragment  >
                      <p onClick={() => exportToExcel("TableAttendanceDashboard")}>RosterData</p>
                      <p onClick={exportData}>ROTA</p>
                    </React.Fragment>
                  }
                >
                  <button className="downloadBtn"
                    aria-controls={open ? 'basic-menu' : undefined}
                    aria-haspopup="true"
                    aria-expanded={open ? 'true' : undefined}
                  //onClick={handleClick}

                  >
                    <DownloadIcon></DownloadIcon></button>
                </HtmlTooltip>
              </Grid>

            </>
          }

          <Grid item xs={12} md={2} className="textCenter">
            <label className="RosterLabels">Weekoff Options</label>
            <br />
            <Switch
              className="switchtoggle"
              checked={toggleView}
              onChange={toggleSwitch} />
          </Grid>

          {user?.RoleId && ![12].includes(user.RoleId) && <>


            <Grid item xs={12} md={3}>
              <label className="RosterLabels">Select State</label>

              <Select
                options={stateMaster}
                onChange={(e) => HandleChangeState(e)}

                components={{
                  IndicatorSeparator: () => null
                }}
                className="RosterMaster"
                value={selectedState}
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <label className="RosterLabels">Select Process</label>

              <Select
                options={processMaster}
                onChange={(e) => HandleChangeProcess(e)}
                components={{
                  IndicatorSeparator: () => null
                }}
                className="RosterMaster"
                value={selectedProcess}
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <label className="RosterLabels">Select Supervisor</label>

              <Select
                options={supervisorData}
                onChange={(e) => HandleChangeSupervisor(e)}
                components={{
                  IndicatorSeparator: () => null
                }}
                className="RosterMaster"
                value={selectedSupervisor}
              />
            </Grid>
          </>}
        </Grid>



        {
          isLoading ? <Loader /> :

            <Grid container spacing={2}>
              <Grid item xs={12} md={12}>
                <div ref={elementRef}
                  // onScroll={handleScroll}
                  className="AttendanceDashboard">
                  <table id="TableAttendanceDashboard">
                    <thead>
                      <tr>
                        <th colSpan="2">Employee Details</th>
                        <th colSpan="7">Week 1</th>
                        <th colSpan="7">Week 2</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td><b>Emp. Code</b></td>
                        <td><b>User Name</b></td>
                        <td className="HideColumn"><b>TL Name</b></td>
                        <td className="HideColumn"><b>TL Ecode</b></td>
                        {Array.isArray(rosterDates) && rosterDates.map((date, ind) =>
                          <td key={ind} className={date <= moment().format('YYYY-MM-DD') ? 'AttendanceDate' : ''}>{moment(date).format('DD MMM YY')} <p className="day">{moment(date).format('ddd')}</p></td>
                        )}
                      </tr>
                      {
                        <>
                          {toggleView ?
                            ToggledValue :
                            NonToggledValue
                          }
                        </>
                      }
                      {/* {
                              agentLeavesData && Object.keys(agentLeavesData)?.map((key, ind) => {
                                let data = agentLeavesData[key] || [];

                                return <tr key={ind}>
                                  <td>{data[0]?.EmployeeId || '-'}</td>
                                  <td>{data[0]?.EmployeeName || '-'}</td>
                                  {Array.isArray(rosterDates) && rosterDates.map(item => {
                                    let date = item;
                                    let isLeave = false, leaveType = '';
                                    for (let index = 0; index < data.length; index++) {
                                      const element = data[index];
                                      const applicationDate = moment(element?.ApplicationDate).format('DD/MM/YY');
                                      if (date === applicationDate) {
                                        isLeave = true;
                                        leaveType = element.LeaveTypeId;
                                      }
                                    }
                                    if (isLeave) {
                                      switch (leaveType) {
                                        case 1:
                                          return <td className="weeklyOf">W/O</td>
                                        case 2:
                                          return <td className="earnedLeave">E/L</td>
                                        case 3:
                                          return <td>E. W/O</td>
                                        case 4:
                                          return <td>S/L</td>
                                        case 5:
                                          return <td className="causalLeave">CL</td>
                                        default:
                                          return <td>P</td>
                                      }

                                    } else {
                                      return <td>P</td>
                                    }
                                  }

                                  )}
                                </tr>

                              })
                            } */}
                      <ShrinkageBreakdown
                        rosterDates={rosterDates}
                        leavesDateWise={leavesDateWise}
                        agentLeavesData={agentLeavesData}
                        agentsData={refAgents.current}
                        attendanceAgentWiseData={attendanceAgentWiseData}
                        attendanceDateWiseData={attendanceDateWiseData}
                      />

                    </tbody>
                  </table>
                </div>
              </Grid>
              {/* <Grid item xs={12} md={12}>
                <button className="FreezeBtn">Freeze</button>
                <button className="ResetBtn">Reset</button>
              </Grid> */}
            </Grid>
        }
      </ContentBox>
    </>
  )
}

export default connect(
  null,
  {
    GetCommonData,
    GetCommonspData,
    GetCommonspDataV2
  }
)(AttendanceDashboard);
