const sqlHelper = require("../../LibsPBIncentive/sqlHelper");
const conf = require('../../env_config');
const tblList = require("../constants");

async function VerifyRequestSourceContest({
  ApplicationId,
  SourceApp,
  ClientKey
}) {

  try {
    let sqlParams = [];
    sqlParams.push({ key: 'ApplicationId', value: parseInt(ApplicationId) })
    sqlParams.push({ key: 'ApplicationName', value: SourceApp })
    sqlParams.push({ key: 'ClientKey', value: ClientKey });
    const table = tblList['ContestApplicationMaster'] || null;

    if (!table) {
      return res.status(500).json({
          status: 500,
          error: "endpoint not exist."
      });
  }
    const query = `SELECT * from ${table} where ApplicationId = @ApplicationId
                   and ApplicationName = @ApplicationName
                   and ClientKey = @ClientKey and IsActive = 1`;

    let result = await sqlHelper.sqlquery("R", query, sqlParams);
    return result && result.recordset && result.recordset || [];

  } catch (err) {
    console.log('VerifyRequestSourceContest', err);
    return [];
  }
}

const CheckContestAgent = async (req, res) => {
  try {
    const { EmployeeId, UserId } = req.body || {};
    const { clientkey, sourceapp, applicationid } = req.headers;
    
    const VerifySource = await VerifyRequestSourceContest({
      ApplicationId: parseInt(applicationid),
      SourceApp: sourceapp,
      ClientKey: clientkey
    });

    if (Array.isArray(VerifySource) && VerifySource.length > 0) {
      const proc = tblList['CheckContestAgent'] || null;

      if(!proc) {
        return res.status(500).json({
          status: 500,
          error: "endpoint not exist."
        });
      }
      let sqlParams = [], contests = [];
      sqlParams.push({ key: 'ApplicationId', value: parseInt(applicationid) });
      sqlParams.push({ key: 'EmployeeId', value: EmployeeId });
      sqlParams.push({ key: 'UserId', value: UserId });
  
      let response = await sqlHelper.sqlProcedure("R", proc, sqlParams);
      let data = response?.recordset || [];
  
      for (let i = 0; i < data.length; i++) {
        let element = data[i];
        if(element && element.ContestId) {
          element = { ...element, ContestUrl: `${conf.MATRIX_DASHBOARD_BASEURL}/gaming/Spinwheel?cid=${element.ContestId}` }
        }
        contests.push(element);
      }
     
      return res.status(200).json({
        status: 200,
        message: 'success',
        data: contests
      })
    } else {
      return res.status(500).json({
        status: 500,
        message: 'Unidentified Source'
      })
    }

  } catch (err) {
    console.log('Inside CheckContestAgent:: ', err);
    return res.status(500).json({
      status: 500,
      message: err.toString()
    })
    
  }
}

module.exports = {
  CheckContestAgent: CheckContestAgent
}