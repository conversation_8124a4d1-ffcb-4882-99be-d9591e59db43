const tblList = require("../constants");
const methods = require("./ApiCommMethods");
let fs = require('fs');
const path = require("path");
const moment = require("moment");
const sqlHelper = require("../../Libs/sqlHelper");




async function Ticketing(req, res) {
    try {
        methods.Ticketing(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}


module.exports = {
    Ticketing: Ticketing,
};