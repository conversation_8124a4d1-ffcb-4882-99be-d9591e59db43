const tblList = require("../constants");
const methods = require("./SQLdbMethods");
const IncFileUpload = require("./IncFileUpload");
let fs = require('fs');
const path = require("path");
const moment = require("moment");
const sqlHelper = require("../../LibsPbCroma/sqlHelper");
const Utility = require("../../LibsPbCroma/Utility");
const UploadMatrixFiles = require("./UploadMatrixFiles");
const UploadStoryFiles = require("./UploadStoryFile");


async function insert(req, res) {
    try {


        let endpoint = req.body.data.root;
        let inputdata = req.body.data.body;
        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        var query = 'INSERT INTO ' + tblList[endpoint] + '(';
        var columns = [];
        var VALUES = [];
        let sqlparams = [];
        // for (var key in inputdata) {
        //     columns.push(key);
        //     VALUES.push(inputdata[key]);
        // }
        for (var key in inputdata) {
            columns.push(`${key}`);
            VALUES.push(`@${key}`);
            sqlparams.push({ key: key, value: inputdata[key] });
        }
        query = query + columns.join() + ") VALUES ( ";
        query = query + VALUES.join() + ")";

        if (req.body.data.scope)
            query = query + "; SELECT SCOPE_IDENTITY()";
        console.log(inputdata);
        console.log(query);
        console.log(sqlparams);
        let result = await sqlHelper.sqlquery("L", query, sqlparams);
        console.log(result);
        let e = JSON.stringify(result);
        var myArr = JSON.parse(e);

        if (typeof (myArr[0]) !== 'undefined') {
            if (typeof (myArr[0].error) !== 'undefined') {
                return res.send({
                    status: 500,
                    error: myArr[0].error
                });
            }
        }

        return res.send({
            status: 200,
            data: result,
            message: "Success"
        });
    } catch (err) {
        console.log(err);
        return res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}

async function update(req, res) {
    try {

        let endpoint = req.body.data.root;
        let inputdata = req.body.data.body;
        let querydata = req.body.data.querydata;
        if (!tblList[endpoint]) {
            res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        var query = 'UPDATE ' + tblList[endpoint] + ' SET ';
        var updatedata = [];
        var updatequery = [];
        let sqlparams = [];

        for (var key in inputdata) {
            updatedata.push(`${key} = @${key}`);
            sqlparams.push({ key: key, value: inputdata[key] });
        }
        query = query + updatedata.join();
        query = query + ` WHERE `;

        for (var key in querydata) {
            updatequery.push(`${key} = @${key}`);
            sqlparams.push({ key: key, value: querydata[key] });
        }
        query = query + updatequery.join(' and ');
        console.log(query);
        let result = await sqlHelper.sqlquery("L", query, sqlparams);

        res.send({
            status: 200,
            data: result,
            message: "Success"
        });

    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        await sql.close();
    }
}

async function getdata(req, res) {
    try {
        var endpoint = req.query.root;
        var c = req.query.c ? req.query.c : "R";
        if (methods.FindRoot(req, res)) {
            return;
        }

        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        // let skip = req.query.skip ? parseInt(req.query.skip) : 0,
        //     limit = req.query.limit ? parseInt(req.query.limit) : 10;

        let tablename = tblList[endpoint];
        if (tablename.toUpperCase().indexOf("NOLOCK") == -1) {
            tablename = tablename + " (NOLOCK) "
        }

        var query = 'SELECT  * FROM ' + tablename;// + ' (NOLOCK) ';


        if (req.query.cols && req.query.cols.length > 0) {
            query = 'SELECT  ' + req.query.cols.join() + ' FROM ' + tablename;// + ' (NOLOCK) ';
        }
        var whereCondition = [];
        let sqlparams = [];
        if (req.query.con) {
            query = query + ` WHERE `;
            for (var key in req.query.con) {
                let json = JSON.parse(req.query.con[key]);
                for (var obj in json) {
                    // whereCondition.push(`${obj} = '${json[obj]}'`);
                    var objparam = obj;
                    if (obj.indexOf('.') !== -1) {
                        var objparam = obj.replace('.', '')
                    }
                    //console.log(objparam);
                    if (obj.toLowerCase().indexOf('lastmessage') > -1 || obj.toLowerCase().indexOf('date') > -1 || obj.toLowerCase().indexOf('time') > -1) {
                        whereCondition.push(`CAST(${obj} AS DATE) = @${objparam}`);
                    }
                    else if (obj.toLowerCase().indexOf('likesearch') > -1 ) {
                        whereCondition.push(`${obj.substr(0, obj.indexOf('_'))} like @${objparam.substr(0, objparam.indexOf('_'))}`);
                    }
                    else {
                        whereCondition.push(`${obj} = @${objparam}`);
                    }
                    if (objparam.toLowerCase().indexOf('likesearch') > -1 ) {
                        sqlparams.push({ key: objparam.substr(0, objparam.indexOf('_')), value: '%'+json[obj]+'%' });
 
                    }else{
                    sqlparams.push({ key: objparam, value: json[obj] });
                    }
                }
            }
            query = query + whereCondition.join(' and ');
        }
        if (req.query.order) {
            if (!req.query.direction)
                query = query + " ORDER BY " + req.query.order + " DESC";
            else
                query = query + " ORDER BY " + req.query.order + " " + req.query.direction;
        } else{
            query = query + " ORDER BY 2 DESC";
        }
        // query =
        //     query +
        //     ` ORDER BY 1
        //     OFFSET ${skip} ROWS
        //     FETCH NEXT ${limit} ROWS ONLY`;

        console.log(query);
        //console.log(sqlparams);

        // let result = await sql.query(query);
        // await sql.close();
        let result = await sqlHelper.sqlquery(c, query, sqlparams);
        //console.log(result.recordsets);
        res.send({
            status: 200,
            data: result.recordsets && result.recordsets.length > 0 && result.recordsets[0],
            message: "Success"
        });
    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
    finally {
        //await sql.close();
    }
}


async function getdatasp(req, res) {
    try {
        console.log(req.query);
        var endpoint = req.query.root;
        var c = req.query.c ? req.query.c : "R";
        let params = req.query.params;
        if (!tblList[endpoint]) {
            return res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }
        var query = tblList[endpoint];
        let sqlparam = [];
        //const request = new sql.Request();
        let sqlparams = [];
        if (params) {
            for (var key in params) {
                const obj = JSON.parse(params[key])
                for (var k in obj) {
                    //request.input(k, obj[k]);
                    sqlparam.push({
                        key: k,
                        value: obj[k]
                    });
                    sqlparams.push("@" + k);
                }
            }
        }
        console.log(sqlparam);
        let result = await sqlHelper.sqlProcedure(c, query, sqlparam);

        res.send({
            status: 200,
            data: result.recordsets,
            message: "Success"
        });



    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
    finally {
        //await sql.close();
    }
}

async function deleteRow(req, res) {
    try {
        console.log('---------');
        let endpoint = req.body && req.body.data && req.body.data.root || "";
        // var patharray = req.url.split("/");
        // var endpoint = patharray[patharray.length - 1];
        if (!tblList[endpoint]) {
            res.send({
                status: 500,
                error: "endpoint not exist."
            });
        }

        var query = 'DELETE FROM ' + tblList[endpoint];
        var deleteQuery = [];
        var sqlparams = [];
        if (req.body && req.body.data && req.body.data.query) {
            query = query + ` WHERE `;
            for (var key in req.body.data.query) {
                deleteQuery.push(`${key} = @${key}`)
                sqlparams.push({ key: key, value: req.body.data.query[key] })
            }
            query = query + deleteQuery.join(' and ');
        }
        console.log(query, '-------');
        let result = null;
        if (query.indexOf("WHERE") > -1) {
            result = await sqlHelper.sqlquery("L", query, sqlparams);
        }
        //let result = await sql.query(query);
        res.send({
            status: 200,
            data: result,
            message: "Success"
        });
    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
    finally {
        await sql.close();
    }
}

async function GetAgentLoginData(req, res) {
    try {

        var query = ""


        res.send({
            status: 200,
            data: (await sql.query`SELECT  * FROM alertmaster WHERE Id = ${Id}`)
                .recordsets[0][0],
            message: "Success"
        });
    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: "Please enter correct inputs."
        });
    }
}


async function agentidletime(req, response) {
    try {

        var query = "[MTX].[GetAgentIdleTime]  " + req.query.u;

        let result = await sqlHelper.sqlquery("R", query);

        let idletime = 0;
        if (result && result.recordset) {
            result.recordset.forEach(element => {
                idletime = element.IdleTime;
            });
        }




        fs.readFile(path.join(appRoot, "client", "agentwidget.html")

            , null, function (error, data) {
                if (error) {
                    response.writeHead(404);
                    response.write('Whoops! File not found!');
                } else {
                    try {
                        data = data.toString();
                        data = data.replace("{data}", Math.round(idletime / 60));
                        data = data.replace("{u}", req.query.u);
                        response.write(data);

                    }
                    catch (e) {
                        response.write(e.toString());
                    }
                }
                response.end();
            });
    } catch (err) {
        console.log(err);
        response.write(err.toString());
        response.end();
    }
    finally {

    }
}
function secondsToHms(d) {
    d = Number(d);
    var h = Math.floor(d / 3600);
    var m = Math.floor(d % 3600 / 60);
    var s = Math.floor(d % 3600 % 60);

    var hDisplay = h > 0 ? h + (h == 1 ? ":" : ":") : "";
    var mDisplay = m > 0 ? m + (m == 1 ? ":" : ":") : "";
    var sDisplay = s > 0 ? s + (s == 1 ? "" : "") : "";
    return hDisplay + mDisplay + sDisplay;
}
async function agentstats(req, response) {
    try {

        var query = "[MTX].[AgentLoginTracker]";
        let sqlparams = [];
        sqlparams.push({ key: "UserId", value: req.query.u });
        //console.log(sqlparams);
        let result = await sqlHelper.sqlProcedure("R", query, sqlparams);
        let AgentStats = {};
        console.log(result.recordset);
        if (result && result.recordset) {
            result.recordset.forEach(element => {
                AgentStats = element;
            });
        }
        fs.readFile(path.join(appRoot, "client", "agentstats.html")

            , null, function (error, data) {
                if (error) {
                    response.writeHead(404);
                    response.write('Whoops! File not found!');
                } else {
                    try {
                        data = data.toString();
                        data = data.replace("{FLT}", moment(AgentStats.FIRSTLOGIN).utc().format("HH:mm:ss"));
                        data = data.replace("{talktime}", secondsToHms(AgentStats.TOTALTALKTIME));
                        data = data.replace("{totalbreaks}", AgentStats.SUM_DISPOSITIONMINS);
                        data = data.replace("{UniqueDailed}", AgentStats.UNIQUEDIAL);
                        data = data.replace("{MissedCallbacks}", AgentStats.MISSEDCALL);
                        data = data.replace("{APE}", AgentStats.APE);
                        data = data.replace("{BKGS}", AgentStats.BKGS);
                        data = data.replace("{IdleTime}", AgentStats.IDLETIME);
                        data = data.replace("{u}", req.query.u);
                        response.write(data);

                    }
                    catch (e) {
                        response.write(e.toString());
                    }
                }
                response.end();
            });
    } catch (err) {
        console.log(err);
        response.write(err.toString());
        response.end();
    }
    finally {

    }
}

async function login(req, res) {
    try {

        let inputdata = req.body;

        let query = "Select userid from CRM.UserDetails (NOLOCK) where EmployeeId = @EmployeeId and CAST(Password AS Varchar) = @Password";
        let sqlparams = [];
        sqlparams.push({ key: "EmployeeId", value: inputdata.EmpId });
        sqlparams.push({ key: "Password", value: inputdata.password });
        let result = await sqlHelper.sqlquery("R", query, sqlparams);

        if (result && result.recordset.length > 0) {
            res.cookie("userid", result.recordset[0].userid);
            res.cookie("AgentId", result.recordset[0].userid);
            res.redirect(req.headers.origin + "/admin/Users");
        } else {
            res.redirect(req.headers.origin + "?m=User Not found.");
        }
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}
async function awsfile(req, res) {
    try {

        let key = req.query.key;
        let bucket = (req.query.bucket) ? req.query.bucket : 'newcctecbuckt'
        let result = await Utility.fetchAWSfiles(key, bucket);

        res.send({
            status: 200,
            data: result
        });
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}

async function getawsfile(req, res) {
    try {

        let key = req.query.key;
        let bucket = req.query.bucket;
        let result = await Utility.getAWSfiles(key, bucket);

        res.send({
            status: 200,
            data: result
        });
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}

async function UploadIncentiveFile(req, res) {
    try {
        console.log("here");
        methods.UploadIncentiveFile(req, res);

    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function UploadVideoFile(req, res) {
    try {
        console.log("here");
        methods.UploadVideoFile(req, res);

    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function UploadStoryFile(req, res) {
    try {
        console.log("here");
        UploadStoryFiles.UploadStoryFile(req, res);
        
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}
async function UploadChatAgentFile(req, res) {
    try {
        console.log("here");
        await UploadMatrixFiles.UploadChatAgentFile(req, res);

    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function HealthCheck(req, res) {
    try {
        console.log('healthcheck');
        methods.HealthCheck(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        //await sql.close();
    }
}


async function rejectLeads(req, res) {
    try {
        methods.uploadLeadRejectionFile(req, res);

    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function uploadUserGrades(req, res) {
    try {
        methods.uploadUserGradesFile(req, res);
        
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function ProcessUploadIncentivefile(req, res) {
    try {
        IncFileUpload.ProcessUploadIncentivefile(req, res);
        
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

async function UploadAllocationFile(req, res) {
    try {
        UploadMatrixFiles.UploadChatAgentFile(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}


async function AgentSurvey(req, res) {
    try {
        UploadMatrixFiles.AgentSurvey(req, res);
    } catch (err) {
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}

module.exports = {
    insert: insert,
    update: update,
    getdata: getdata,
    delete: deleteRow,
    getdatasp: getdatasp,
    agentidletime: agentidletime,
    agentstats: agentstats,
    awsfile: awsfile,
    login: login,
    UploadIncentiveFile: UploadIncentiveFile,
    UploadVideoFile: UploadVideoFile,
    UploadStoryFile: UploadStoryFile,
    UploadChatAgentFile: UploadChatAgentFile,
    HealthCheck: HealthCheck,
    getawsfile: getawsfile,
    rejectLeads: rejectLeads,
    uploadUserGrades: uploadUserGrades,
    ProcessUploadIncentivefile: ProcessUploadIncentivefile,
    UploadAllocationFile: UploadAllocationFile,
    AgentSurvey: AgentSurvey,
};