import axios from "axios";
import config from "../../../Agent/config";
import constants from "../../../Agent/Constants/Constants.jsx";

axios.interceptors.response.use(response => {
  return response;
}, error => {
 return error;
});

const GetCommonData_fetched = Todos => {

  if (Todos.dataToSend.state && Todos.dataToSend.statename) {
    localStorage.setItem(Todos.dataToSend.statename, JSON.stringify(Todos.data));
  }

  return {
    type: constants.GET_COMMON_SUCCESS,
    payload: Todos.data,
    root: Todos.dataToSend.statename ?? Todos.dataToSend.root
  };
};

const GetCommonData_fetch_error = error => {
  return {
    type: constants.GET_COMMON_FAIL,
    payload: error
  };
};

export const GetCommonData = (dataToSend, cb) => {

  if (dataToSend.data != null || dataToSend.data != undefined) {
    return function (dispatch, getState) {
      dispatch(GetCommonData_fetched({ data: dataToSend.data, dataToSend: dataToSend }));
    };
  }

  // try {
  //   if ((dataToSend.state && localStorage.getItem(dataToSend.statename) || localStorage.getItem(dataToSend.statename))) {

  //     return function (dispatch, getState) {
  //       let data = JSON.parse(localStorage.getItem(dataToSend.statename));
  //       if (cb) {
  //         cb(data);
  //       }
  //       dispatch(GetCommonData_fetched({ data: data, dataToSend: dataToSend }));
        
  //     }
  //   }
  // }
  // catch (e) {

  // }


  return function (dispatch, getState) {
    axios
      .get(config.api.base_url + "/db/list/", {
        params: dataToSend
      })
      .then(data => {
        // if (dataToSend.state && dataToSend.statename) {
        //   localStorage.setItem(dataToSend.statename, JSON.stringify(data.data.data));
        // }
        if (cb) {
          cb(data && data.data && data.data.data);
        }
        dispatch(GetCommonData_fetched({data: data && data.data && data.data.data, dataToSend: dataToSend }));
      })
      .catch(error => {
        dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  };
};


export const GetCommonspData = (dataToSend, cb) => {

  if (dataToSend.data != null || dataToSend.data != undefined) {
    return function (dispatch, getState) {
      dispatch(GetCommonData_fetched({ data: dataToSend.data, dataToSend: dataToSend }));
    };
  }


  return function (dispatch, getState) {
    axios
      .get(config.api.base_url + "/db/listsp/", {
        params: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(GetCommonData_fetched({ data: data.data.data, dataToSend: dataToSend }));
      })
      .catch(error => {
        dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  };
};

export const GetCommonspDataV2 = (dataToSend, cb) => {
  try {
    let errorStatus = false;
    let endpoint = dataToSend.root || '';
    if (dataToSend.data != null || dataToSend.data != undefined) {
      return function (dispatch, getState) {
        dispatch(GetCommonData_fetched({ data: dataToSend?.data, dataToSend: dataToSend }));
      };
    }
  
    return function (dispatch, getState) {
      axios
        .get(config.api.base_url + "/db/listspV2/" + endpoint, {
          params: dataToSend
        })
        .then(data => {
          if (cb) {
            if(data?.status === 200) {
              cb(errorStatus, data?.data);
            } else {
              errorStatus = true;
              cb(errorStatus, null);
            }
          }
          dispatch(GetCommonData_fetched({ data: data?.data?.data, dataToSend: dataToSend }));
        })
        .catch(error => {
          errorStatus = true;
          dispatch(GetCommonData_fetch_error(error, dataToSend));
          cb(errorStatus, null);
        });
    };
  } catch (err) {
    return function (dispatch, getState) {
      dispatch(GetCommonData_fetch_error(err, dataToSend));
    };
  }
};

const InsertData_fetched = Todos => {

  return {
    type: constants.INSERT_COMMON_SUCCESS,
    payload: Todos.data
  };
};

const InsertData_fetch_error = error => {
  return {
    type: constants.INSERT_COMMON_FAIL,
    payload: error
  };
};

export const InsertData = (dataToSend, cb) => {
  return function (dispatch, getState) {
    axios
      .post(config.api.base_url + "/db/insert/", {
        data: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(InsertData_fetched({ data: data.data }));
      })
      .catch(error => {
        dispatch(InsertData_fetch_error(error));
      });
  };
};


const UpdateData_fetched = Todos => {

  return {
    type: constants.UPDATE_COMMON_SUCCESS,
    payload: Todos.data
  };
};

const UpdateData_fetch_error = error => {
  return {
    type: constants.UPDATE_COMMON_FAIL,
    payload: error
  };
};

export const UpdateData = (dataToSend, cb) => {
  return function (dispatch, getState) {
    axios
      .post(config.api.base_url + "/db/update/", {
        data: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(UpdateData_fetched({ data: data.data }));
      })
      .catch(error => {
        dispatch(UpdateData_fetch_error(error));
      });
  };
};

const DeleteData_fetched = Todos => {

  return {
    type: constants.DELETE_COMMON_SUCCESS,
    payload: Todos.data
  };
};

const DeleteData_fetch_error = error => {
  return {
    type: constants.DELETE_COMMON_FAIL,
    payload: error
  };
};

export const DeleteData = (dataToSend, cb) => {
  return function (dispatch, getState) {
    axios
      .post(config.api.base_url + "/db/delete", {
        data: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(DeleteData_fetched({ data: data.data }));
      })
      .catch(error => {
        dispatch(DeleteData_fetch_error(error));
      });
  };
};


export const GetDataDirect = (dataToSend, cb) => {

  try {
    if ((dataToSend.state && localStorage.getItem(dataToSend.statename)) || localStorage.getItem(dataToSend.statename)) {

      let data = JSON.parse(localStorage.getItem(dataToSend.statename));
      cb(data);
      return;
    }
  }
  catch (e) {

  }
  //return function (dispatch, getState) {
  axios
    .get(config.api.base_url + "/db/list/", {
      params: dataToSend
    })
    .then(data => {
      if (dataToSend.state && dataToSend.statename) {
        localStorage.setItem(dataToSend.statename, JSON.stringify(data.data.data));
      }
      cb(data.data.data);

      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetRealTimeAgentData = (manager, context, cb) => {

  let url = config.api.realtimeurl + "RealTimeAgentStatus?managercode=" + (context == "" ? manager : "") + "&context=" + context;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      cb(error);
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetRealTimeSingleAgentData = (assignedagent, cb) => {

  let url = config.api.realtimeurl + "getagentrealtime/" + assignedagent + "/";

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetQualityReport = (encodedId, cb) => {

  let url = config.api.aiUrl + "getQualityReport?id=" + encodedId;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};
export const PostIncentiveFormData = (formdata, cb) => {
  debugger;
  let url = config.api.base_url + "/db/UploadIncentiveFile";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};

export const PostUploadStoryFormData = async (formdata, cb) => {
  debugger;
  let url = config.api.base_url + "/db/UploadStoryFile";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  return await axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      cb(data);
      console.log(data);
    })
    .catch(error => {
    });

};

export const PostUploadVideoFormData = async (formdata, cb) => {
  debugger;
  let url = config.api.base_url + "/db/UploadVideoFile";

  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  return await axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      cb(data);
      console.log(data);
    })
    .catch(error => {
    });

};

export const AddUpdateCourseContentData = async (formdata) => {
  debugger;
  try {
    let url = config.api.base_url + "/db/CourseContentData";
    const headers = { 'Content-Type': 'multipart/form-data' }
    let result = await axios.post(url, formdata, { headers: headers});
    return result;

  } catch (err) {
    return null;
  }
};

export const InsertCourseData= async (formdata) =>{
try{
  debugger
  let url= config.api.base_url + "/db/insertCourseData";
  // const headers = {'Content-Type': 'multipart/form-data'};
  let result = await axios.post(url, { data: formdata });
  return result;
}
catch(err){
  return null;
}
};

export const UpdateCourseData= async (formdata) =>{
  try{
    debugger
    let url= config.api.base_url + "/db/updateCourseData";
    // const headers = {'Content-Type': 'multipart/form-data'};
    let result = await axios.post(url, { data: formdata });
    return result;
  }
  catch(err){
    return null;
  }
  };


export const PostAgentChatFileData = (formdata, cb) => {
  let url = config.api.base_url + "/db/UploadChatAgentFile";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};

export const ProcessUploadIncentiveFile = (row, cb) => {
  let url = config.api.base_url + "/db/ProcessUploadIncentivefile?rid=" + row.Id + "&ProductId=" + row.ProductId
    + "&IncentiveMonth=" + row.IncentiveMonth;

  axios
    .get(url)
    .then(data => {

      cb(data);
    })
    .catch(error => {
    });

};
export const GetRealTimeQueueData = (context, cb) => {

  let url = config.api.realtimeurl + "RealTimeData?context=" + context;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetWaitingAssignedCallData = (queuename, cb) => {

  let url = config.api.waitingAssignedQueue + "?queue=" + queuename;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetRecordingName = (calldataid, cb) => {

  let url = config.api.recordingnameurl + calldataid;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetAwsRecordingUrl = (key, bucket, cb) => {

  let myBucket = bucket;
  if (bucket === undefined) {
    myBucket = "newcctecbuckt";
  }
  let url = config.api.awsrecordingurl + "?key=" + key + "&bucket=" + myBucket;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetRealTimeTotalData = (context, cb) => {

  let url = config.api.realtimeurl + "RealTimeData?context=" + context;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const ResetTokenDidUpdate = (userid, cb) => {

  let url = config.api.realtimeurl + "resettoken/" + userid;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const SetAgentStatusBlocked = (managerid, agentCode, bookingid, grade, cb) => {

  //let url = config.api.base_url + "setAgentStatus.php?emp_id=" + agentCode + "&requested_by=" + managerid + "&leadid=" + bookingid + "&grade=" + grade + "&status=blocked";
  // .get(config.api.base_url + "/dialerapi/setAgentStatus", {
  //   params: dataToSend
  // })
  let dataToSend = {
    agentCode,
    managerid,
    bookingid,
    grade
  }

  axios
    .get(config.api.base_url + "/dialerapi/setAgentStatus", {
      params: dataToSend
    })
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const TransferCallThirdParty = (agent, thirdpartynumber, campaign, bookingid, transfer_type, dtmf_no, insurer,
  application_number, transfer_agents, grade, blockedAgentid, claimid, claim_callid, productid, salesagent, isenc, countrycode, dialercode, cb) => {

  let dataToSend = {
    agent,
    thirdpartynumber,
    campaign,
    bookingid,
    transfer_type,
    dtmf_no,
    insurer,
    application_number,
    transfer_agents,
    grade,
    blockedAgentid,
    productid,

    salesagent,
    isenc,
    countrycode,
    dialercode,
    action: "transfer",


  }


  if (transfer_type == 'claim_transfer') {
    dataToSend['claimid'] = claimid;
    dataToSend['claim_callid'] = claim_callid;
  }


  axios
    .get(config.api.base_url + "/dialerapi/multi_conference", {
      params: dataToSend
    })
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const MergeCallThirdParty = (agent, cb) => {

  //let url = config.api.dialerApiUrl + "multi_conference.php?agent=" + agent + "&action=merge";
  let dataToSend = {
    agent,
    action: "merge"
  }
  axios
    .get(config.api.base_url + "/dialerapi/multi_conference", {
      params: dataToSend
    })
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const UnHoldCallThirdParty = (agent, cb) => {

  // let url = config.api.dialerApiUrl + "multi_conference.php?agent=" + agent + "&action=unhold";

  let dataToSend = {
    agent,
    action: "unhold"
  }
  axios
    .get(config.api.base_url + "/dialerapi/multi_conference", {
      params: dataToSend
    })
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const AgentToAgentCall = (leadid, serviceagent, salesagent, sericeagentphn, salesagentphn,
  uid, serviceserverip, salesserverip, cb) => {

  // let url = config.api.dialerApiUrl + "mob2mobcall.php?leadid=" + leadid + "&campaign=agentToagent" + "&emp=" + serviceagent +
  //   "&agentphone=" + sericeagentphn
  //   + "&emp2=" + salesagent + "&agent2phone=" + salesagentphn
  //   + "&uid=" + uid + "&server_ip=" + serviceserverip + "&serverip2=" + salesserverip + "&islocal=1"
  //   ;

  let dataToSend = {
    leadid,
    campaign: "agentToagent",
    emp: serviceagent,
    agentphone: sericeagentphn,
    emp2: salesagent,
    agent2phone: salesagentphn,
    uid: uid,
    server_ip: serviceserverip,
    serverip2: salesserverip,
    islocal: 1
  }
  axios
    .get(config.api.base_url + "/dialerapi/mob2mobcall", {
      params: dataToSend
    })
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetMoodleCourse = (productid, cb) => {

  let url = config.api.moodleurl + "course.php?productid=" + productid;

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetMoodleQuiz = (productid, cb) => {

  let url = config.api.moodleurl + "courseModule.php?productid=" + productid + "&type=quiz";

  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};

export const GetExportQuiz = (dataToSend, cb) => {
  let url = config.api.moodleurl + "quiz.php";
  axios
    .post(url, dataToSend
      //{"quizId":"10,13","courseId":"7,10","userId":"pw07725,pw05523","productId":"2","format":"export"}
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const PostLoginLogoutHistory = (dataToSend, cb) => {
  let url = config.api.chatAgentUrl + "loginhistory/getloginhistory";
  axios
    .post(url, dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const PostLoginLogoutDetails = (dataToSend, cb) => {
  let url = config.api.chatAgentUrl + "loginhistory/getlogindetailsperdep";
  axios
    .post(url, dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};
export const CustAgentVerifyOTP = (dataToSend, cb) => {
  let url = config.api.progressiveUrl + "CustAgentVerifyOTP";
  axios
    .post(url, dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const GetAgentCallDetails = (dataToSend, cb) => {
  let url = config.api.progressiveUrl + "GetAgentCallDetails";
  axios
    .post(url, dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const GetAgentCallLogs = (dataToSend, cb) => {
  let url = config.api.progressiveUrl + "GetAgentCallDetailsV2";
  axios
    .post(url, dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const GetSummaryQuiz = (dataToSend, cb) => {
  let url = config.api.moodleurl + "quiz.php";
  axios
    .post(url,
      dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const UpdateAgentChatParams = (dataToSend, cb) => {
  debugger;
  let url = config.api.chatAgentUrl + "departmentagent/updateagentchatparams";
  axios
    .post(url,
      dataToSend
    )
    .then(data => {
      //cb(data);
    })
    .catch(error => {
    });
};

export const GetSummaryCourse = (dataToSend, cb) => {
  let url = config.api.moodleurl + "courseAttendance.php";
  axios
    .post(url,
      dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const GetSummaryLMS = (dataToSend, cb) => {
  let url = config.api.moodleurl + "lmsVisit.php";
  axios
    .post(url,
      dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const SendOTP = (dataToSend, cb) => {
  let url = config.api.SendOTP;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': 'cG9saWN5 YmF6YWFy'
  }
  axios
    .post(url,
      dataToSend
      , {
        headers: headers
      })
    .then(data => {

      cb(data);
    })
    .catch(error => {
    });
};

export const GetComunicationData = (dataToSend, cb) => {

  return function (dispatch, getState) {
    axios
      .get(config.api.ServiceAPI + dataToSend.root, {
        params: dataToSend.data
      })
      .then(data => {
        cb(data);
        //dispatch(GetCommonData_fetched({ data: data, dataToSend: dataToSend }));
      })
      .catch(error => {
        dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  };
};
export const PostCommunicationData = (dataToSend, cb) => {

  return function (dispatch, getState) {
    axios
      .post(config.api.ServiceAPI + dataToSend.root,
        dataToSend.data
      )
      .then(data => {
        cb(data);
        //dispatch(GetCommonData_fetched({ data: data, dataToSend: dataToSend }));
      })
      .catch(error => {
        dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  };
};


export const GetFileExists = (url, cb) => {

  axios
    .get(url)
    .then(data => {
      if (cb) {
        cb(data);
      }
    })
    .catch(error => {
      if (cb) {
        cb(error);
      }
    });
};

export const GetChatMessages = (rid, dep, cb) => {
  let url = config.api.chatAgentUrl + "chat/getChatMessagesUsingRoom/" + rid + "?dep=" + dep;
  axios
    .get(url
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const GetDashboardUrl = (dataToSend, cb) => {
  let url = "https://cdp.policybazaar.com/quicksight/getdashboardurl";
  const headers = {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache',
    'userid': dataToSend.userid,
  }
  delete dataToSend['userid'];

  axios
    .post(url,
      dataToSend
      , {
        headers: headers
      })
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const PostLeadRejectionData = (formdata, cb) => {
  debugger;
  let url = config.api.base_url + "/db/rejectLeads";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};

export const PostUserGrades = (formdata, cb) => {
  let url = config.api.base_url + "/db/uploadUserGrades";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });
};

export const SendReassignNotification = (params, cb) => {

  let url = config.api.realtimeurl + "setnotification";
  axios
    .post(
      url,
      params
    )
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};

export const ReAssignedPODLead = (params, cb) => {
  let url = config.api.progressiveUrl + "ReAssignedPODLead/" + params.leadId + "/" + params.userId + "/" + params.assignedUserId;
  axios
    .get(url)
    .then(data => {

      cb(data);
      //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
    })
    .catch(error => {
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};


export const resolveRedirectURL = (dataToSend, cb) => {
  axios
    .post(config.api.base_url + "/db/resolveRedirectURL",
      dataToSend
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const GetDashboardLink = (dataToSend, cb) => {
  let url = config.api.DashboardApiUrl + "linkGenerate/genrateLinkLead";
  const headers = {
    'x-access-token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'
  }
  axios
    .post(url, dataToSend, {
      headers: headers,
      withCredentials: true
    }
    )
    .then(data => {
      cb(data);
    })
    .catch(error => {
    });
};

export const LotteryData = (params, cb, method = 'get') => {
  let url = config.api.base_url + "/comm/" + params.root;
  if (method === 'post') {
    axios
      .post(url, params)
      .then(data => {

        cb(data);
        //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
      })
      .catch(error => {
        //dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  } else {
    axios
      .get(url, params)
      .then(data => {

        cb(data);
        //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
      })
      .catch(error => {
        //dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  }

};


export const AllocationFormData = (formdata, cb) => {
  debugger;
  let url = config.api.base_url + "/db/UploadAllocationFile";
  const headers = {
    'Content-Type': 'multipart/form-data',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      console.log(data);
    })
    .catch(error => {
    });

};

export const listsp = (dataToSend, cb, method = 'get') => {
  let url = config.api.base_url + "/db/listsp/";
  if (method === 'post') {
    axios
      .post(url, {
        params: dataToSend
      })
      .then(data => {

        cb(data);
        //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
      })
      .catch(error => {
        //dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  } else {
    axios
      .get(url, {
        params: dataToSend
      })
      .then(data => {

        cb(data);
        //dispatch(GetCommonData_fetched({ data: data.data.data[0], dataToSend: dataToSend }));
      })
      .catch(error => {
        //dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  }

};


export const AgentSurvey = (formdata, cb) => {
  debugger;
  let url = config.api.base_url + "/db/AgentSurvey";
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .post(url, formdata
      , {
        headers: headers
      })
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};


export const getqueuetwo = (type, proid, queues, cb) => {
  debugger;
  queues = queues.replaceAll("'", "");
  let url = `${config.api.dialerApiUrlInbound}?type=${type.toLowerCase()}&product=${proid}&queuename=${queues}`;
  //let url = "https://dialerqaapi.policybazaar.com/api/v2/dialer/getInboundQueueMappingData?type=service&product=4&queuename=teletata,testtermweb"
  console.log(url);
  axios
    .get(url)
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};

export const Ticketing = (userid, startdate, enddate, UserName, EmployeeID, cb) => {
  debugger;
  let url = `${config.api.base_url}/apicomm/Ticketing?userid=${userid}&startdate=${startdate}&enddate=${enddate}&UserName=${UserName}&EmployeeID=${EmployeeID}`;
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .get(url)
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};

export const RegisterCustomer = (dataToSend, cb) => {
  let url = config.api.base_url + "/registerCustomer";
  axios
    .post(
      url,
      dataToSend
    )
    .then(data => {
      let errorCodes = [400, 401, 402, 403, 404, 500, 501, 502, 503, 504];
      let errorData = {};
      if (data && data.response && data.response.status && errorCodes.includes(data.response.status)) {
        errorData = {
          error: data.response.data || "",
          status: data.response.status || 0 
        }
        cb(errorData);
      } else if (cb) {
        cb(data.data);
      }
      //console.log(data);
    })
    .catch(error => {
      if (cb) {
        cb(error);
      }
      //dispatch(GetCommonData_fetch_error(error, dataToSend));
    });
  //};

};


export const InsertSurveyMapping = (dataToSend, cb) => {

  if (dataToSend.data != null || dataToSend.data != undefined) {
    return function (dispatch, getState) {
      dispatch(GetCommonData_fetched({ data: dataToSend.data, dataToSend: dataToSend }));
    };
  }


  return function (dispatch, getState) {
    debugger;
    axios
      .get(config.api.base_url + "/db/InsertSurveyMapping/", {
        params: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(GetCommonData_fetched({ data: data.data.data, dataToSend: dataToSend }));
      })
      .catch(error => {
        dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  };
};



export const InsertQuizMapping = (dataToSend, cb) => {

  if (dataToSend.data != null || dataToSend.data != undefined) {
    return function (dispatch, getState) {
      dispatch(GetCommonData_fetched({ data: dataToSend.data, dataToSend: dataToSend }));
    };
  }


  return function (dispatch, getState) {
    debugger;
    axios
      .get(config.api.base_url + "/db/InsertQuizMapping/", {
        params: dataToSend
      })
      .then(data => {
        if (cb) {
          cb(data);
        }
        dispatch(GetCommonData_fetched({ data: data.data.data, dataToSend: dataToSend }));
      })
      .catch(error => {
        dispatch(GetCommonData_fetch_error(error, dataToSend));
      });
  };
};

export const GetData = async (url, headers={}) => {
  try {
    let result = await axios.get(
      url,
      {headers: headers}
    );

    return { 
      errorStatus: false,
      data: result
    };
  } catch (err) {
    return {
      errorStatus: true,
      data: err
    }
  }
}



export const DownloadSurveyData = (surveyid, cb) => {
  debugger;
  let url = `${config.api.base_url}/db/DownloadSurveyData?surveyid=${surveyid}`;
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .get(url)
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};

export const DownloadQuizData = (QuizId, cb) => {
  debugger;
  let url = `${config.api.base_url}/db/DownloadQuizData?QuizId=${QuizId}`;
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .get(url)
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};


export const QuizInsertResponsesData = (body, cb) => {
  debugger;
  // console.log("The body inserted is ", body);
  let url = config.api.base_url + "/db/QuizInsertResponsesData";
  const headers = {
    'Content-Type': 'application/json'
  }
  axios
    .post(url,body
      , {
        headers: headers
      })
    .then(data => {
    
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

}

export const PBSchoolQuizInsertResponsesData = ({Responses, UserId}, cb) => {
  debugger;
  console.log("The body inserted is ", {Responses, UserId});
  const body={Responses, UserId};
  let url = config.api.base_url + "/db/PBSchoolQuizInsertResponsesData";
  const headers = {
    'Content-Type': 'application/json'
  }
  axios
    .post(url,body
      , {
        headers: headers
      })
    .then(data => {
      console.log("QUiz insert response data ", data);
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

}


export const RmsRedirectionToUrl = (cb) => {
  let url = `${config.api.base_url}/Rms/RmsRedirectionToUrl`;
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .get(url)
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};


export const InsertRosterHistory = ({excelData, RosterId, ProductId, StateId, ProcessList, AllIndia}, cb) => {

  const body={excelData, RosterId, ProductId, StateId, ProcessList, AllIndia};
  let url = config.api.base_url + "/Rms/InsertRosterHistory";
  const headers = {
    'Content-Type': 'application/json'
  }
  axios
    .post(url,body
      , {
        headers: headers
      })
    .then(data => {
      
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

}

export const DownloadRotaData = (rosterId, selectedProduct, selectedState, cb) => {
  
  let url = `${config.api.base_url}/Rms/DownloadRotaData?RosterId=${rosterId}&ProductId=${selectedProduct}&StateId=${selectedState}`;
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .get(url)
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};

export const ApplyAutoLeave = (cb) => {
  
  let url = `${config.api.base_url}/Rms/ApplyAutoLeave`;
  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .get(url)
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};


export const InsertTransferRequest = (body, cb) => {
  // let url = `${config.api.base_url}/Rms/RmsRedirectionToUrl?userId=${userId}&ApplicationId=${ApplicationId}`;
  let url = `${config.api.base_url}/Rms/InsertTransferRequest`;

  const headers = {
    'Content-Type': 'application/json',
  }
  axios
  .post(url,body
    , {
      headers: headers
    })
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};

export const updateAdvisorRemoveRequest = (body, cb) => {
  let url = `${config.api.base_url}/Rms/updateAdvisorRemoveRequest`;

  const headers = {
    'Content-Type': 'application/json',
  }
  axios
  .post(url,body
    , {
      headers: headers
    })
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });
}

export const updateAdvisorTransferData = (body, cb) => {
  let url = `${config.api.base_url}/Rms/updateAdvisorTransferData`;

  const headers = {
    'Content-Type': 'application/json',
  }
  axios
  .post(url,body
    , {
      headers: headers
    })
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });
}

export const addAdvisorsProcess = (body, cb) => {
    let url = `${config.api.base_url}/Rms/addAdvisorsProcess`;

  const headers = {
    'Content-Type': 'application/json',
  }
  axios
  .post(url,body
    , {
      headers: headers
    })
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });
}


export const getTLRosterData = (cb) => {
  let url = `${config.api.base_url}/Rms/getTLRosterData`;

const headers = {
  'Content-Type': 'application/json',
}
axios
.get(url
  , {
    headers: headers
  })
  .then(data => {
    if (cb) {
      cb(data);
    }
    //console.log(data);
  })
  .catch(error => {
  });
}

export const FreezedTlData = (cb) => {
  let url = `${config.api.base_url}/Rms/FreezedTlData`;

const headers = {
  'Content-Type': 'application/json',
}
axios
.get(url
  , {
    headers: headers
  })
  .then(data => {
    if (cb) {
      cb(data);
    }
    //console.log(data);
  })
  .catch(error => {
  });
}


export const MenuAuthentication = (MenuId, cb) => {
  let url = `${config.api.base_url}/Rms/MenuAuthentication?MenuId=${MenuId}`;

const headers = {
  'Content-Type': 'application/json',
}
axios
.get(url
  , {
    headers: headers
  })
  .then(data => {
    if (cb) {
      cb(data);
    }
    //console.log(data);
  })
  .catch(error => {
  });
}


export const GetUserMenus = (cb) => {
  let url = `${config.api.base_url}/Rms/GetUserMenus`;

const headers = {
  'Content-Type': 'application/json',
}
axios
  .get(url
    , {
      headers: headers
    })
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });
}

export const GetRosterData = (cb) => {
  // let url = `${config.api.base_url}/Rms/RmsRedirectionToUrl?userId=${userId}&ApplicationId=${ApplicationId}`;
  let url = `${config.api.base_url}/Rms/GetRosterData`;

  const headers = {
    'Content-Type': 'application/json',
  }
  axios
    .get(url)
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};


export const updateLeaveApproval = (body, cb) => {
  let url = `${config.api.base_url}/RMS/updateLeaveApproval`;
  const headers = {
    'Content-Type': 'application/json'
  }
  axios
  .post(url,body
    , {
      headers: headers
    })
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};


export const updateLeaveRejection = (body, cb) => {
  let url = `${config.api.base_url}/RMS/updateLeaveRejection`;
  const headers = {
    'Content-Type': 'application/json'
  }
  axios
  .post(url,body
    , {
      headers: headers
    })
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
    });

};

export const updateLeaveCancellation = (body, cb) => {
  let url = `${config.api.base_url}/Rms/updateLeaveCancellation`;

  const headers = {
    'Content-Type': 'application/json',
  }
  axios
  .post(url, body
    , {
      headers: headers
    })
    .then(data => {
      if (cb) {
        cb(data);
      }
      //console.log(data);
    })
    .catch(error => {
      console.log(error)
    });
};

