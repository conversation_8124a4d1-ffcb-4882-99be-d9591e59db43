import { Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import {
  GetCommonspData, GetCommonspDataV2, updateLeaveCancellation
} from "../../store/actions/CommonAction";
import moment from 'moment';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';

const LeaveStatus = (props) => {
  const { Userid } = props
  const [userData, setUserData] = useState()
  const [open, setOpen] = useState(false);
  const [leaveIds, setLeaveIds] = useState(null);

  useEffect(() => {
    getLeaves();
  }, [])

  const getLeaves = () => {
    props.GetCommonspDataV2({
      root: 'GetLeaveStatus',
      // params: [{ UserId: Userid }],
      c: "R",
    }, function (errorStatus, result) {
      if (!errorStatus) {
        console.log(result)
        if (result && result.data[0].length > 0) {
          // console.log(result.data[0])
          setUserData(result.data[0]);
        }
      }
    });
  }

  const updateLeaveCancellationUser = (leaveIds) => {
    // console.log(LeaveId)
    leaveIds = [parseInt(leaveIds)];
    let body = { leaveIds: leaveIds }
    try {
      updateLeaveCancellation(body, function (results) {
        debugger;
        if (results && results.data && results.data.status == 200) {
          //  console.log(results.data.result)
          getLeaves();
        }
      })
    }
    catch (ex) {

    }
  }

  const handleOpenPopUp = (leaveId) => {
    setLeaveIds(leaveId);
    setOpen(true);
  }

  const handleConfirm = () => {
    if (leaveIds) {
      updateLeaveCancellationUser(leaveIds);
    }
    setOpen(false);
  };

  const ConfirmPopUp = () => {
    return (
      <Dialog open={open} onClose={() => setOpen(false)}>
        <DialogTitle>Are you sure you want to cancel this leave?</DialogTitle>
        <DialogActions>
          <Button onClick={() => setOpen(false)} color="primary">Cancel</Button>
          <Button onClick={handleConfirm} color="error">Yes</Button>
        </DialogActions>
      </Dialog>
    );
  }

  const LeaveStatusTable = () => {
    return (
      <TableContainer component={Paper} className="LeaveStatus">
        <Table sx={{ minWidth: 650 }} aria-label="simple table">
          <TableHead>
            <TableRow>
              <TableCell>Sr.No</TableCell>
            <TableCell>Created On</TableCell>
            <TableCell>Leave Date</TableCell>
            {/* <TableCell>End Date</TableCell> */}
            <TableCell>Leave Type</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Comments</TableCell>
            <TableCell>Action</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {userData && userData.map((row, index) => (
            <TableRow key=''>
              <TableCell>{index + 1}</TableCell>
              <TableCell>{moment(row.CreatedOn).format("DD/MM/YYYY")}</TableCell>
              <TableCell>{moment(row.StartDate).format("DD/MM/YYYY")}</TableCell>
              {/* <TableCell>{moment(row.EndDate).format("DD/MM/YYYY")}</TableCell> */}
              <TableCell>{row.LeaveType}</TableCell>
              <TableCell>
                {
                  `${row.StatusName} ${row.EmployeeName ? `(${row.EmployeeName?.length > 15 ? row.EmployeeName.slice(0, 15) + '...' : row.EmployeeName})` : ''}`
                }
              </TableCell>                <TableCell title={`Advisor : ${row.Comment ? row.Comment : '-'}
FirstApprover : ${row.FirstApproverComment ? row.FirstApproverComment : '-'}
SecondApprover : ${row.SecondApproverComment ? row.SecondApproverComment : '-'}`}>
                {`${row.Comment ? row.Comment.length > 15 ? row.Comment.slice(0, 15) + "..." : row.Comment : '-'}`}</TableCell>
              <TableCell align="left"> {row.LeaveType != 1 && row.Status == 3 ? <button className="Withdrawalbtn" onClick={() => { handleOpenPopUp(row.LeaveId) }}>Remove</button> : '-'}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer> 
    );
  }

  return (
    <>
      <LeaveStatusTable />
      <ConfirmPopUp />
    </>
  );
}

function mapStateToProps(state) {
  return {
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonspData,
    GetCommonspDataV2
  }
)(LeaveStatus);
