
import React, { Suspense, lazy } from 'react';
import {
  BrowserRouter,
  Routes,
  Route,
} from "react-router-dom";

import { createBrowserHistory } from "history";
import { createRoot } from 'react-dom/client';

// import "./assets/scss/paper-dashboard.scss?v=1.1.0";
// import "./assets/demo/demo.css";
// import "perfect-scrollbar/css/perfect-scrollbar.css";
// import 'bootstrap/dist/css/bootstrap.min.css';

import { createStore, applyMiddleware, compose } from "redux";
import { Provider } from "react-redux";
import thunk from "redux-thunk";
import rootReducer from "./Agent/store/reducers/rootReducers";
import { getCookie } from './Agent/utility/utility.jsx';
import config from "./Agent/config";
import routes from "./routes";

const store = createStore(rootReducer, compose(applyMiddleware(thunk)));

const hist = createBrowserHistory();
const AdminLayout = lazy(() => import('./Agent/layouts/Admin'));
const ClientLayout = lazy(() => import('./Agent/layouts/Client'));
const UnAuthenticated = React.lazy(() => import('./Agent/views/UnAuthenticated'));

let DefaultComponent = UnAuthenticated;
let DefaultAdminLayout = AdminLayout;
let DefaultClientLayout = ClientLayout;
let loader = <div>Loading...</div>;

if (config.byPassSecurity) {
  var flag = false;
  //auth check iframe and domain check
  //console.log("document.referrer", document.referrer)
  if (document.referrer != "") {
    let origin = document.referrer;
    let allowedOrigins = [".policybazaar.com", ".policybazaar.ae"]
    allowedOrigins.forEach(async function (val, key) {
      if (origin.includes(val)) {
        flag = true;
        console.log("flag", flag)
      }
    });
  }
  //cookie check if cookie exist need to improve this check
  var token = getCookie("AgentId");
  // if (getUrlParameter("u") != userid) {
  //   flag = true;
  // }
  if (token) {
    flag = true;
  }

  //By pass page level check from cookies
  for (let index = 0; index < config.byPassPages.length; index++) {
    const element = config.byPassPages[index];
    let pageurl = document.location.href.toLowerCase();
    if (pageurl.includes(element.toLowerCase())) {
      flag = true;
    }
  }

  console.log("after allowedOrigins check flag", flag)
  if (!flag) {
    DefaultComponent = UnAuthenticated;
    DefaultAdminLayout = UnAuthenticated
    DefaultClientLayout = UnAuthenticated
  }
}

const container = document.getElementById('root');
const root = createRoot(container);

root.render(
  <Provider store={store}>
    <BrowserRouter history={hist}>
      <Suspense fallback={loader}>
        <Routes>
          {/* <Route path="/admin" render={props => <DefaultAdminLayout {...props} />} />
          <Route path="/client" render={props => <DefaultClientLayout {...props} />} />
          <Route exact path="" render={props => <DefaultComponent {...props} />} /> */}

          {
            routes.map((prop, key) => {
              return (
                <Route
                  path={prop.layout + prop.path}
                  element={prop.component}
                  key={key} />);
            })
          }
        </Routes>
      </Suspense>
    </BrowserRouter>
  </Provider>
);
