import React, {useState, useEffect} from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { Autocomplete, Chip, FormControl, Grid, IconButton, MenuItem, Select, TextField, Tooltip } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ListIcon from '@mui/icons-material/List';
import {
    GetCommonData, GetCommonspData, GetCommonspDataV2, addAdvisorsProcess
  } from "../../store/actions/CommonAction";
import { connect } from "react-redux";


const AddAdvisorPopup = (props) => {

    const { open, onClose, processList, getRosterData, TLRosterStatusData } = props
    // const options = ['Option 1', 'Option 2', 'Option 3', 'Option 4'];
    // const options1 = [{options :'Option 1', Name : 'AYu'}, {options :'Option 2'}, {options :'Option 3'}, {options :'Option 4'}];
    const [options, setOptions] = useState([]);
    const [typedValue, setTypedValue] = useState();
    const [selectedProcess ,setSelectedProcess] = useState(0);
    const [addedAdvisors ,setaddedAdvisors ] = useState([]);

    useEffect(()=>{
        if(typedValue && typedValue.length >= 4){
            getEmployeeData();
        }
    },[typedValue])

    const getEmployeeData = () => {
        try {

            props.GetCommonspData({
                limit: 10,
                skip: 0,
                root: "GetRecommendEmployeeData",
                params: [{ EmployeeId: typedValue }],

                // ManagerIds: Supervisors,
                // slotdate: paramsData,
                c: "R"
                
            }, function (result) {
               console.log(result);
               console.log(result.data.data[0]);
               let data = result.data.data[0];
               let EmployeeIds = [];
            //    for(let  i= 0;i<data.length; i++){
            //     EmployeeIds.push(data[i].EmployeeId);
            //    }
            //    setOptions(EmployeeIds);
               setOptions(data);
            })
        }
      catch (ex) {
        
      }
    }

    const TextChange = (e) => {
        // console.log(e.target.value);
        setTypedValue(e.target.value);
    }

    const handleSave = (event, value) => {
        console.log(value);
        setaddedAdvisors(value);
    }

    const HandleProcessChange = (e) => {
        setSelectedProcess(e.target.value);
    }

    const addAdvisors = () => {
        // console.log();
        console.log(selectedProcess);
        if(selectedProcess > 0 && addedAdvisors.length > 0){
            let advisors = [];
            for(let i = 0; i < addedAdvisors.length; i++){
                if(advisors.indexOf(addedAdvisors[i].userId) == -1){
                    advisors.push(addedAdvisors[i].userId);

                }
                // advisors.push(addedAdvisors[i].userId);
            }
            console.log(advisors);
            let transferData = {
                userId: advisors,
                MovedToProcess: selectedProcess,
                RequestType: 1
            }
            addAdvisorsProcess(transferData, function (result) {
                if (result && result.data && result.data.status == 200) {
                    console.log(result);
                    getRosterData();
                    TLRosterStatusData();
                    setSelectedProcess(0);
                    onClose();
                }
            })
        }
        

    }
    

    return (
        <Dialog open={open} onClose={onClose} maxWidth="xs" className="AddAdvisorPopup">
            <DialogTitle>
                {"Add Advisor"}
                <IconButton
                    aria-label="close"
                    onClick={onClose}
                    sx={{ position: 'absolute', right: 8, top: 8, color: (theme) => theme.palette.grey[500] }}
                >
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent>
                <Grid container spacing={2}>
                    <Grid item md={12} sm={12} xs={12}>
                        <label>Current Process</label>
                        <FormControl fullWidth>
                            <Select className="BlueBgColor" onChange={HandleProcessChange}>
                                <MenuItem value="" >
                                    Select Process
                                </MenuItem>
                                {Object.keys(processList).map((process) =>
                                (
                                    <MenuItem value={process}>{processList[process].ProcessName} ({processList[process].count})
                                    </MenuItem>
                                )
                                )}
                                {/* <MenuItem value={2}>FF(12)</MenuItem>
                                <MenuItem value={3}>PED(10)</MenuItem> */}
                            </Select>
                        </FormControl></Grid>
                    <Grid item md={12} sm={12} xs={12}>
                        <label className="EmiDetails">Employee code or Employee name  
                        {/* <Tooltip title="Show list view" arrow><ListIcon /></Tooltip> */}
                            {/* <Tooltip title="Show pill view" arrow> <img src="/rms/attach.svg" /></Tooltip> */}
                        </label>
                        <Autocomplete
                            multiple
                            id="tags-outlined"
                            options={options}
                            onChange={handleSave}
                            getOptionLabel={(option) => (
                                option.EmployeeId +"\xa0\xa0\xa0" + option.EmployeeName
                            )}
                            // value= {option.EmployeeId}
                            // onClick = {handleSave}
                            // defaultValue={[options[1]]}
                            renderInput={(params) => (
                                <TextField onChange={TextChange}
                                    {...params}
                                    variant="outlined"
                                    placeholder='eg. PW37624'
                                    
                                />
                            )}
                            renderTags={(value, getTagProps) =>
                                value.map((option, index) => (
                                    <Chip variant="outlined" label={option.EmployeeId} {...getTagProps({ index })} />
                                ))
                            }
                        />

                    </Grid>
                    {/* <Grid item md={12} sm={12} xs={12}>
                        <label className="TextUppercase">Advisors to be added</label>
                        <div className="scrollbar">
                            <Grid container>
                                <Grid item md={6} sm={6} xs={6}><p className="EMIName">Keysang Yonthan</p></Grid>
                                <Grid item md={6} sm={6} xs={6}><p className="EmiCode">PW37624 <CloseIcon /></p></Grid>
                                <Grid item md={6} sm={6} xs={6}><p className="EMIName">Keysang Yonthan</p></Grid>
                                <Grid item md={6} sm={6} xs={6}><p className="EmiCode">PW37624 <CloseIcon /></p></Grid>
                                <Grid item md={6} sm={6} xs={6}><p className="EMIName">Keysang Yonthan</p></Grid>
                                <Grid item md={6} sm={6} xs={6}><p className="EmiCode">PW37624 <CloseIcon /></p></Grid>
                                <Grid item md={6} sm={6} xs={6}><p className="EMIName">Keysang Yonthan</p></Grid>
                                <Grid item md={6} sm={6} xs={6}><p className="EmiCode">PW37624 <CloseIcon /></p></Grid>
                                <Grid item md={6} sm={6} xs={6}><p className="EMIName">Keysang Yonthan</p></Grid>
                                <Grid item md={6} sm={6} xs={6}><p className="EmiCode">PW37624 <CloseIcon /></p></Grid>
                                <Grid item md={6} sm={6} xs={6}><p className="EMIName">Keysang Yonthan</p></Grid>
                                <Grid item md={6} sm={6} xs={6}><p className="EmiCode">PW37624 <CloseIcon /></p></Grid>
                            </Grid>
                        </div>
                    </Grid> */}
                    <Grid item md={12} sm={12} xs={12} className="alignCenter">
                        <button className="sendTransferbutton" onClick = {addAdvisors}>Add advisor</button>
                    </Grid>
                </Grid>
            </DialogContent>

        </Dialog>
    );
}

// export default AddAdvisorPopup;

function mapStateToProps(state) {
    return {
    };
  }
  
  export default connect(
    mapStateToProps,
    {
      GetCommonData,
      GetCommonspData,
      GetCommonspDataV2
    }
  )(AddAdvisorPopup);
