import React from 'react';
import { Grid, Table, TableBody, TableContainer, TableCell, TableHead, TableRow, Checkbox } from '@mui/material';
import "../app/css/ApprovalDashboard.scss"
const Approval = () => {
  return (
    <>
      <div className="approvalDashboard">
        <Grid container spacing={2}>
          <Grid item xs={12} md={2}>
            <div className="Approvalmenu">
              <p>Dashboard</p>
              <img src="/Lms/vistalogo.svg" />
              <ul>
                <li className="active">Pending</li>
                <li>Approved</li>
                <li>Rejected</li>
                <li>All</li>
              </ul>
            </div>
          </Grid>
          <Grid item xs={12} md={10}>

          <TableContainer className="ApprovalDetails" style={{ maxHeight: '500px' }}>
              <Table stickyHeader aria-label="customized table" >
                <TableHead>
                  <TableRow>
                    <TableCell align="left"><Checkbox/>  Request No.</TableCell>
                    <TableCell align="left">E-Code</TableCell>
                    <TableCell align="left">Emp Name</TableCell>
                    <TableCell align="left">Application Date</TableCell>
                    <TableCell align="left">Application Type</TableCell>
                    <TableCell align="left">Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
              
                <TableRow>
                    <TableCell align="left"><Checkbox/>  01</TableCell>
                    <TableCell align="left">PW37633</TableCell>
                    <TableCell align="left">Vineet Yadav</TableCell>
                    <TableCell align="left">10th October</TableCell>
                    <TableCell align="left">SL</TableCell>
                    <TableCell align="left"><button className="Approved">Approved</button></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell align="left"><Checkbox/>  02</TableCell>
                    <TableCell align="left">ET01843</TableCell>
                    <TableCell align="left">Gunjan Sharma</TableCell>
                    <TableCell align="left">10th October</TableCell>
                    <TableCell align="left">CL</TableCell>
                    <TableCell align="left"><button className="Rejected">Rejected</button></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell align="left"><Checkbox/>  03</TableCell>
                    <TableCell align="left">PW37633</TableCell>
                    <TableCell align="left">Vineet Yadav</TableCell>
                    <TableCell align="left">10th October</TableCell>
                    <TableCell align="left">SL</TableCell>
                    <TableCell align="left"><button className="Pending">Pending</button></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell align="left"><Checkbox/>  04</TableCell>
                    <TableCell align="left">ET01843</TableCell>
                    <TableCell align="left">Gunjan Sharma</TableCell>
                    <TableCell align="left">10th October</TableCell>
                    <TableCell align="left">CL</TableCell>
                    <TableCell align="left"><button className="Rejected">Rejected</button></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell align="left"><Checkbox/>  05</TableCell>
                    <TableCell align="left">PW37633</TableCell>
                    <TableCell align="left">Vineet Yadav</TableCell>
                    <TableCell align="left">10th October</TableCell>
                    <TableCell align="left">SL</TableCell>
                    <TableCell align="left"><button className="Pending">Pending</button></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell align="left"><Checkbox/>  06</TableCell>
                    <TableCell align="left">PW37633</TableCell>
                    <TableCell align="left">Vineet Yadav</TableCell>
                    <TableCell align="left">10th October</TableCell>
                    <TableCell align="left">SL</TableCell>
                    <TableCell align="left"><button className="Approved">Approved</button></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell align="left"><Checkbox/>  07</TableCell>
                    <TableCell align="left">ET01843</TableCell>
                    <TableCell align="left">Gunjan Sharma</TableCell>
                    <TableCell align="left">10th October</TableCell>
                    <TableCell align="left">CL</TableCell>
                    <TableCell align="left"><button className="Rejected">Rejected</button></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell align="left"><Checkbox/>  08</TableCell>
                    <TableCell align="left">PW37633</TableCell>
                    <TableCell align="left">Vineet Yadav</TableCell>
                    <TableCell align="left">10th October</TableCell>
                    <TableCell align="left">SL</TableCell>
                    <TableCell align="left"><button className="Pending">Pending</button></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell align="left"><Checkbox/>  09</TableCell>
                    <TableCell align="left">ET01843</TableCell>
                    <TableCell align="left">Gunjan Sharma</TableCell>
                    <TableCell align="left">10th October</TableCell>
                    <TableCell align="left">CL</TableCell>
                    <TableCell align="left"><button className="Rejected">Rejected</button></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell align="left"><Checkbox/>  10</TableCell>
                    <TableCell align="left">PW37633</TableCell>
                    <TableCell align="left">Vineet Yadav</TableCell>
                    <TableCell align="left">10th October</TableCell>
                    <TableCell align="left">SL</TableCell>
                    <TableCell align="left"><button className="Pending">Pending</button></TableCell>
                  </TableRow>

                </TableBody>
              </Table>
              <div className="BottomBtn">
              <button className="GetDetailsBtn">Get Details</button>
              <button className="FreezeBtn">Freeze</button>
              </div>
            </TableContainer>
          </Grid>
        </Grid>
      </div>
    </>
  );
};

export default Approval;
