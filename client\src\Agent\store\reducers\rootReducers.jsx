import { combineReducers } from "redux";

import AlertMasterReducer from "./AlertMasterReducer";
import CommonReducer from "./CommonReducer";
import AppReducer from './RuleReducer/AppReducer';
import RulesetReducer from './RuleReducer/RulesetReducer';
import SurveyReducer from "./SurveyReducer";
import QuizReducer from "./SurveyReducer";
import CourseReducer from "./CourseReducer";
import CourseQuiz from './CourseQuizReducer';

const rootReducer = combineReducers({
    CommonData : CommonReducer,
    Survey: SurveyReducer,
    Quiz: QuizReducer,
    Course: CourseReducer,
    CourseQuiz: CourseQuiz
});

export default rootReducer;
