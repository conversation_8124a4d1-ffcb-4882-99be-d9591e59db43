
async function bulkInsertBooking(recordArray){
    if(!recordArray){
      return {
        status: 200,
        message: "Array is Null"
      };
    }else if(recordArray.length === 0){
      return {
        status: 200,
        message: "Array Length is zero"
      };
    }else{
      try{
        var bulk = matrixdb.collection('BookingDetailsDump').initializeOrderedBulkOp();
        recordArray.forEach((record)=>{
          record.isRead = false;
          bulk.insert(record);
        });
        const result = await bulk.execute();
        return {
          status: 200,
          message: "Insertion Success"
        };
      }catch(err){
        console.log(err);
        return {
          status : 400,
          message : err
        };
      }
    }
    
  }
module.exports = {
    bulkInsertBooking
}