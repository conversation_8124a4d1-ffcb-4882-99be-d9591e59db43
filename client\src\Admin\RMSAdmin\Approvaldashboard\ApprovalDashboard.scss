* {
    margin: 0px;
    padding: 0px;
}

*::-webkit-scrollbar {
    width: 8px;
    height: 10px;
}

*::-webkit-scrollbar-thumb {
    margin-right: 10px;
    border-radius: 10px;
    box-shadow: inset 0 0 0 10px;
    color: #d6d4d4;

    :hover {
        color: rgba(0, 0, 0, 0.3);
    }
}

.mt-1 {
    margin-top: 1rem;
}

.mt-3 {
    margin-top: 3rem;
}

.theme-light {
    background-color: #fff;
}

.pd25 {
    padding: 10px 30px 65px !important;
}

.alignCenter {
    text-align: center;
}

.approvalDashboard {
    padding: 0px 20px 0px 0px;

    .pageStatus {
        color: rgba(37, 56, 88, 0.60);
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        display: flex;
        align-items: center;
        text-transform: uppercase;

        svg {
            color: #1C1B1F;
            width: 13px;
            margin-left: 5px;
            margin-right: 5px;
        }

        span {
            color: #0065FF;
            text-transform: capitalize;
        }

    }

    .Approvalmenu {
        width: 100%;
        border-right: 1px solid #ddd;
        padding: 15px;
        height: 100vh;

        img {
            margin-bottom: 40px;
        }

        ul {
            list-style-type: none;

            li {
                color: #253858;
                font-family: Roboto;
                font-size: 1.1vw;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
                padding: 10px 12px;
                margin-bottom: 5px;
            }

            .active {
                border-radius: 8px;
                background: rgba(0, 101, 255, 0.05);
                color: #0065FF;

            }
        }
    }

    .RosterLabels {
        font: normal normal 600 1.1vw/19px Roboto;
        letter-spacing: 0px;
        color: #253858;
        display: block;
        margin-top: 1rem;
        margin-bottom: 5px;
    }

    .RosterMaster {
        width: 98%;
        border-radius: 8px;
        background: #F5F5F5;

        .MuiOutlinedInput-input {
            padding: 8px;


        }

        fieldset {
            border: none;
        }
    }

    .SearchInput {
        position: relative;
        margin-top: 2.5rem;

        input {
            width: 100%;
            border-radius: 8px;
            background: #F5F5F5;
            border: none;
            outline: none;
            padding: 11px;
            text-indent: 28px;
            color: #253858;
            font-family: Roboto;
            font-size: 1.1vw;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
        }

        svg {
            position: absolute;
            z-index: 999;
            top: 9px;
            color: #1C1B1F;
            font-size: 23px;
            left: 10px;
        }
    }

    .ApprovalDetails {
        margin-top: 1.2em;

        .Mui-checked {
            svg {
                color: #0065FF !important;
            }
        }

        table {
            th {
                color: rgba(37, 56, 88, 0.60);
                font-family: Roboto;
                font-size: 1.1vw;
                font-style: normal;
                font-weight: 600;
                white-space: nowrap;
                width: 100px;
                line-height: normal;
                border-color: #7070701f;
                padding: 9px;
                background-color: #fff;

                &:first-child {
                    padding-left: 0px;
                    width: 60px;
                }

                &:last-child {
                    padding-right: 0px;
                }
            }

            td {
                color: rgba(37, 56, 88, 0.89);
                font-family: Roboto;
                font-size: 1.1vw;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
                border-color: #7070701f;
                white-space: nowrap;
                overflow: hidden;
                width: 100px;
                text-overflow: ellipsis;
                padding: 9px;

                &:first-child {
                    padding-left: 0px;

                }

                &:last-child {
                    padding-right: 0px;
                }

            }

            .MuiCheckbox-root {
                padding: 0px;

                svg {
                    width: 0.8em;
                    height: 0.8em;
                    color: #7070703d;
                }
            }

            button {
                font-family: Roboto;
                font-size: 1.1vw;
                font-style: normal;
                font-weight: 600;
                line-height: 15px;
                border: none;
                border-radius: 24px;
                background-color: transparent;
            }

            .Pending {

                color: #FEC617;
            }

            .Approved {
                color: #397444;

            }

            .Rejected {
                color: #CA2626;

            }

            .cancelbtn {
                background-color: #CA2626;
                color: #fff;
                padding: 5px 7px;
                cursor: pointer;
            }
        }

        .BottomBtn {
            position: absolute;
            bottom: 20px;
            right: 30px;

            button {
                color: #FFF;
                font-family: Roboto;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                border-radius: 8px;
                border: none;
                cursor: pointer;
            }

            .GetDetailsBtn {
                background: #0065FF;
                padding: 13px 15px;
                width: 120px;
                margin: 0px 14px;
            }

            .FreezeBtn {
                background-color: #FF2C2C;
                padding: 13px 40px;
                margin-left: 10px;
            }

            .disable {
                opacity: 0.3;
            }
        }
    }

    .multiApproval {
        border-radius: 8px;
        background: #F8F8F8;
        padding: 10px 30px 20px;
        margin-top: 1.5rem;

        .RosterDetails {
            display: flex;
            justify-content: space-between;

            ul {
                margin-bottom: 15px;
                list-style-type: none;
                display: flex;
                justify-content: space-between;
                width: 400px;

                li {
                    color: #25385899;
                    font-family: Roboto;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                    display: flex;
                    flex-direction: column;
                    line-height: 25px;

                    strong {
                        color: #253858e3;
                        font-family: Roboto;
                        font-size: 16px;
                        font-style: normal;
                        font-weight: 600;
                        line-height: normal;
                    }
                }
            }

            button {
                height: 38px;
                border-radius: 8px;
                border: 1px solid #0065FF;
                color: #0065FF;
                font-family: Roboto;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                padding: 10px 16px 10px 24px;
                display: flex;
                align-items: center;
                cursor: pointer;

                svg {
                    width: 16px;
                    margin-left: 5px;
                }
            }
        }

        .ActiveRoster {
            border-radius: 8px;
            background: rgba(0, 101, 255, 0.05);
            padding: 5px 20px;

            .ActiveRosterDate {
                display: flex;
                border-radius: 24px;
                background: rgba(30, 30, 30, 0.05);
                width: 350px;
                padding: 4px 10px 4px 15px;
                align-items: center;
                margin-top: 10px;
                color: rgba(37, 56, 88, 0.60);
                font-family: Roboto;
                font-size: 14px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;

                h4 {
                    color: rgba(37, 56, 88, 0.89);
                    font-weight: bold;
                    margin-right: 5px;
                }

                svg {
                    width: 10px;
                    color: #0065ff;
                    margin-right: 5px;
                }
            }

            th {
                background-color: #ebf0f8;
                border: none;
            }

            td {
                border: none;
            }
        }

        .UpComingRoster {
            background: #2323230D;
            margin-top: 1.5rem;

            .ActiveRosterDate {
                svg {
                    color: #F18E0A;
                }
            }

            th {
                background-color: transparent;
            }
        }
    }

    .scrollBar {
        height: 520px;
        overflow-y: auto;
    }

    .LeaveApproveFooter {
        position: static;
        width: 100%;
        z-index: 999;
        // bottom: 0px;
        padding-bottom: 10px;
        // left: 0px;
        // right: 0px;
        text-align: right;
        margin-top: 2rem;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #FFF 41.67%);

        .approvebtn {
            width: 200px;
            padding: 13px 34px;
            border-radius: 8px;
            background: #26A69A;
            border: none;
            color: #FFF;
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            margin: 0px 10px;
        }

        .RejectedBtn {
            width: 200px;
            border-radius: 8px;
            background: #F76767;
            padding: 13px 34px;
            border: none;
            color: #FFF;
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            margin: 0px 10px;
        }

        // .Undobtn {
        //     border-radius: 8px;
        //     background: #5C6BC0;
        //     width: 150px;
        //     padding: 13px 34px;
        //     color: #FFF;
        //     font-family: Roboto;
        //     font-size: 14px;
        //     font-style: normal;
        //     font-weight: 400;
        //     line-height: normal;
        //     border: none;
        //     float: left;
        //     margin-left: 10px;
        // }

        .disableBtn {
            opacity: 0.3;
        }
    }
}

.CommentPopup {
    .MuiDialog-paperWidthSm {
        width: 394px;

        .TextArea {
            background: #FFFFFF 0% 0% no-repeat padding-box;
            border: 1px solid #7070703D;
            border-radius: 8px;
            opacity: 1;
            width: 100%;
            margin: 5px 0px 15px 0px;

        }

        label {
            text-align: left;
            font: normal normal bold 14px/19px Roboto;
            letter-spacing: 0px;
            color: #253858;
            opacity: 1;
        }

        .MuiDialogContent-dividers {
            border-bottom: none;
        }

        .MuiDialogActions-root {
            justify-content: center;
        }

        .saveButton {
            background: #0065FF 0% 0% no-repeat padding-box;
            border-radius: 4px;
            font: normal normal 500 14px/19px Roboto;
            letter-spacing: 0px;
            width: 250px;
            padding: 10px;
            color: #FFFFFF;
            opacity: 1;
        }

        .CommonComment {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;

            p {
                display: flex;
                text-align: left;
                font: normal normal 600 1.1vw/16px Roboto;
                letter-spacing: 0px;
                color: #253858;
                opacity: 1;
                align-items: center;

                svg {
                    background-color: #0065ff;
                    padding: 4px;
                    width: 20px;
                    height: 20px;
                    margin-right: 5px;
                    border-radius: 13px;
                    color: #fff;
                }
            }

            .MuiSwitch-root {
                width: 30px;
                height: 17px;
                border-radius: 20px !important;
                padding: 0px;

                .MuiSwitch-switchBase {
                    top: 1px;
                    left: 1px;
                    padding: 0px;
                }

                .MuiSwitch-thumb {
                    width: 14px;
                    height: 14px;
                    border-radius: 50%;
                }

                .Mui-checked {
                    transform: translateX(14px);
                    color: #0065FF;
                }

            }
        }
    }

    .header {
        display: flex;
        align-items: center;
        flex-direction: column-reverse;
        float: left;
        padding: 0px;
        font: normal normal 500 10px/13px Roboto;
        letter-spacing: 0px;
        color: #253858;
        opacity: 1;

        svg {
            font-size: 15px;
        }
    }

    .MuiTypography-h6 {
        text-align: center;
        font: normal normal 500 21px/28px Roboto;
        letter-spacing: 0px;
        color: #253858;
        opacity: 1;
    }
}

.SearchAdvisorPopup {
    .MuiPaper-elevation {
        border-radius: 12px;
    }

    h2 {
        color: #253858E3;
        font-family: Poppins;
        font-size: 20px;
        font-style: normal;
        letter-spacing: 0;
        font-weight: 600;
        line-height: 32px;
        padding: 10px 22px 0px;
    }

    .RosterMaster {
        width: 100%;
        border-radius: 8px;
        background: #F5F5F5;

        .MuiOutlinedInput-input {
            padding: 8px;
        }

        fieldset {
            border: none;
        }
    }

    .RosterLabels {
        font: normal normal 600 1.1vw/19px Roboto;
        letter-spacing: 0px;
        color: #253858;
        display: block;
        margin-top: 1rem;

    }

    .SearchAdvisorBtn {
        width: auto;
        border-radius: 8px;
        background: #0065FF;
        padding: 12px 10px;
        border: none;
        color: #FFF;
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        margin: 5px 0px 0px;
    }
}

.Backdrop {
    // width: 100%;
    // height: 100vh;
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.7;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    position: fixed;
    z-index: 9;
}

.flexbox {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    height: 100%;
    align-items: center;
}

.bt-spinner {
    margin-left: 50vw;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: transparent;
    border: 4px solid #222;
    border-top-color: #009688;
    -webkit-animation: 1s spin linear infinite;
    animation: 1s spin linear infinite;
}

@-webkit-keyframes spin {
    -webkit-from {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    -webkit-to {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-webkit-keyframes spin {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes spin {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.LeaveManagementPopup {
    .MuiDialog-paperFullWidth {
        width: 82%;
        max-height: calc(100% - 10px);
        margin: 0px;

      
    
    }
    .MuiDialogContent-root {
        padding: 0px;
    }
}

.closeIcon {
    position: absolute;
    top: 10px;
    right: 10px;
}

.SingleApprovalPopup{
    .MuiDialog-paperFullWidth {
        width: 83%;
        max-height: calc(100% - 10px);
        margin: 0px;
        height: 600px;
        .MuiDialogContent-root{
            padding: 0px 10px;
              .MuiTabs-root{
                display: inline-block;
            }
            .MuiTabPanel-root{
                padding:0px;
            }
          
             .MuiTabs-flexContainer{
                gap:20px;
                    button{
                        font-size: 1.1vw;
                        text-transform: capitalize;
                    }
                }
            .multiApproval{
                margin-top:1rem;
                padding:10px 15px 15px;
                .RosterDetails {
                    li{
                        font-size: 1vw;
                        strong{
                            font-size: 1.1vw;
                        }
                    }
                }
               
                .ActiveRosterDate{
                    font-size: 1vw;
                    width:280px;
                }
                .ActiveRoster{
                    padding:5px 10px;
                    th, td{
                        text-align: center;
                    }
                }
                .ApprovalDetails{
                    margin-top:0.5em;
                }
            }
            .approvalDashboard{
                padding:0px;
            }
        }
    }
}