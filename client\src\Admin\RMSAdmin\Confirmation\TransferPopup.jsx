import React, { useEffect, useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { FormControl, Grid, IconButton, MenuItem, Select } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import {
    InsertTransferRequest
} from "../../store/actions/CommonAction";



const TransferPopup = (props) => {

    const { open, onClose, transferData, processList, getRosterData, TLRosterStatusData } = props;
    // console.log(props);
    const [EmployeeData, setEmployeeData] = useState();
    const [selectedProcess, setSelectedProcess] = useState();

    useEffect(() => {
        if (transferData.length > 0) {
            // console.log(JSON.parse(transferData));
            setEmployeeData(JSON.parse(transferData));
        }
    }, [transferData])

    const HandleProcessChange = (e) => {
        setSelectedProcess(e.target.value);
    }

    const HandleTransferRequest = (e) => {
        // console.log(EmployeeData.UserId, EmployeeData.ProcessId, selectedProcess)
        if (selectedProcess > 0) {
            try {
                let transferData = {
                    UserId: EmployeeData.UserId,
                    CurrentProcess: EmployeeData.ProcessId,
                    MovedToProcess: selectedProcess,
                    RequestType: 3
                }
                InsertTransferRequest(transferData, function (result) {
                    if (result && result.data && result.data.status == 200) {
                        // console.log(result);
                        getRosterData();
                        TLRosterStatusData();
                        onClose();
                    }
                })
            }
            catch (ex) {

            }
        }

    }

    return (
        <>
            {EmployeeData &&
                <Dialog open={open} onClose={onClose} maxWidth="xs" className="transferPopup">
                    <DialogTitle>
                        {"Transfer process"}
                        <IconButton
                            aria-label="close"
                            onClick={onClose}
                            sx={{ position: 'absolute', right: 8, top: 8, color: (theme) => theme.palette.grey[500] }}
                        >
                            <CloseIcon />
                        </IconButton>
                    </DialogTitle>
                    <DialogContent>
                        <Grid container spacing={4}>
                            <Grid item md={6} sm={6} xs={6}>
                                <label>Employee Code</label>
                                <h4>{EmployeeData.EmployeeId}</h4>
                            </Grid>
                            <Grid item md={6} sm={6} xs={6}>
                                <label>TL</label>
                                <h4>{EmployeeData.TLName}</h4>
                            </Grid>
                            <Grid item md={6} sm={6} xs={6}>
                                <label>Current Process</label>
                                <FormControl fullWidth>
                                    <Select className="BlueBgColor"
                                        // placeholder='less'
                                        value={2}
                                    >
                                        <MenuItem disabled="true" value={2}>{EmployeeData.ProcessName}</MenuItem>
                                        {/* <MenuItem value={3}>03 Oct 2023 - 15 Oct 2023</MenuItem>
                                <MenuItem value={4}>04 Oct 2023 - 15 Oct 2023</MenuItem> */}
                                    </Select>
                                </FormControl></Grid>
                            <Grid item md={6} sm={6} xs={6}>
                                <label>Move to Process</label>
                                <FormControl fullWidth>
                                    <Select className="BlueBgColor" onChange={HandleProcessChange}>
                                        <MenuItem value="" disabled>
                                            <em>Select Process</em>
                                        </MenuItem>
                                        {/* <MenuItem value={2}>FF(12)</MenuItem>
                                        <MenuItem value={3}>PED(10)</MenuItem> */}
                                        {Object.keys(processList).map((process) => (
                                            <MenuItem value={process} disabled={processList[process].ProcessName === EmployeeData.ProcessName} >
                                                {processList[process].ProcessName} ({processList[process].count})
                                            </MenuItem>
                                        )
                                        )}

                                    </Select>
                                </FormControl></Grid>
                            <Grid item md={12} sm={12} xs={12} className="alignCenter">
                                <button className="sendTransferbutton" onClick={HandleTransferRequest}>Send transfer request</button>
                            </Grid>
                        </Grid>
                    </DialogContent>

                </Dialog>}
        </>
    );
}

export default TransferPopup;
