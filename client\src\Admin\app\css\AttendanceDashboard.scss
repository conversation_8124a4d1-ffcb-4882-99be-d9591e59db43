* {
    margin: 0px;
    padding: 0px;
}

*::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

*::-webkit-scrollbar-thumb {
    margin-right: 10px;
    border-radius: 10px;
    box-shadow: inset 0 0 0 10px;
    color: #d6d4d4;

    :hover {
        color: rgba(0, 0, 0, 0.3);
    }
}

.downloadDropdown {
    .MuiTooltip-tooltip {
        margin-top: 5px !important;
        box-shadow: rgba(0, 0, 0, 0.2) 0px 5px 5px -3px, rgba(0, 0, 0, 0.14) 0px 8px 10px 1px, rgba(0, 0, 0, 0.12) 0px 3px 14px 2px;
        border-radius: 4px;
        background-color: #fff;
        font: normal normal 600 13px/30px Roboto;
        letter-spacing: 0px;
        color: #25385899;
        width: 120px;
        cursor: pointer;
    }
}

.Caption {
    text-align: left;
    font: normal normal 600 14px/19px <PERSON>o;
    letter-spacing: 0px;
    color: #25385899;
    text-transform: uppercase;
}

.heading {
    text-align: left;
    font: normal normal 600 24px/31px Roboto;
    letter-spacing: 0px;
    color: #253858;
    opacity: 1;
}

.RosterMaster {
    margin: 5px auto 0px;
    //z-index: 999;
    font: normal normal 600 13px/19px Roboto;
    .css-1nmdiq5-menu{
        z-index: 9999999;
    }
}

.RosterLabels {
    font: normal normal 600 14px/19px Roboto;
    letter-spacing: 0px;
    color: #25385899;
}

.day {
    font: normal normal 600 12px/16px Roboto;
    letter-spacing: 0px;
    color: #25385899;
    text-transform: uppercase;
}

.calendar {
    text-align: left;
    font: normal normal 600 19px/28px Roboto;
    letter-spacing: 0px;
    color: #253858;
    opacity: 1;
    margin-bottom: 10px;
    display: flex;
}

.calendarIcon {
    float: left;
    margin-right: 10px;
    position: relative;
    top: 14px;
}

.AttendanceDashboard {
    .AgentPresent {        
        background-color: #aeecae;
    }

    .AgentUnplanned {
        background-color: #f5ec55;
    }

    .AgentAbsent {
        background-color: #ffa5a2;
    }

    .AttendanceDate {
        background-color: #ddd;
    }

    width: 100%;
    overflow-x: scroll;
    overflow-y: auto;
    padding: 0;
    margin-top: 1.4rem;
    height: 435px;

    table {
        // border-collapse: collapse;
        // width: 100%;

        border-collapse: separate;
        border-spacing: 0;
        // border-top: 1px solid #7070701F;

        thead {
            position: sticky;
            top: 0px;
            z-index: 999;

            tr {
                th {
                    text-align: center;
                    padding: 3px;
                    font: normal normal 600 12px/16px Roboto;
                    letter-spacing: 0px;
                    color: #253858;
                    min-width: 180px;
                    border: 1px solid #7070701F;
                    white-space: nowrap;

                    &:first-child {
                        position: sticky;
                        left: 0;
                        background-color: #fff;
                        min-width: 100px;
                    }

                    &:nth-child(2) {
                        background: #B8BACF;
                    }

                    &:nth-child(3) {
                        background: #999AC6;
                    }
                }

            }
        }

        tbody {

            tr {
                background-color: #fff;

                td {
                    text-align: center;
                    padding: 5px 10px;
                    font: normal normal 600 14px/19px Roboto;
                    letter-spacing: 0px;
                    color: #253858;
                    min-width: 110px;
                    border: 1px solid #7070701F;
                    background-color: #fff;
                    position: relative;

                    &:first-child {
                        position: sticky;
                        left: 0;
                        min-width: 126px;
                        font-weight: normal;
                        z-index: 99;
                    }

                    &:nth-child(2) {
                        position: sticky;
                        left: 126px;
                        min-width: 110px;
                        font-weight: normal;
                        z-index: 99;
                    }

                    .day {
                        font: normal normal 600 12px/14px Roboto;
                        text-transform: capitalize;
                    }

                    .checkBoxTick {
                        float: left;
                        padding: 0px;
                        display: none;
                        height: 16px;
                        position: absolute;

                        svg {
                            width: 0.6em;
                            height: 0.6em;
                        }

                    }

                    &:hover {
                        .checkBoxTick {
                            display: block;
                        }
                        .AllcheckBoxTick {
                            display: inline-block;
                        }
                    }
                    .AllcheckBoxTick {
                        padding: 0px;
                        position: absolute;
                        display: none;
                        left: 6px;
                        top: 6px;
                    
                        svg {
                            width: 0.6em;
                            height: 0.6em;
                        }
                    }

                    .Mui-checked {
                        display: block;
                        color: #253858;
                    }
                }

                .HideColumn {
                    display: none;
                }

                &:nth-last-child(2) {
                    position: sticky;
                    bottom: 50px;
                    z-index: 9999;

                    td {
                        background-color: #E2EDFF;

                        &:first-child {
                            font-size: 10px;
                            font-weight: 600;
                            line-height: 17px;
                            background-color: #C6DCFF;

                            span {
                                font: normal normal 600 22px/29px Roboto;
                            }
                        }
                    }
                }

                &:last-child {
                    position: sticky;
                    bottom: 0;
                    z-index: 999;

                    td {
                        background-image: url(../../../../public/Lms/bg.svg);
                        color: #fff;

                        &:first-child {
                            left: 126px;
                            background-color: #AACBFF;
                            color: #253858;
                            background-image: none;
                            min-width: 184px;
                        }

                        &:nth-child(2) {
                            position: static;
                        }
                    }
                }

                &:first-child {
                    position: sticky;
                    top: 23px;
                    z-index: 999;
                }
            }

            .weeklyOf {
                color: #253858;
            }

            .earnedLeave {
                // color: #8E884E;
                background-color: #72f7ff9c;
            }

            .causalLeave {
                color: #4978AA;
                
            }

            .eligibleDates {
                background-color: #1976d280;
            }

        }

    }
}

.FreezeBtn {
    border: none;
    padding: 8px;
    float: right;
    background: #FF2C2C;
    border-radius: 4px;
    color: #fff;
    font: normal normal 500 14px/19px Roboto;
    margin-left: 10px;
    cursor: pointer;
    width: 122px;
}

.ResetBtn {
    background: #0000004d;
    border-radius: 4px;
    color: #fff;
    border: none;
    padding: 8px;
    float: right;
    font: normal normal 500 14px/19px Roboto;
    cursor: pointer;
    width: 116px;
}



.downloadBtn {

    border: none;
    font-size: 14px;
    padding: 6px;
    border-radius: 6px;
    margin-top: 1.5rem;
    font-weight: 600;
    cursor: pointer;
}

.textCenter {
    text-align: center;
}

.AlartPopup {
    z-index: 99999999;

    .MuiBox-root {
        padding: 20px;
        border: none;
        border-radius: 8px;
        width: 230px;

        h2 {
            font: normal normal 600 17px/19px Roboto;
            color: #253858;

        }
    }
}

