
import React, { lazy } from "react";

import Loadable from './Admin/app/components/Loadable';



const AgentLayout = React.lazy(() => import('./Agent/layouts/Admin'));


const LeaveApp = React.lazy(() => import('./Agent/views/LeaveManagement/App/LeaveApp'));
// const ApplyLeave = React.lazy(() => import('./views/LeaveManagement/Applyleave.jsx'));
const LeaveStatus = React.lazy(() => import('./Agent/views/LeaveManagement/LeaveStatus'));
const RmsRedirection = React.lazy(() => import('./Agent/views/LeaveManagement/RmsRedirection'));
const AdminLayout = React.lazy(() => import('./Admin/app/App.jsx'));
const RmsApp = React.lazy(() => import('./Admin/app/RmsApp'));



// session pages

const NotFound= Loadable(lazy(()=>import ('./Admin/app/views/sessions/NotFound.jsx')))

const JwtLogin = Loadable(lazy(() => import('./Admin/app/views/sessions/JwtLogin')));
const JwtRegister = Loadable(lazy(() => import('./Admin/app/views/sessions/JwtRegister')));
const ForgotPassword = Loadable(lazy(() => import('./Admin/app/views/sessions/ForgotPassword')));

// echart page
// const AppEchart = Loadable(lazy(() => import('./Admin/app/views/charts/echarts/AppEchart')));

// // dashboard page
// const Analytics = Loadable(lazy(() => import('./Admin/app/views/dashboard/Analytics')));

var routes = [
   
    { path: '/session/404', layout:"/agent", component: <AgentLayout element={<NotFound />}/>} ,
      { path: '/session/signin', element: <JwtLogin /> },
      { path: '/session/signup', element: <JwtRegister /> },
      { path: '/session/forgot-password', element: <ForgotPassword /> },
    
  
    {
        path: "/LeaveManagement/*",
        name: "LeaveManagement",
        icon: "nc-icon nc-minimal-right",
        component: <AgentLayout element={<LeaveApp />}/>,
        layout: "/agent",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13],
        authCheck: true
    },
    {
        path: "/LeaveStatus/*",
        name: "LeaveStatus",
        icon: "nc-icon nc-minimal-right",
        component: <AgentLayout element={<LeaveStatus />}/>,
        layout: "/agent",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13],
        authCheck: true
    },
    {
        path: "/RmsRedirection",
        name: "RmsRedirection",
        icon: "nc-icon nc-minimal-right",
        component: <AgentLayout element={<RmsRedirection />}/>,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13],
        authCheck: true
    },
    {
        path: "/",
        name: "adminRedirection",
        icon: "nc-icon nc-minimal-right",
        component: <AdminLayout />,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13],
        authCheck: true
    },
    // {
    //     path: "/RmsRedirection/",
    //     name: "RmsRedirection",
    //     icon: "nc-icon nc-minimal-right",
    //     component: <AdminLayout element={<RmsRedirection />}/>,
    //     layout: "/admin",
    //     RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13],
    //     authCheck: true
    // },
    {
        path: "/Rms/*",
        name: "LeaveStatus",
        icon: "nc-icon nc-minimal-right",
        component: <AdminLayout element={<RmsApp />}/>,
        layout: "/admin",
        RoleId: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13],
        authCheck: true
    }
   
];
export default routes;