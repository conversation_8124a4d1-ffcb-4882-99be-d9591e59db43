const Utility = require("../../Libs/Utility");
const sqlHelper = require("../../Libs/sqlHelper");
const { GET_UserLotteyTicket_Schema, GET_RewardsList_Schema, GET_TicketCount_Schema, POST_TicketWinnerDetail_Schema } = require("./Joi");

//Code By <PERSON><PERSON><PERSON><PERSON>
async function GetUserLotteryTickets(req, res) {
  const { error } = GET_UserLotteyTicket_Schema.validate(req.query);
  if (error && error.details[0]) {
    return res.send({
      status: 403,
      message: error.details[0].message,
    });
  }

  try {

    let query = `SELECT * FROM enc.Lottery_Tickets (NOLOCK) WHERE IsActive = 1 AND AssignToUserId = ` + req.query.UserID;
    console.log(query);
    let result = await sqlHelper.sqlquery("L", query);
    console.log("Query Result: ", result);
    res.send({
      status: 200,
      data: result.recordsets
    });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    return;
  }
}

async function GetBuList(req, res) {

  try {

    let query = `SELECT LBU.Id, LBU.BUName as Display FROM enc.Lottery_BU LBU (NOLOCK) WHERE IsActive = 1 and QuizId = `+req.query.QuizId+` AND ProductId = ` + req.query.ProductId;
    console.log(query);
    let result = await sqlHelper.sqlquery("L", query);
    console.log("Query Result: ", result);
    res.send({
      status: 200,
      data: result.recordsets
    });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    return;
  }
}


async function getCurrentQuiz(req, res) {

  try {

    let query = `SELECT top 1 * from enc.Lottery_QuizMaster (nolock) where IsActive = 1  order by id DESC`;
    console.log(query);
    let result = await sqlHelper.sqlquery("L", query);
    console.log("Query Result: ", result);
    res.send({
      status: 200,
      data: result.recordsets
    });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    return;
  }
}

async function GetRewardsList(req, res) {
  const { error } = GET_RewardsList_Schema.validate(req.query);
  if (error && error.details[0]) {
    return res.send({
      status: 403,
      message: error.details[0].message,
    });
  }

  try {

    let query = `SELECT RewardId, RewardTitle, MaxRewards, PendingRewards, BURM.IsActive FROM enc.Lottery_BU_Rewards_Mapping BURM (NOLOCK)
                    INNER JOIN enc.Lottery_Rewards RW (NOLOCK) ON RW.id = BURM.RewardId
                    WHERE BURM.IsActive = 1 AND BURM.PendingRewards > 0 AND BURM.BUId = ` + req.query.BUId;
    console.log(query);
    let result = await sqlHelper.sqlquery("L", query);
    console.log("Query Result: ", result);
    res.send({
      status: 200,
      data: result.recordsets
    });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    return;
  }
}
async function GetRewardDetails(req, res) {


  try {

    let query = `SELECT * FROM enc.Lottery_BU_Rewards_Mapping where BUId = ` + req.query.BUId + ' and RewardId= ' + req.query.RewardId;
    console.log(query);
    let result = await sqlHelper.sqlquery("L", query);
    console.log("Query Result: ", result);
    res.send({
      status: 200,
      data: result.recordsets
    });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    return;
  }
}

async function GetTicketCount(req, res) {
  const { error } = GET_TicketCount_Schema.validate(req.query);
  if (error && error.details[0]) {
    return res.send({
      status: 403,
      message: error.details[0].message,
    });
  }

  try {

    //let query = `SELECT COUNT(1) TicketCount FROM enc.Lottery_Tickets where BUId = ${req.query.BUId} AND RewardId = ${req.query.RewardId}`;
    let query = `SELECT SUM(TotalTickets) TicketCount,COUNT(1) AgentCount,SUM(CASE WHEN EA.TotalTickets > 0 THEN 1 else 0 END) EligibleAgents,EA.BUId,EA.RewardId, BU.BUName, RD.RewardTitle FROM enc.Lottery_EligibleAgents EA
    JOIN enc.Lottery_BU BU ON BU.Id = EA.BUId
    JOIN enc.Lottery_Rewards RD ON RD.Id = EA.RewardId
    WHERE EA.BUId = ${req.query.BUId} and EA.RewardId = ${req.query.RewardId}
    GROUP BY EA.BUId,EA.RewardId, BU.BUName, RD.RewardTitle
    `;




    console.log(query);
    let result = await sqlHelper.sqlquery("L", query);
    console.log("Query Result: ", result);
    res.send({
      status: 200,
      data: result.recordsets
    });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    return;
  }
}

async function GetWinnerDetail(req, res) {
  const { error } = POST_TicketWinnerDetail_Schema.validate(req.body.params);
  if (error && error.details[0]) {
    return res.send({
      status: 403,
      message: error.details[0].message,
    });
  }

  try {

    let param = req.body.params;

    let sqlparam = [];
    sqlparam.push({ key: "BUId", value: param.BUId });
    sqlparam.push({ key: "RewardId", value: param.RewardId });
    sqlparam.push({ key: "TicketNumber", value: param.TicketNumber });

    let spresult = await sqlHelper.sqlProcedure("L", "[enc].[DrawLottery]", sqlparam);
    console.log("Query Result: ", spresult);
    res.send({
      status: 200,
      data: spresult.recordsets
    });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    await sql.close();
  }
}
async function GetWinners(req, res) {


  try {




    let query = `SELECT RANK() OVER (order by t.updatedOn) winno, t.TicketNumber,IsWinner,ud.EmployeeId,ud.UserName FROM enc.Lottery_Tickets (nolock) t join crm.userdetails ud (nolock) on t.assigntouserid = ud.userid
    WHERE t.BUId = ${req.query.BUId} and t.RewardId = ${req.query.RewardId}
    and IsWinner=1
    `;

    console.log(query)

    let spresult = await sqlHelper.sqlquery("L", query);
    console.log("Query Result: ", spresult);
    res.send({
      status: 200,
      data: spresult.recordsets
    });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    await sql.close();
  }
}

async function GetTickets(req, res) {


  try {

    //let query = `SELECT COUNT(1) TicketCount FROM enc.Lottery_Tickets where BUId = ${req.query.BUId} AND RewardId = ${req.query.RewardId}`;
    let query = `Select * from enc.Lottery_Tickets 
    where IsActive = 1 
    and BUId = ${req.query.BUId} and RewardId = ${req.query.RewardId}
    order by RandomNo desc`;




    console.log(query);
    let result = await sqlHelper.sqlquery("L", query);
    console.log("Query Result: ", result);
    res.send({
      status: 200,
      data: result.recordsets
    });
  }
  catch (e) {
    console.log(e);
    res.send({
      status: 500,
      message: e
    });
  }
  finally {
    return;
  }
}

module.exports = {
  GetUserLotteryTickets: GetUserLotteryTickets,
  GetBuList: GetBuList,
  GetRewardsList: GetRewardsList,
  GetTicketCount: GetTicketCount,
  GetWinnerDetail: GetWinnerDetail,
  GetWinners: GetWinners,
  GetRewardDetails: GetRewardDetails,
  GetTickets: GetTickets,
  getCurrentQuiz
};