const compression = require('compression');
const express = require("express");
const path = require("path");
const bodyParser = require("body-parser");
const newrelic = require("newrelic")

const MongoClient = require("mongodb").MongoClient;
const http = require("http");
sql = require("mssql");
mysql = require('mysql');
const cors = require("cors");
require('dotenv').config({ path: `.${process.env.NODE_ENV}.env`});
const SQLdb = require("./modules/SQLdb/SQLdbRoute");
const SQLdbPBCroma = require("./modules/SQLdbPBCroma/SQLdbRoute");
const Survey = require("./modules/Survey/Route");
const Quiz = require("./modules/Quiz/Route");
const Contest = require("./modules/Contest/Route");
const Rms = require("./modules/Rms/RmsRoute")
const Metrics = require("./modules/Metrics/Route");
const LeaveRequest = require("./modules/LeaveRequest/LeaveRequestRoute");
const { GetParsedConfigFromCache, GetParsedConfigLocal } = require('./modules/common/CommonMethods');
const fileUpload = require('express-fileupload');
require("./getMongoConnection");
const conf = require("./env_config");
const { Auth } = require('./auth');
const { PersonaAccess } = require('./PersonaAccess');
global.appRoot = path.resolve(__dirname);
let app = express();

const csp = require('content-security-policy');
const sts = require('strict-transport-security');
const referrerPolicy = require('referrer-policy');
const nosniff = require('dont-sniff-mimetype');
//const xXssProtection = require("x-xss-protection");
const featurePolicy = require('feature-policy');

const cspPolicy = {
  'frame-ancestors': [csp.SRC_SELF, "*.policybazaar.com", "*.policybazaar.ae"],
  'object-src': [csp.SRC_NONE]
};

const globalCSP = csp.getCSP(cspPolicy);
const globalSTS = sts.getSTS({ 'max-age': { 'days': 30 }, 'includeSubDomains': true });


// Compress all HTTP responses
app.use(compression());
app.use(globalCSP);
// This will apply this policy to all requests
app.use(globalSTS);
app.use(referrerPolicy({ policy: 'strict-origin' }));
app.use(nosniff());
app.use(featurePolicy({
  features: {
    fullscreen: ["'self'"],
    vibrate: ["'self'"],
    syncXhr: ["'self' *.policybazaar.com *.policybazaar.ae"]
  }
}))

app.disable("x-powered-by");

app.set("port", conf.PORT);

app.use(cors());
app.use(fileUpload({
  limits: { fileSize: 50 * 1024 * 1024 },
  useTempFiles: true,
  tempFileDir: '/tmp/'
}));

app.use(
  bodyParser.urlencoded({
    limit: "50mb",
    extended: true
  })
);

app.use(
  bodyParser.json({
    limit: "50mb"
  })
);

app.use("/api/v1/db/", Auth, PersonaAccess, SQLdb);
app.use("/api/v1/PBCroma/", Auth, PersonaAccess, SQLdbPBCroma);
app.use("/html/", Auth, SQLdb);
// app.use("/api/v1/InternalSurvey", Survey);
// app.use("/api/v1/InternalQuiz", Quiz);
// app.use("/api/v1/InternalContest/", Contest);
app.use("/api/v1/RMS/", Auth, PersonaAccess, Rms);
app.use("/api/v1/Metrics/", Metrics);

app.use("/api/v1/LeaveRequest/", Auth, LeaveRequest);

app.use(express.static(path.join(__dirname, "public")));
app.use(Auth, express.static(path.join(__dirname, "client/build")));


app.get("*", Auth, function (req, res) {
  // console.log("URL123456: ", req.url);
  res.sendFile(path.join(__dirname, "client/build", "index.html"));
});

app.use(function (err, req, res, next) {
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader(
    "Access-Control-Allow-Methods",
    "GET, POST, OPTIONS, PUT, PATCH, DELETE"
  );
  res.setHeader(
    "Access-Control-Allow-Headers",
    "X-Requested-With,access_token, api_key, content-type,versions"
  );
  res.setHeader("Access-Control-Allow-Credentials", true);
  res.setHeader("X-XSS-Protection", "1; mode=block");

  // res.setHeader("Content-Security-Policy", "frame-ancestors 'self' *.policybazaar.com");
  // res.setHeader("Referrer-Policy", "strict-origin");
  // res.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
  // res.setHeader("X-Content-Type-Options", "nosniff");
  // res.setHeader("X-XSS-Protection", "1; mode=block");

  next();
});

global.app = app;

let startServer = http.createServer(app).listen(app.get("port"), async function () {
  console.log("Server connected on port :", app.get("port"));
  await startInitialProcess();
});

async function startInitialProcess() {
  let SecretConfig = {};
  try {
    if(process.env.ENVIRONMENT_MTX_DASH === 'DEV_MTX_DASH') {
      SecretConfig = await GetParsedConfigLocal();
      global.SecretConfig = SecretConfig;
      console.log("Database Connected Local >> ", SecretConfig?.SQL_URL?.server);
    } else {
      SecretConfig = await GetParsedConfigFromCache({ key: 'ENV_CONFIG' });
      global.SecretConfig = SecretConfig;
      console.log("Database Connected :L >> ", SecretConfig?.SQL_URL?.server);
    }
    // console.log("SecretConfig", SecretConfig);
    // matrixclient = await MongoClient.connect(SecretConfig?.MONGO_URL_MATRIX?.connectionString, {
    //   useNewUrlParser: true,
    //   useUnifiedTopology: true,
    //   auth: {
    //     username: SecretConfig?.MONGO_URL_MATRIX?.user,
    //     password: SecretConfig?.MONGO_URL_MATRIX?.password
    //   }
    // });
    // matrixdb = matrixclient.db(SecretConfig?.MONGO_URL_MATRIX?.collection);
   
  } catch (error) {
    console.log('Inside startInitialProcess', error);
    throw error;
  }
}

process.on("message", function (message) {
  console.log("Received signal : " + message);
  if (message === "shutdown") {
    startServer.close();
    setTimeout(function () {
      process.exit(0);
    }, 15000);
  }
});
