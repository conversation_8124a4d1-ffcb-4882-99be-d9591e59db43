* {
    margin: 0px;
    padding: 0px;
    box-sizing: border-box;
}

$base-color: #253858;
$whitecolor: #fff;
$BasefontSize: normal normal 600 14px/19px Roboto;
$boxShadow: 0px 3px 12px #D0D0D029;
$peraFontsize: normal normal 600 12px/16px Roboto;
$headingFontSize: normal normal 600 16px/21px Roboto;

*::-webkit-scrollbar {
    width: 5px;
    height: 3px;
}

*::-webkit-scrollbar-thumb {
    margin-right: 10px;
    border-radius: 10px;
    box-shadow: inset 0 0 0 10px;
    color: #d6d4d4;

    :hover {
        color: rgba(0, 0, 0, 0.3);
    }
}

@mixin DisplayFlex {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

@font-face {
    font-family: "Proxima Nova";
    src: url("../../../../public/font/ProximaNovaFont.otf");
    font-weight: normal;
    font-style: normal;
}

.alignRight {
    justify-content: right !important;
}

body {
    background-color: #FAFCFF;
    box-sizing: border-box;
}

.text-center {
    text-align: center;
}



.LeaveManagementLayout {
    padding: 25px 15px;

    @media (min-width: 320px) and (max-width: 1024px) {
        padding: 20px 15px;
    }

    .LeaveMgtHeader {

        .innerContext {
            display: flex;
            flex-wrap: wrap;
        }

        .inner {
            background: transparent;
            border-radius: 0;
            padding: 0;
            box-shadow: none !important;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
            color: rgba(37, 56, 88, 0.89);

            p {
                font-size: inherit;
                color: inherit;
                font-weight: 400;
                display: block;
                margin-bottom: 2px;
            }

            img {
                float: left;
                margin-right: 10px;
            }


        }

        .pagetitle {
            font: $BasefontSize;
            letter-spacing: 0px;
            color: $base-color;
            text-transform: uppercase;
            opacity: 0.6;
        }

        h2 {
            text-align: left;
            font: normal normal 600 30px/43px Roboto;
            letter-spacing: 0px;
            color: $base-color;
            opacity: 1;
            display: inline-block;
        }

        .banner {
            margin: 12px 0px 15px;
            position: relative;
            height: 40px;

            @media(min-width: 320px) and (max-width: 1024px) {
                width: auto;
            }

            img {
                width: 100%;
                height: 40px;
            }

            .bannertext {
                position: absolute;
                top: 0;
                width: 92%;
                @include DisplayFlex;
                bottom: 0;
                left: 0;
                right: 0;
                margin: auto;
                font: $BasefontSize;
                letter-spacing: 0px;
                color: $whitecolor;
                opacity: 1;

                span {
                    font: $BasefontSize;
                    letter-spacing: 0px;
                    color: #FF2C2C;
                }

                button {
                    background: $whitecolor;
                    border-radius: 4px;
                    opacity: 1;
                    font: $BasefontSize;
                    border: none;
                    width: 164px;
                    outline: none;
                    letter-spacing: 0px;
                    color: #000000;
                    text-transform: uppercase;
                    height: 41px;
                    cursor: pointer;
                }
            }
        }

        ul {
            float: right;
            display: flex;
            width: 68%;
            list-style-type: none;

            hr {
                color: #0000;
                opacity: 0.12;
                height: 36px;
                margin: 4px 28px;
            }

            li {


                width: 100%;
                font: normal normal 500 17px/29px Roboto;
                color: $base-color;

                p {
                    text-align: left;
                    font: $peraFontsize;
                    letter-spacing: 0px;
                    color: $base-color;
                    text-transform: uppercase;
                    opacity: 0.6;
                }

                img {
                    float: left;
                    position: relative;
                    top: 8px;
                    margin-right: 12px;
                }
            }
        }
    }

    .TabButton {
        list-style-type: none;
        display: flex;
        width: 100%;
        border-bottom: 1px solid #ddd;
        margin-bottom: 20px;
        padding-bottom: 0px;

        li {
            margin: 0px 15px 0px 0px;
            width: 160px;
            font: $BasefontSize;
            line-height: 35px;
            letter-spacing: 0px;
            color: $base-color;
            opacity: 0.6;
            cursor: pointer;
            text-align: center;
        }

        .active {
            color: #0065FF;
            opacity: 1;
            border-bottom: 2px solid;
        }
    }

    .leftSide {
        background: $whitecolor;
        box-shadow: $boxShadow;
        border-radius: 8px;
        padding: 0px 10px 25px 20px;
        height: 530px;
        overflow-y: auto;

        @media (min-width: 320px) and (max-width: 1024px) {
            height: auto;
            padding: 0px 10px;
        }

        p {
            font: $BasefontSize;
            letter-spacing: 0px;
            color: #25385899;
            text-transform: uppercase;
            padding: 10px 2px 4px;
            position: sticky;
            top: 0;
            background-color: $whitecolor;
        }

        ul {
            list-style-type: none;
            display: flex;
            flex-wrap: wrap;
            margin-top: 20px;
            align-items: center;

            @media (min-width: 320px) and (max-width: 1024px) {
                margin-top: 10px;
            }

            li {
                width: 15%;

                span {
                    text-align: center;
                    display: flex;
                    color: $whitecolor;
                    height: 31px;
                    align-items: center;
                    justify-content: center;
                    width: 43px;
                    font: $BasefontSize;
                    border-radius: 2px;
                }


                &:nth-of-type(2n) {
                    width: 77%;
                    margin-left: 8%;
                    font: $headingFontSize;
                    letter-spacing: 0px;
                    color: $base-color;
                    opacity: 1;


                }
            }

        }

        hr {
            border: 1px solid #000000;
            opacity: 0.12;
            width: 100%;
            margin: 25px 0px;

            @media (min-width: 320px) and (max-width: 1024px) {
                margin: 15px 0px;
            }

        }
    }

    .my-calendar {
        background-color: $whitecolor;
        padding: 20px;
        box-shadow: $boxShadow;
        border-radius: 8px;
        font-family: roboto;
        height: 530px;
        position: relative;

        .fc-view-harness {
            height: 440px !important;
            // .fc-scrollgrid-sync-table{
            //     height: 410px !important;
            // }
        }

        .fc-scrollgrid {
            border: none;

            th {
                border: none;
                padding: 5px 0px 5px 0px;
                text-align: center;
                font: normal normal 600 15px/21px Roboto;
                letter-spacing: 0px;
                color: $base-color;
                text-transform: uppercase;
                opacity: 1;
                border-top: 1px solid #0000001f;
            }

            td {
                border: 1px solid #0000001f;
                font: $headingFontSize;
                letter-spacing: 0px;
                color: $base-color;
            }

        }

        .fc-toolbar {
            align-items: center;
            display: flex;
            justify-content: left;
        }

        .RosterDate {
            background-color: #fff7c994;
        }

        .disableDate {
            font-weight: normal !important;
        }

        .fc-toolbar-title {
            font: normal normal 600 21px/28px Roboto;
            letter-spacing: 0px;
            color: $base-color;
            margin-right: 15px;
        }

        .fc-button-group {
            .fc-button {
                background-color: transparent;
                color: $base-color;
                border-radius: 40px;
                padding: 0px;
                width: 25px;
                height: 25px;
                font-size: 12px;
                margin-right: 12px;
                display: flex;
                justify-content: center;
                align-items: center;
                opacity: 0.8;
            }

        }

        .fc-day-today {
            background-color: #f7676733;
        }

        .fc-scrollgrid-sync-table {
            border-left: 1px solid #0000001f;
            border-top: 1px solid #0000001f;
        }

        .noSlot {
            font: $headingFontSize;
            letter-spacing: 0px;
            color: $base-color;
            opacity: 1;
            position: absolute;
            right: 20px;

            span {
                background-color: #F76767;
                height: 24px;
                width: 24px;
                display: inline-block;
                opacity: 0.3;
                position: relative;
                right: 10px;
                top: 6px;
            }
        }
    }

    .LeaveStatus {
        box-shadow: 0px 3px 12px #0065FF29;
        border-radius: 8px;

        th {
            background-color: #FAFCFF;
            text-align: center;
            font: $BasefontSize;
            letter-spacing: 0px;
            color: $base-color;
            border-radius: 8px 8px 0px 0px;
            opacity: 1;
            padding: 14px;
            border: none;
        }

        td {
            border: none;
            text-align: center;
            font: $BasefontSize;
            letter-spacing: 0px;
            color: $base-color;
            padding: 14px;
            font-weight: normal;

            &:last-child {
                font-weight: 600;
            }
        }

        tr {
            &:nth-child(odd) {
                background-color: #E2EDFF;
            }
        }

    }

    .Withdrawalbtn {
        background-color: #CA2626;
        padding: 5px 10px;
        cursor: pointer;
        color: #FFF;
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        border-radius: 15px;
        border: none;

    }
}

.ErrorMsgPopup {
    font-weight: 600;
    font-family: 'Roboto';
    font-size: 15px;
    padding-bottom: 20px !important;
    color: #b72424;
    background-color: #f6dbdb !important;


    @media (min-width: 320px) and (max-width: 1024px) {
        width: 94% !important;
        margin: auto !important;
        padding: 40px 14px 20px !important;
        text-align: center;
        top: 50% !important;
        transform: translate(-50%, -50%) !important;
        bottom: auto !important;
        left: 50% !important;
        border-radius: 8px !important;

        .closebtn {
            position: absolute;
            top: 10px;
            right: 10px;
        }


    }

}

.NoteMsg {
    color: #b72424;
    font-weight: 600;
    font-size: 13px;
    font-family: 'Roboto';
}

.noSlot {
    color: #b72424;
    font-weight: 600;
    font-size: 16px;
    font-family: 'Roboto';
    list-style-type: none;
    margin-top: 10px;
}



.LeaveApplicationPopup {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 463px;
    background-color: $whitecolor;
    border-radius: 8px;
    padding: 20px 25px 30px 25px;
    outline: none;
    // height: 400px;

    @media(min-width: 320px) and (max-width: 1024px) {
        top: auto;
        bottom: 0;
        transform: none;
        left: 0;
        right: 0;
        border-radius: 32px 32px 0px 0px;
        padding: 24px 15px;
        width: 100%;
    }

    header {
        @include DisplayFlex;

        h2 {
            font: normal normal 600 21px/28px Roboto;
            letter-spacing: 0px;
            color: $base-color;
            display: flex;
            align-items: center;

            svg {
                margin-right: 7px;
                color: $base-color;
            }
        }

        button {
            background: #777DA7;
            border-radius: 24px;
            font: $peraFontsize;
            letter-spacing: 0px;
            color: $whitecolor;
            text-transform: uppercase;
            opacity: 1;
            border: none;
            outline: none;
            padding: 7px 15px;
            cursor: pointer;
        }

        .closebtn {
            color: #000000;
            opacity: 0.6;
            cursor: pointer;
        }
    }

    label {
        display: block;
        text-align: left;
        font: $headingFontSize;
        letter-spacing: 0px;
        color: $base-color;
        opacity: 1;
        margin: 25px 0px 15px;
    }



    .TypeOfLeaveSeclect {
        width: 100%;
        border: 1px solid #2538581F;
        border-radius: 8px;
        font: $BasefontSize;
        letter-spacing: 0px;
        color: #253858;
        outline: none;
        display: flex;
        align-items: center;
        height: 48px;

        p {
            width: 25px;
            margin: 0px;
            border-radius: 2px;
            height: 18px;
            font-size: 12px;
            text-align: center;
            color: $whitecolor;
            margin-right: 10px;

            svg {
                width: 14px;
                height: 14px;
                position: relative;
                top: 2px;
            }
        }

        .MuiSelect-select {
            display: flex;
            padding: 7px;
            align-items: center;
        }
    }

    .calendarheader {
        @include DisplayFlex;
        margin-top: 20px;
        position: relative;
        top: 9px;

        p {
            text-align: left;
            font: $BasefontSize;
            letter-spacing: 0px;
            color: $base-color;
            opacity: 0.6;
        }

        .arrow {
            display: flex;
            align-items: center;
            text-align: right;
            font: $peraFontsize;
            letter-spacing: 0px;
            color: $base-color;
            opacity: 1;

            span {
                height: 12px;
                width: 12px;
                background-color: #f76767;
                border-radius: 10px;
                margin-right: 6px;
            }

            svg {
                border: 1px solid #000;
                border-radius: 13px;
                margin: 0px 5px 0px 17px;
                cursor: pointer;
            }
        }
    }

    .weekdays {
        margin: 0;
        padding: 0px;
        @include DisplayFlex;

        li {
            display: inline-flex;
            width: 40px;
            text-align: center;
            font: $headingFontSize;
            letter-spacing: 0px;
            color: $base-color;
            text-transform: uppercase;
            opacity: 0.6;
            @include DisplayFlex;
        }
    }

    .days {
        padding: 12px 0 10px;
        margin: 0;
        @include DisplayFlex;

        li {
            list-style-type: none;
            background: #ffffff;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            width: 40px;
            height: 40px;
            text-align: center;
            font: $headingFontSize;
            letter-spacing: 0px;
            color: #000;
            opacity: 1;
            border-radius: 50%;
            margin-bottom: 5px;
            cursor: pointer;
            position: relative;

            span {
                position: absolute;
                top: 44px;
                font-size: 15px;
                color: #000000;
            }
        }

        .white {
            color: #fff;
        }

    }

    .monthView {
        list-style-type: none;
        @include DisplayFlex;
        margin: 8px 2px;

        li {
            font: $BasefontSize;
            letter-spacing: 0px;
            color: $base-color;
            text-transform: uppercase;
            opacity: 0.6;

            &:last-child {
                // text-decoration: underline;
                font: $BasefontSize;
                letter-spacing: 0px;
                color: #0065FF;
                opacity: 1;
                text-transform: capitalize;
                cursor: pointer;
            }
        }
    }

    .ApplyLeaveBtn {
        background: #0065FF;
        border-radius: 4px;
        border: none;
        text-align: center;
        font: $BasefontSize;
        letter-spacing: 0px;
        color: $whitecolor;
        text-transform: uppercase;
        opacity: 1;
        padding: 12px 45px;
        margin-top: 3em;
        cursor: pointer;
    }

    hr {
        margin: 18px 2px;
        height: 1px;
        border: none;
        background-color: #000;
        opacity: 0.12;
    }

    .LeaveAppliedSucess {
        text-align: center;

        svg {
            color: #0065FF;
            font-size: 63px;
        }

        h3 {
            font: normal normal 600 21px/28px Roboto;
            letter-spacing: 0px;
            color: $base-color;
            opacity: 1;
            margin-top: 10px;
        }

        ul {
            display: flex;
            justify-content: space-around;
            background: #F7F7F7;
            border-radius: 12px;
            list-style-type: none;
            flex-wrap: wrap;
            padding: 10px 0px;
            width: 256px;
            margin: 20px auto;

            li {
                width: 49%;
                font: $BasefontSize;
                letter-spacing: 0px;
                color: $base-color;
                opacity: 0.6;
                text-align: left;
                padding: 7px 18px;

                &:nth-child(even) {
                    opacity: 1;
                }
            }

        }

        button {
            background: #0065FF;
            border-radius: 4px;
            outline: none;
            border: none;
            color: $whitecolor;
            text-align: center;
            font: $BasefontSize;
            padding: 10px;
            width: 220px;
            cursor: pointer;
            margin-top: 20px;
        }

    }
}

.WeeklyOff {
    background: #397444 !important;
    color: #fff !important;
}

.SickLeave {
    background: #6865AC !important;
    color: #fff !important;
}

.CasualLeave {
    background: #4978AA !important;
    ;
    color: #fff !important;
}

.EarnedLeave {
    background: #18837C !important;
    ;
    color: #fff !important;
}

.EarnedWeekOff {
    background: #8E884E !important;
    ;
    color: #fff !important;
}

.leaveTakenandnoslots {
    background: linear-gradient(90deg, #f76767 51%, #0065FF 38%) !important;
    color: #fff !important;
}

.leaveTaken {
    background: #0065FF !important;
    color: #fff !important;
}

.noSlotAvailable {
    background: #f76767 !important;
    color: #fff !important;
}

.leaveTaken {
    background: #0065FF !important;
    color: #fff !important;
}

.MuiMenuItem-root {
    font: $BasefontSize !important;
    letter-spacing: 0px;
    color: $base-color !important;

    p {
        width: 25px;
        color: $whitecolor;
        border-radius: 2px;
        font-size: 12px;
        height: 20px;
        text-align: center;
        margin-right: 12px;
        line-height: 20px;

        svg {
            width: 14px;
            height: 14px;
            position: relative;
            top: 3px;
        }
    }
}

.Backdrop {
    // width: 100%;
    // height: 100vh;
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.7;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    position: fixed;
    z-index: 9;
}

.flexbox {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    height: 100%;
    align-items: center;
}

.bt-spinner {
    margin-left: 50vw;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: transparent;
    border: 4px solid #222;
    border-top-color: #009688;
    -webkit-animation: 1s spin linear infinite;
    animation: 1s spin linear infinite;
}

.low-opacity-event {
    opacity: 0.5 !important;
}

.center-text {
    text-align: center !important;
    display: flex;
    justify-content: center;
    align-items: center;
}

@-webkit-keyframes spin {
    -webkit-from {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    -webkit-to {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-webkit-keyframes spin {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes spin {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}



/*----------------media-css-----------*/
@media (min-width: 320px) and (max-width: 1024px) {

    .logo {
        display: block;
        margin: 0px auto 8px;
        text-align: center;

        img {
            width: 90px;
        }

    }

    .MuiContainer-root {
        padding-right: 0 !important;
        padding-left: 0 !important;
    }


}

