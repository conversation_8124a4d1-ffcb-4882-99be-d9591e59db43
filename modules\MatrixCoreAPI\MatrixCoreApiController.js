const tblList = require("../constants");
const methods = require("./MatrixCoreApiMethods");
let fs = require('fs');
const path = require("path");
const moment = require("moment");



async function UpdateFOSCityAssignment(req, res) {
    try {
       await methods.UpdateFOSCityAssignment(req, res);
    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}



module.exports = {
    UpdateFOSCityAssignment: UpdateFOSCityAssignment,
    
};