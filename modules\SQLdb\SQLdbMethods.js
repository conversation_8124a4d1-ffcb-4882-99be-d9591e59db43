
const sqlHelper = require("../../Libs/sqlHelper");
const Utility = require("../../Libs/Utility");
const UserStatsToManager = require("../SQLdb/UserStatsToManager");
const xlsxFile = require('read-excel-file/node');
var fs = require('fs');
var jsonxml = require('jsontoxml');
const bodyParser = require("body-parser");
const crypto = require('crypto');
const encryptionType = 'aes-256-cbc';
const encryptionEncoding = 'base64';
const bufferEncryption = 'utf-8';
const moment = require("moment");
let express = require('express');
var path = require('path');
const uuid = require('uuid');
const AWS = require('aws-sdk');
//AWS.config.update(conf.s3);

const { ExcelFileColumns } = require("../ExcelFileColumns");

var cache = require('memory-cache');
var _ = require('underscore');
const { bulkInsertBooking } = require("../Mongodb/Methods");
const conf = require("../../env_config");
const { GetCourseQuizDetails } = require('./PBSchoolSQLdbMethods');

exports.FindRoot = function (req, res) {
    let result = false;
    console.log(req.query.root);
    switch (req.query.root) {
        case "Hierarchy":
            FetchManagerHierarchy(req, res);
            result = true;
            break;
        case "UserDetails":
            GetUserDetails(req, res);
            result = true;
            break;
        case "IVRActivityReport":
            IVRActivityReport(req, res);
            result = true;
            break;
        case "UserStatsToManager":
            UserStatsToManager.UserStatsToManager(req, res);
            result = true;
            break;
        case "unregistered":
            unregistered(req, res);
            result = true;
            break;
        // case "AgentLeadsNotCalled":
        //     AgentLeadsNotCalled(req, res);
        //     result = true;
        //     break;
        case "InboundCallData":
            InboundCallData(req, res);
            result = true;
            break;
        // case "AesDecryption":
        //     AesDecryption(req, res);
        //     result = true;
        //     break;
        case "GetAgentsUnderManagers":
            GetAgentsUnderManagers(req, res);
            result = true;
            break;
        case "GetSurveyDetails":
            GetSurveyDetails(req, res);
            result = true;
            break;
        case "GetQuizDetails":
            GetQuizDetails(req, res);
            result = true;
            break;
        case "FetchMenuMaster":
            FetchMenuMaster(req, res);
            result = true;
            break;
        case "FetchAsteriskUrl":
            FetchAsteriskUrl(req, res);
            result = true;
            break;
        case "FetchCallingCompany":
            FetchCallingCompany(req, res);
            result = true;
            break;
        case "GetReviewQuizDetails":
            GetReviewQuizDetails(req, res);
            result = true;
            break;  
        case "GetAnswerQuizDetails":
            GetAnswerQuizDetails(req, res);
            result = true;
            break;
        case "GetCourseQuizDetails":
            GetCourseQuizDetails(req, res);
            result = true;
            break;
        case "getRosterData":
            getRosterData(req,res);
            result = true;
            break;
        case "getEmployeeData":
            getEmployeeData(req,res);
            result = true;
            break;
        case "GetHolidays":
            GetHolidays(req,res);
            result = true;
            break;
    }

    return result;
}

async function FetchAsteriskUrl(req, res) {
    try {
        let result = await Utility.API_GET(conf.dialerApiUrlV2 + "dialer/getDialerData");

        let Apiparams = [];

        let json = result.data.servers
        for (let index = 0; index < json.length; index++) {
            const element = json[index];
            Apiparams.push({ Id: element.AsteriskIp, Display: element.AsteriskUrl, ProductID: element.productIds });
        }
        //console.log('apiparams',Apiparams);

        res.send({
            status: 200,
            data: [Apiparams],
            message: 'success'
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}


async function FetchCallingCompany(req, res) {
    try {
        console.log(conf.dialerApiUrlV2 + "dialer/getDialerData")

        let result = await Utility.API_GET(conf.dialerApiUrlV2 + "dialer/getDialerData");
        console.log('asteriskurl', result)
        let Apiparams = [];
        let json = result.data.callingCompanies
        console.log("callingcom", json)
        for (let index = 0; index < json.length; index++) {
            Apiparams.push({ Id: json[index], Display: json[index] });
        }

        console.log('apiparams', Apiparams);
        res.send({
            status: 200,
            data: [Apiparams],
            message: 'success'
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}
async function GetUserDetails(req, res) {
    try {
        if (isNaN(req.query.UserID)) {
            res.send({
                status: 200,
                data: []
            });
            return;
        }

        let query = `SELECT * from PBT.UserMaster where EmployeeId = @EmployeeId`
        let sqlparam = [];
        sqlparam.push({ key: "EmployeeId", value: req.query.EmployeeId });
        let result = await sqlHelper.sqlquery("R", query, sqlparam);
        res.send({
            status: 200,
            data: result.recordset
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}

async function FetchManagerHierarchy(req, res) {
    let result = {};
    if (req.query.ManagerId == 75) {
        result = cache.get('jerry');
        if (result === null) {
            result = await GetTree(req.query.ManagerId);
            cache.put('jerry', result, (60 * 60 * 1000), function (key, value) {
                console.log(key + ' did ' + "EMPTY");
            });
        }
    }
    else {
        result = await GetTree(req.query.ManagerId);
    }


    res.send({
        status: 200,
        data: result
    });
}

async function GetTree(ManagerId) {
    let result = await GetUnderManagers(ManagerId);
    if (result) {
        for (let index = 0; index < result.length; index++) {
            const element = result[index];
            let childrens = await GetTree(element.UserID);
            if (childrens && childrens.length > 0) {
                element["children"] = childrens;
            }

        }
    }

    return result;
}



async function GetUnderManagers(ManagerId) {
    try {

        let query = `SELECT distinct UD.UserID, 12 AS RoleId, UD.EmployeeId, UD.UserName, UD.ManagerId, 'Supervisor' AS RoleName
                from CRM.UserGroupRoleMapNew UGRMN (NOLOCK)
                INNER JOIN CRM.UserDetails UD (NOLOCK) ON UD.UserID=UGRMN.UserID AND UD.IsActive=1 
                INNER JOIN  CRM.RoleSuperMaster RSM (NOLOCK) ON UGRMN.RoleSuperId=RSM.RoleSuperId    
                INNER JOIN  CRM.RoleMaster RM (NOLOCK) ON RSM.RoleId=RM.RoleId    
                INNER JOIN CRM.PermissionDetails PD (NOLOCK) ON UGRMN.UserId=PD.UserId  
                WHERE  RSM.RoleCategoryID IN(2) AND UD.IsActive=1 AND RSM.RoleId NOT IN(13) and UD.ManagerId = @ManagerId`
        console.log(query);
        let sqlparam = [];
        sqlparam.push({ key: "ManagerId", value: ManagerId });

        let result = await sqlHelper.sqlquery("R", query, sqlparam);
        let res = result.recordset;
        return res;
    }
    catch (e) {
        console.log(e);
    }
    finally {

    }
}


async function IVRActivityReport(req, res) {
    try {

        let query = `SELECT
            DISTINCT n.callid AS utcallid,
            CONVERT(varchar, n.calltime,120) As calltime,
            C.ProductName,
            n.leadid,
            ISNULL(t.isAlreadyWhatsAppOptIn,'NULL') AS isAlreadyWhatsAppOptIn,
            ISNULL(t.isWhatsAppOptIn,'NULL') AS isWhatsAppOpted,            
            t.isAlreadyHaveOpenTicket AS OpenTicketExist,
            t.isTatBusted AS TicketTatBusted,
            t.ticketStatusCustInput,
            t.bookingGreaterThenThirtyDay AS BookingGreaterThen30Days,
            t.isPolicyIssued,
            t.ivrOption,
            t.softCopyOption,
            t.isSatisifyWithIvrOption,
            t.playnstp,
            t.playnstpid,
            t.playnstpOption,
            C.AssignedToAgent AS AssignedAgent,
            n.CallStatus AS AssignedAgentCallStatus,
            n.newcallid,
            n.language,
            n.QName,
            --n.agentname AS GroupAgent,
            CONVERT(varchar, n.Qentertime,120) As Qentertime,
            CONVERT(varchar, n.answertime,120) As answertime,
            CONVERT(varchar, n.hanguptime,120) As hanguptime,
            n.callstatus AS GroupCallStatus,
            --n.callendnode,
            n.callendreason,
            --n.ivrcalltype,
            --CASE WHEN n.duration='switchToService' THEN 'Yes' ELSE 'NO' END AS isSalesToService,
            C.BookingNo, C.PolicyNo, C.SupplierName, C.policyType, 
            CONVERT(varchar, C.bookingCreatedDate,120) As bookingCreatedDate,
            CONVERT(varchar, C.bookingStampingDate,120) As bookingStampingDate,
            C.registrationNumber,
            CASE WHEN C.isWhatsAppOptIn=1 THEN 'YES' ELSE 'NO' END AS isWhatsAppOptIn,
            CASE WHEN C.isStp=1 THEN 'STP' ELSE 'NSTP' END AS isStp,
            CASE WHEN C.isSoftCopyAvailable=1 THEN 'YES' ELSE 'NO' END AS isSoftCopyAvailable
        FROM MTX.IBCallData n
            INNER JOIN MTX.CustomerINFO C ON n.callid=C.UniqueueID AND n.leadid=C.LeadID AND C.IsBooking=1 AND C.productID IN (117,114)
            LEFT JOIN MTX.inboundCallTracking t ON n.callid=t.unique_id
        WHERE       n.calltime BETWEEN @startdate  AND @enddate AND CAST(n.calltime AS TIME) BETWEEN '10:00'  AND '19:00'
            AND ISNULL(n.QName,'') <> 'unregistered'`;

        if (req.query.ProductId > 0) {
            query = query + `AND C.productID = @productID`
        }

        let sqlparam = [];
        sqlparam.push({ key: "startdate", value: req.query.startdate });
        sqlparam.push({ key: "enddate", value: req.query.enddate });
        sqlparam.push({ key: "productID", value: req.query.productID });

        console.log(query);
        let result = await sqlHelper.sqlquery("R", query, sqlparam);
        res.send({
            status: 200,
            data: result.recordsets
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}


async function unregistered(req, res) {
    try {

        let query = `SELECT DATEPART(HOUR,n.calltime) AS hour, 
        CONVERT(varchar, n.calltime,120) As calltime,        
        ISNULL(n.CallID,0) callid, 
        ISNULL(n.AgentID,'') agentname, 
        ISNULL(CONVERT(varchar, n.Qentertime,120),'') AS entertime, 
        ISNULL(CONVERT(varchar, n.answertime,120),'') AS answertime, 
        ISNULL(CONVERT(varchar, n.hanguptime,120),'') AS hanguptime, 
        ISNULL(n.callstatus,'') AS callstatus, 
        ISNULL(t.callid,'') AS transferedCallid, 
        ISNULL(PD.ProductName,'') AS product, 
        CAST(ISNULL(t.leadid,'') AS CHAR( 50 ) ) AS leadid, 
        ISNULL(t.QName,'') AS mainQueue, 
        ISNULL(t.AgentID,'') AS mainQueueAgent, 
        ISNULL(CONVERT(varchar, t.Qentertime,120),'') AS mainQueueEnterTime, 
        ISNULL(CONVERT(varchar, t.answertime,120),'') AS mainQueueAnswerTime, 
        ISNULL(CONVERT(varchar, t.hanguptime,120),'') AS mainQueueHangupTime, 
        ISNULL(t.callstatus,'') AS mainQueueCallStatus 
        FROM		MTX.IBCallData n 
        LEFT JOIN	MTX.IBCallData t 
        LEFT JOIN   CRM.ProductDetails PD (NOLOCK) ON t.LeadID=PD.LeadID
        ON			n.callid=t.orig_callid                 
        WHERE       n.calltime BETWEEN @startdate  AND @enddate 
        AND			DATEPART(HOUR,n.CallTime) BETWEEN 10 AND 19 
        AND			n.QName = 'unregistered'`;

        let sqlparam = [];
        sqlparam.push({ key: "startdate", value: req.query.startdate });
        sqlparam.push({ key: "enddate", value: req.query.enddate });

        console.log(query);
        let result = await sqlHelper.sqlquery("R", query, sqlparam);
        res.send({
            status: 200,
            data: result.recordsets
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}


async function InboundCallData(req, res) {
    try {
        let sqlparam = [];
        sqlparam.push({ key: "FromDate", value: req.query.startdate });
        sqlparam.push({ key: "ToDate", value: req.query.enddate });
        sqlparam.push({ key: "QName", value: req.query.queues });
        sqlparam.push({ key: "ProductID", value: req.query.ProductID });
        sqlparam.push({ key: "ivrType", value: req.query.ivrType });
        console.log('inboundcalldata,sqlparam', sqlparam);

        let result = await sqlHelper.sqlProcedure("R", "[MTX].[GetInboundCallData]", sqlparam);
        console.log(result);
        res.send({
            status: 200,
            data: result.recordsets
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}

async function AesDecryption(req, res) {
    try {
        this.AesKey = 'PB9MUI18RLIghJ2NA6TH0JI73U1NaWQA';
        this.AesIV = 'ABCDEFGHIJKLMNOP';
        if (req.query.Source && req.query.Source == 'Fos') {
            this.AesKey = 'Aqn5RCrH7vDIjdLRDZG6R4sFrTB5Ax7g';
            this.AesIV = 'bO4ujK1eepa0BT95';
        }
        const buff = Buffer.from(req.query.uid, encryptionEncoding);
        const key = Buffer.from(this.AesKey, bufferEncryption);
        const iv = Buffer.from(this.AesIV, bufferEncryption);
        const decipher = crypto.createDecipheriv(encryptionType, key, iv);
        const deciphered = decipher.update(buff) + decipher.final();
        //return JSON.parse(deciphered);
        res.send({
            status: 200,
            data: JSON.parse(deciphered)
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}

async function UploadIncentiveData(xml, typeId, userId, validfrom, productId, awsFileName, awsFilePath) {
    try {
        //console.log(xml,typeId, userId, validfrom, productId, awsFileName, awsFilePath);
        let sqlparam = [];
        sqlparam.push({ key: "ProductID", value: productId });
        sqlparam.push({ key: "UserId", value: userId });
        sqlparam.push({ key: "Type", value: typeId });
        sqlparam.push({ key: "filename", value: awsFileName });
        sqlparam.push({ key: "filePath", value: awsFilePath });
        if (typeId == 15 || typeId == 16) {
            sqlparam.push({ key: "xml", value: '' });
        } else {
            sqlparam.push({ key: "xml", value: xml });
        }
        sqlparam.push({ key: "message", value: '' });
        if (!validfrom == '') {
            sqlparam.push({ key: "ValidFrom", value: validfrom });
        }
        return await sqlHelper.sqlProcedure("L", "[Enc].[UploadIncentiveData]", sqlparam);
    }
    catch (e) {
        console.log(e);
        return e;

    }
    finally {

    }
}


exports.UploadIncentiveFile = async function (req, res) {
    try {
        var appDir = path.dirname(require.main.filename);
        var awsFilePath = '';
        var awsFileName = '';
        var TypeIsXml = JSON.parse(req.body.TypeIsXml);
        console.log('appdir', appDir, req.body, req.files);
        if (!req.files) {
            return res.status(500).send({ msg: "file is not found" })
        }
        // accessing the file
        const myFile = req.files.myFile;
        const today = moment(Date.now()).format('YYYY-MM-DD');
        var dir = appDir + "\\client\\public\\SampleExcelfiles\\" + today;
        fs.existsSync(dir) || fs.mkdirSync(dir);
        var myFileName = myFile.name.split('.').join('-' + Date.now() + '.');
        console.log("timestampfilename", myFileName);
        fs.copyFile(`${myFile.tempFilePath}`, process.cwd() + "\\client\\public\\SampleExcelfiles\\" + today + "\\" + myFileName, function (err) {
            if (err) {
                throw err
            } else {
                console.log("Successfully copied and moved the file!")
                // Upload File to AWS
                var filePath = appDir + "\\client\\public\\SampleExcelfiles\\" + today + "\\" + myFileName;
                console.log('filepath', filePath, "filename", myFileName);
                let timestamp = Date.now();
                let filename = "Incentive/" + today + "/" + myFileName;

                if (req.body.TypeId && (req.body.TypeId == 17 || req.body.TypeId == 18 || req.body.TypeId == 20)) {
                    var batchId = uuid.v1().toUpperCase();

                    xlsxFile(fs.createReadStream(`${filePath}`)).then(async (rows) => {
                        for (var col = 0; col < rows[0].length; col++) {
                            if (ExcelFileColumns.UploadIncentiveData[req.body.TypeId].indexOf(rows[0][col]) === -1) {
                                return res.send({
                                    status: 403,
                                    message: "Invalid column(s) used in sheet, please refer sample file"
                                });
                            }
                        }

                        var _result = [];
                        //var JsonExcel = {};
                        if (req.body.TypeId == 20) {
                            let sqlparams = [];
                            sqlparams.push({ key: "ProductId", value: req.body.ProductId });
                            sqlHelper.sqlProcedure("L", "mtx.ResetAgentProcessDetails", sqlparams);
                        }
                        for (var i = 1; i in rows; i++) {
                            var obj = {};
                            for (j in rows[i]) {
                                obj[rows[0][j]] = rows[i][j];
                            }

                            //var innerObj = {};
                            obj['Status'] = await uploadFileReadData(obj, req.body, batchId);
                            //console.log(obj);
                            _result.push(obj);
                        }
                        res.send({
                            status: 200,
                            data: _result
                        });
                    });
                } else {
                    let awsUploadResponse = uploadAWSfiles(filePath, filename, 'asterisk-log');
                    console.log("awsresp", awsUploadResponse);
                    awsUploadResponse.then(function (resultaws) {
                        console.log('resultedkey', resultaws.key);
                        var awsFileName = myFileName;
                        var awsFilePath = 'https://' + req.headers.host + '/api/v1/db/getawsfile?key=' + resultaws.key + '&bucket=asterisk-log';

                        if (req.body.TypeId == 15) {
                            xlsxFile(fs.createReadStream(`${filePath}`)).then(async (rows) => {
                                for (var col = 0; col < rows[0].length; col++) {
                                    if (ExcelFileColumns.UploadIncentiveData[req.body.TypeId].indexOf(rows[0][col]) === -1) {
                                        return res.send({
                                            status: 403,
                                            message: "Invalid column(s) used in sheet, please refer sample file"
                                        });
                                    }
                                }

                                var _result = [];
                                for (var i = 1; i in rows; i++) {
                                    var obj = {};
                                    for (j in rows[i]) {
                                        obj[rows[0][j]] = rows[i][j];
                                    }
                                    _result.push(obj);
                                }
                                await bulkInsertBooking(_result);
                            });
                        }

                        // Read Excel file to xml
                        let xml = ExcelToXml(myFile, TypeIsXml);
                        // console.log("xml",xml);
                        xml.then(function (resultedxml) {
                            console.log('xmlrsult', resultedxml);
                            let response = UploadIncentiveData(resultedxml, req.body.TypeId, req.body.UserId, req.body.ValidFrom, req.body.ProductId, awsFileName, awsFilePath);
                            response.then(function (result) {
                                console.log("recordset", result.recordset) // "Some User token"
                                res.send({
                                    status: 200,
                                    data: result.recordset
                                });
                            })
                        })

                    })
                }


            }
        })


        // xlsxFile(fs.createReadStream(`${myFile.tempFilePath}`)).then((rows) => {console.log(rows);
        //     var _result = [];
        //     //let response = [];
        //     var JsonExcel = {};
        //     for (var i = 1; i in rows; i++) {
        //         var obj = {};
        //         for (j in rows[i]) {
        //             obj[rows[0][j]] = rows[i][j];
        //         }
        //         var innerObj = {};
        //         innerObj['Data'] = obj;
        //         _result.push(innerObj);
        //     }
        //     JsonExcel['xml'] = _result;
        //     console.log("result", JsonExcel);

        //     var xml = jsonxml(JsonExcel);
        //     console.log(xml);
        // let response = UploadIncentiveData(xml, req.body.TypeId, req.body.UserId, req.body.ValidFrom, req.body.ProductId, awsFileName, awsFilePath);
        // response.then(function (result) {
        //     console.log(result.recordset) // "Some User token"
        //     res.send({
        //         status: 200,
        //         data: result.recordset
        //     });
        // })

        // })
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}

/**
 * Call SP and set parameters
 * @param {*} xlsData 
 * @param {*} fixedParams 
 * @param {*} batchId 
 * @returns 
 */
async function uploadFileReadData(xlsData, fixedParams, batchId) {
    var response = '';
    try {
        let sqlparam = [];
        let spName = '';
        if (fixedParams.TypeId == 17 || fixedParams.TypeId == 18) {
            sqlparam.push({ key: "ProductID", value: fixedParams.ProductId });
            sqlparam.push({ key: "UploadedBy", value: fixedParams.UserId });
            sqlparam.push({ key: "BatchId", value: batchId });
            //sqlparam.push({ key: "message", value: '' });

            if (!fixedParams.ValidFrom == '') {
                sqlparam.push({ key: "ValidFrom", value: fixedParams.ValidFrom });
            }
            sqlparam.push({ key: "SuperGroupId", value: xlsData.SuperGroupId });
        }

        if (fixedParams.TypeId == 17) {
            sqlparam.push({ key: "SuperGroupName", value: xlsData.SuperGroupName ? (xlsData.SuperGroupName.trim()).substring(0, 100) : '' });
            sqlparam.push({ key: "GroupType", value: xlsData.Type });
            sqlparam.push({ key: "MinRange", value: xlsData.LowerRange });
            sqlparam.push({ key: "MaxRange", value: xlsData.UpperRange });
            sqlparam.push({ key: "slabWeightage", value: xlsData.Weightage });
            sqlparam.push({ key: "SubCategoryID", value: xlsData.SubCategoryID });
            spName = "[Enc].[UploadAgentSlabs]";
        } else if (fixedParams.TypeId == 18) {
            sqlparam.push({ key: "FloorProcess", value: xlsData.FloorProcess ? (xlsData.FloorProcess.trim()).substring(0, 100) : '' });
            spName = "[Enc].[UploadFloorProcessMapping]";
        } else if (fixedParams.TypeId == 20) {//20 for AgentProcessDetails
            console.log(xlsData);
            if (fixedParams.ProductId == xlsData.ProductId) {
                sqlparam.push({ key: "EmployeeId", value: xlsData.EmployeeId });
                //sqlparam.push({key: "Tenure", value : '2020-10-01'});
                sqlparam.push({ key: "FloorProcess", value: xlsData.FloorProcess });
                sqlparam.push({ key: "TLEmployeeId", value: xlsData.TLEmployeeId });
                sqlparam.push({ key: "AMEmployeeId", value: xlsData.AMEmployeeId });
                sqlparam.push({ key: "ManagerEmployeeId", value: xlsData.ManagerEmployeeId });
                sqlparam.push({ key: "TargetAPE", value: xlsData.TargetAPE });
                sqlparam.push({ key: "TargetBookings", value: xlsData.TargetBookings });
                sqlparam.push({ key: "ProductId", value: fixedParams.ProductId });
                sqlparam.push({ key: "Location", value: xlsData.Location });
                sqlparam.push({ key: "CreatedBy", value: fixedParams.UserId });
                sqlparam.push({ key: "RoleType", value: xlsData.RoleType });
                spName = "mtx.InsertAgentProcessDetails"
            }
        }
        console.log(sqlparam)
        result = await sqlHelper.sqlProcedure("L", spName, sqlparam);
        console.log(result)
        let status = result.recordset[0];
        response = status['message'];
    }
    catch (e) {
        response = e.getMessage();
    }
    finally {
        return response;
    }
}

async function UploadVideoFileData(body, awsFilePath, pageId) {
    try {
        console.log(body.userId, awsFilePath, pageId);
        let sqlparam = [];
        if (pageId) {
            sqlparam.push({ key: "pageId", value: pageId });
        } else {
            sqlparam.push({ key: "ProductId", value: body.ProductId });
            sqlparam.push({ key: "UserRoleID", value: body.roleId });
            sqlparam.push({ key: "UserGroupID", value: body.groupId });
        }
        sqlparam.push({ key: "userId", value: body.userId });
        sqlparam.push({ key: "IsRequired", value: body.IsRequired });
        sqlparam.push({ key: "IsActive", value: body.IsActive });
        sqlparam.push({ key: "PageUrl", value: awsFilePath });
        sqlparam.push({ key: "Link", value: body.Link });
        sqlparam.push({ key: "SurveyName", value: body.SurveyName });
        sqlparam.push({ key: "StartDate", value: body.StartDate });
        sqlparam.push({ key: "Endtime", value: body.Endtime });
        sqlparam.push({ key: "ContentType", value: body.ContentType });

        let result = await sqlHelper.sqlProcedure("L", "[MTX].[InsertPreLoginPageSurveyByPanel]", sqlparam);
        console.log(result);
        return result;

    }
    catch (e) {
        console.log(e);
        return e;

    }
    finally {

    }
}


exports.UploadVideoFile = async function (req, res) {
    try {
        console.log(req.body);
        var appDir = path.dirname(require.main.filename);
        var awsFilePath = '';
        var awsFileName = '';
        var pageId = '';
        if (req.body.pageId) {
            pageId = req.body.pageId;
        }
        if (req.files) {
            console.log('appdir', appDir, req.body, req.files);
            if (!req.files) {
                return res.status(500).send({ msg: "file is not found" })
            }

            // accessing the file
            const myFile = req.files.myFile;
            const today = moment(Date.now()).format('YYYY-MM-DD');
            var myFileName = myFile.name.split('.').join('-' + Date.now() + '.');//myFile.name;
            console.log("timestampfilename", myFileName);
            //let CopyFileTodayRes = CopyFileToday(myFile);
            //CopyFileTodayRes.then(function (resultedRes) {
            //console.log(resultedRes);
            //if (resultedRes) {           
            // Upload File to AWS
            var filePath = appDir + "\\public\\SampleExcelfiles\\" + today + "\\" + myFileName;
            console.log('filepath', filePath, "filename", myFileName);
            let timestamp = Date.now();
            let filename = "PreLogin/" + today + "/" + myFileName;
            //let awsUploadResponse = uploadAWSfiles(filePath, filename, 'policystatic.policybazaar.com');
            let awsUploadResponse = uploadAWSfiles(`${myFile.tempFilePath}`, filename, 'policystatic.policybazaar.com');

            console.log("awsresp", awsUploadResponse);
            awsUploadResponse.then(function (resultaws) {
                console.log('resultedkey', resultaws.key);
                var awsFileName = myFileName;
                var awsFilePath = 'https://' + resultaws.Bucket + '/' + resultaws.Key;

                let response = UploadVideoFileData(req.body, awsFilePath, pageId);
                response.then(function (result) {
                    console.log("recordset", result.recordset) // "Some User token"
                    res.send({
                        status: 200,
                        data: result.recordset
                    });
                })


            })
        } else {
            let response = UploadVideoFileData(req.body, awsFilePath, pageId);
            response.then(function (result) {
                console.log("recordset", result.recordset) // "Some User token"
                res.send({
                    status: 200,
                    data: result.recordset
                });
            })
        }

        //}
        //})

    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}


async function CopyFileToday(myFile) {
    try {
        var appDir = path.dirname(require.main.filename);
        const today = moment(Date.now()).format('YYYY-MM-DD');
        var dir = appDir + "\\client\\public\\SampleExcelfiles\\" + today;
        fs.existsSync(dir) || fs.mkdirSync(dir);
        let result = await fs.promises.copyFile(`${myFile.tempFilePath}`, process.cwd() + "\\client\\public\\SampleExcelfiles\\" + today + "\\" + `${myFile.name}`)
            .then(async () => {
                return true;
            })
            .catch(function (err) {
                return false;
            });
        console.log(result);
        return result;
    } catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}


async function ExcelToXml(myFile, TypeIsXml) {
    try {
        console.log("TypeIsXml", TypeIsXml);
        if (TypeIsXml == true) {
            let result = await xlsxFile(fs.createReadStream(`${myFile.tempFilePath}`)).then((rows) => {
                var _result = [];
                //let response = [];
                var JsonExcel = {};
                for (var i = 1; i in rows; i++) {
                    var obj = {};
                    for (j in rows[i]) {
                        obj[rows[0][j]] = rows[i][j];
                    }
                    var innerObj = {};
                    innerObj['Data'] = obj;
                    _result.push(innerObj);
                }
                JsonExcel['xml'] = _result;
                console.log("result", JsonExcel);

                var xml = jsonxml(JsonExcel);
                console.log(xml);
                return xml;

            })
            return result;
        } else {
            return '<xml></xml>';
        }
    } catch (e) {
        console.log(e);
        return e;
    }
    finally {

    }
}

async function uploadAWSfiles(filepath, filename, bucketname) {
    // Load the AWS SDK for Node.js
    var AWS = require('aws-sdk');
    // Set the region 
    AWS.config.update({ region: 'ap-south-1' });

    // Create S3 service object
    const s3 = new AWS.S3();

    // call S3 to retrieve upload file to specified bucket
    var uploadParams = { Bucket: bucketname, Key: '', Body: '' };
    var file = filepath;
    console.log(filepath);
    // Configure the file stream and obtain the upload parameters
    var fs = require('fs');
    var fileStream = fs.createReadStream(file);
    fileStream.on('error', function (err) {
        console.log('File Error', err);
    });
    uploadParams.Body = fileStream;
    var path = require('path');
    uploadParams.Key = filename;

    // call S3 to retrieve upload file to specified bucket
    var s3upload = s3.upload(uploadParams).promise();
    // return the `Promise`
    var result = await s3upload
        .then(function (data) {
            console.log(data);
            return data;
        })
        .catch(function (err) {
            return err.message;
        });
    // var result = await s3.upload (uploadParams, function (err, data) {
    //   if (err) {
    //     console.log("Error", err);
    //     return err.message;
    //   } if (data) {
    //     console.log("Upload Success", data);
    //     return data.key;
    //   }
    // });
    return result;

}


exports.HealthCheck = function (req, res) {
    try {
        res.send({
            status: 200,
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}
async function GetAgentsUnderManagers(req, res) {
    try {

        if (isNaN(req.query.ManagerId)) {
            res.send({
                status: 200,
                data: []
            });
            return;
        }


        let query = `SELECT distinct RSM.RoleId,UD.UserID, UD.EmployeeId, UD.UserName, UD.ManagerId, RM.RoleName
                from CRM.UserGroupRoleMapNew UGRMN (NOLOCK)
                INNER JOIN CRM.UserDetails UD (NOLOCK) ON UD.UserID=UGRMN.UserID AND UD.IsActive=1 
                INNER JOIN  CRM.RoleSuperMaster RSM (NOLOCK) ON UGRMN.RoleSuperId=RSM.RoleSuperId    
                INNER JOIN  CRM.RoleMaster RM (NOLOCK) ON RSM.RoleId=RM.RoleId    
                INNER JOIN CRM.PermissionDetails PD (NOLOCK) ON UGRMN.UserId=PD.UserId  
                WHERE  RSM.RoleCategoryID IN(2) AND UD.IsActive=1 AND RSM.RoleId IN (13) and UD.ManagerId IN (SELECT item from dbo.fnSplit(@ManagerId, ','))`;


        console.log(query);

        let sqlparam = [];
        sqlparam.push({ key: "ManagerId", value: req.query.ManagerId });

        let result = await sqlHelper.sqlquery("R", query, sqlparam);


        res.send({
            status: 200,
            data: result.recordset
        });
        //let res = result.recordset;
        //return res;
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}

async function GetSurveyMaster(SurveyID) {
    let result = {};
    try {
        let query = `
    Select * from PBT.SurveyMaster where SurveyID = @SurveyID and IsActive = 1`


        let sqlparam = [];
        sqlparam.push({ key: "SurveyID", value: SurveyID });
        let data = await sqlHelper.sqlquery("R", query, sqlparam);
        if (data.recordsets.length > 0)
            result = data.recordsets[0][0]

        console.log(result)
    }
    catch (e) {

    }
    finally {
        return result;
    }

}


async function GetSurveyParentQuestionMaster(SurveyID) {
    let result = [];
    try {
        let query = `
        Select 
        SQM.*,
        FM.FieldType
        FROM PBT.SurveyQuestionMaster SQM (NOLOCK) 
        INNER JOIN PBT.FieldMaster FM (NOLOCK) ON FM.FieldId = SQM.FieldID
        LEFT JOIN  PBT.SurveyNestedQuestionMaster SNQM (NOLOCK) ON SNQM.NestedQuestionId = SQM.QuestionID 
        where SurveyID = @SurveyID
        and SNQM.NestedQuestionId IS NULL
         ORDER BY SQM.SerialNo ASC`


        let sqlparam = [];
        sqlparam.push({ key: "SurveyID", value: SurveyID });
        let data = await sqlHelper.sqlquery("R", query, sqlparam);
        if (data.recordsets.length > 0)
            result = data.recordsets[0]

        for (let index = 0; index < result.length; index++) {
            const element = result[index];

            element["Options"] = await GetSurveyOptionMaster(element.QuestionID);

        }

    }
    catch (e) {

    }
    finally {
        return result;
    }

}


async function GetSurveyQuestionMaster(QuestionID) {
    let result = {};
    try {
        let query = `
        Select 
        SQM.*,
        FM.FieldType
        FROM PBT.SurveyQuestionMaster SQM (NOLOCK) 
        INNER JOIN PBT.FieldMaster FM (NOLOCK) ON FM.FieldId = SQM.FieldID
        where QuestionID = @QuestionID
         ORDER BY SQM.SerialNo ASC`


        let sqlparam = [];
        sqlparam.push({ key: "QuestionID", value: QuestionID });
        let data = await sqlHelper.sqlquery("R", query, sqlparam);
        if (data.recordsets.length > 0)
            result = data.recordsets[0][0]

        result["Options"] = await GetSurveyOptionMaster(result.QuestionID);


    }
    catch (e) {

    }
    finally {
        return result;
    }

}

async function GetSurveyOptionMaster(QuestionID) {
    let result = [];
    try {

        let query = `
        Select SOM.OptionId,SOM.OptionText,SNQM.NestedQuestionId,SerialNo from PBT.SurveyOptionMaster SOM (NOLOCK)
        LEFT JOIN PBT.SurveyNestedQuestionMaster SNQM (NOLOCK) ON SOM.OptionId = SNQM .SelectedOptionId 
        where QuestionID = @QuestionID order by SerialNo ASC`


        let sqlparam = [];
        sqlparam.push({ key: "QuestionID", value: QuestionID });
        let data = await sqlHelper.sqlquery("R", query, sqlparam);
        if (data.recordsets.length > 0)
            result = data.recordsets[0];

        for (let index = 0; index < result.length; index++) {
            const element = result[index];
            if (element.NestedQuestionId != null) {
                element["NestedQuestion"] = await GetSurveyQuestionMaster(element.NestedQuestionId);
            }
        }

    }
    catch (e) {

    }
    finally {
        return result;
    }
}


async function GetSurveyDetails(req, res) {
    let data = {};
    try {
        data = await GetSurveyMaster(req.query.SurveyID);
        if (data) {
            data["Questions"] = await GetSurveyParentQuestionMaster(req.query.SurveyID);
        }
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {
        res.send({
            status: 200,
            data: data
        });
    }
}


// async function GetSurveyOptionMaster(QuestionID) {
//     let result = [];
//     try {



//         let query = `
//             Select * from PBT.SurveyOptionMaster where QuestionID = @QuestionID order by SerialNo ASC`

//         console.log(query)
//         console.log(QuestionID)
//         let sqlparam = [];
//         sqlparam.push({ key: "QuestionID", value: QuestionID });
//         let data = await sqlHelper.sqlquery("R", query, sqlparam);
//         if (data.recordsets.length > 0)
//             result = data.recordsets[0]

//     }
//     catch (e) {

//     }
//     finally {
//         return result;
//     }
// }


async function rejectLeads(xlsData, userId) {
    var response = '';
    try {
        let sqlparam = [];
        sqlparam.push({ key: "LeadId", value: xlsData.LeadId });
        sqlparam.push({ key: "UserId", value: userId });
        sqlparam.push({ key: "RejectionSet", value: xlsData.RejectionSet });
        sqlparam.push({ key: "Reason", value: xlsData.Reason ? (xlsData.Reason.trim()).substring(0, 100) : '' });
        let result = await sqlHelper.sqlProcedure("L", "[MTX].[RejectLeadByUploadProcess]", sqlparam);
        let status = Object.values(result.recordset[0]);
        response = status[0];
    }
    catch (e) {
        console.log(e.getMessage());
        response = e.getMessage();
    }
    finally {
        return response;
    }
}

exports.uploadLeadRejectionFile = async function (req, res) {
    try {
        var appDir = path.dirname(require.main.filename);
        var awsFilePath = '';
        var awsFileName = '';
        console.log('appdir', appDir, req.body, req.files);
        if (!req.files) {
            return res.status(500).send({ msg: "file is not found" })
        }
        // accessing the file
        const myFile = req.files.myFile;
        const today = moment(Date.now()).format('YYYY-MM-DD');
        var dir = appDir + "\\client\\public\\SampleExcelfiles\\" + today;
        fs.existsSync(dir) || fs.mkdirSync(dir);
        var myFileName = myFile.name.split('.').join('-' + Date.now() + '.');
        fs.copyFile(`${myFile.tempFilePath}`, process.cwd() + "\\client\\public\\SampleExcelfiles\\" + today + "\\" + myFileName, async function (err) {
            if (err) {
                throw err
            } else {
                console.log("Successfully copied and moved the file!")
                // Upload File to AWS
                var filePath = appDir + "\\client\\public\\SampleExcelfiles\\" + today + "\\" + myFileName;
                let timestamp = Date.now();
                let filename = "LeadRejection/" + today + "/" + myFileName;

                xlsxFile(fs.createReadStream(`${filePath}`)).then(async (rows) => {
                    var _result = [];
                    //var JsonExcel = {};
                    for (var i = 1; i in rows; i++) {
                        var obj = {};
                        for (j in rows[i]) {
                            obj[rows[0][j]] = rows[i][j];
                        }

                        //var innerObj = {};
                        obj['Status'] = await rejectLeads(obj, req.body.UserId);
                        console.log(obj);
                        _result.push(obj);
                    }
                    res.send({
                        status: 200,
                        data: _result
                    });
                })


                /*let awsUploadResponse = uploadAWSfiles(filePath, filename, 'asterisk-log');
                console.log("awsresp",awsUploadResponse);
                awsUploadResponse.then(function (resultedkey) {
                    console.log('resultedkey',resultedkey);
                    var awsFileName = myFileName;
                    var awsFilePath = 'https://' + req.headers.host + '/api/v1/db/getawsfile?key=' + resultedkey + '&bucket=asterisk-log';

                    xlsxFile(fs.createReadStream(`${myFile}`)).then((rows) => {
                        var _result = [];
                        //var JsonExcel = {};
                        for (var i = 1; i in rows; i++) {
                            var obj = {};
                            for (j in rows[i]) {
                                obj[rows[0][j]] = rows[i][j];
                            }
                            obj['Status'] = rejectLeads(obj, req.body.UserId);
                            //var innerObj = {};
                            _result.push(obj);
                        }
                        res.send({
                            status: 200,
                            data: _result
                        });
                    });
                });*/

            }
        })
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}

async function UpdateUserGrades(xlsData, userId) {
    var response = '';
    try {
        let sqlparam = [];
        sqlparam.push({ key: "Grade", value: xlsData.Grade });
        sqlparam.push({ key: "UserId", value: userId });
        sqlparam.push({ key: "EmployeeId", value: xlsData.EmployeeId });
        let result = await sqlHelper.sqlProcedure("L", "[MTX].[UpdateUserGradesByUpload]", sqlparam);
        let status = Object.values(result.recordset[0]);
        response = status[0];
    }
    catch (e) {
        console.log(e.getMessage());
        response = e.getMessage();
    }
    finally {
        return response;
    }
}

exports.uploadUserGradesFile = async function (req, res) {
    try {
        var appDir = path.dirname(require.main.filename);
        var awsFilePath = '';
        var awsFileName = '';
        //console.log('appdir',appDir,req.body,req.files);
        if (!req.files) {
            return res.status(500).send({ msg: "file is not found" })
        }
        // accessing the file
        const myFile = req.files.myFile;
        const today = moment(Date.now()).format('YYYY-MM-DD');
        var dir = appDir + "\\client\\public\\SampleExcelfiles\\" + today;
        fs.existsSync(dir) || fs.mkdirSync(dir);
        var myFileName = myFile.name.split('.').join('-' + Date.now() + '.');
        fs.copyFile(`${myFile.tempFilePath}`, process.cwd() + "\\client\\public\\SampleExcelfiles\\" + today + "\\" + myFileName, async function (err) {
            if (err) {
                throw err
            } else {
                console.log("Successfully copied and moved the file!")
                // Upload File to AWS
                var filePath = appDir + "\\client\\public\\SampleExcelfiles\\" + today + "\\" + myFileName;
                let timestamp = Date.now();
                let filename = "UploadUserGrade/" + today + "/" + myFileName;

                xlsxFile(fs.createReadStream(`${filePath}`)).then(async (rows) => {
                    var _result = [];
                    //var JsonExcel = {};
                    for (var i = 1; i in rows; i++) {
                        var obj = {};
                        for (j in rows[i]) {
                            obj[rows[0][j]] = rows[i][j];
                        }

                        //var innerObj = {};
                        obj['Status'] = await UpdateUserGrades(obj, req.body.UserId);
                        console.log(obj);
                        _result.push(obj);
                    }
                    res.send({
                        status: 200,
                        data: _result
                    });
                })
            }
        })
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}

async function FetchMenuMaster(req, res) {
    try {

        console.log(req.body)
        console.log(req.query)

        if (isNaN(req.query.UserId)) {
            res.send({
                status: 200,
                data: []
            });
            return;
        }

        let sqlparam = [];
        sqlparam.push({ key: "UserId", value: req.query.UserId });

        let query = `SELECT FM.* FROM [CRM].[UserMenuMap] UMM  JOIN CRM.FunctionsMaster FM ON FM.MenuId = UMM.MenuId  where UserId = @UserId and URL LIKE '%matrixdashboard%'`
        let result = await sqlHelper.sqlquery("R", query, sqlparam);
        res.send({
            status: 200,
            data: result.recordsets
        });
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {

    }
}



async function GetQuizMaster(QuizID) {
    let result = {};
    try {
        let query = `
    Select * from PBT.QuizMaster where QuizID = @QuizID and IsActive = 1`


        let sqlparam = [];
        sqlparam.push({ key: "QuizID", value: QuizID });
        let data = await sqlHelper.sqlquery("R", query, sqlparam);
        if (data && data.recordsets && data.recordsets.length > 0)
            result = data.recordsets[0][0] || []

    }
    catch (e) {

    }
    finally {
        return result;
    }

}


async function GetQuizParentQuestionMaster(QuizID) {
    let result = [];
    try {
        let query = `
        Select 
        QQM.*,
        FM.FieldType
        FROM PBT.QuizQuestionMaster QQM (NOLOCK) 
        INNER JOIN PBT.QuizFieldMaster FM (NOLOCK) ON FM.FieldId = QQM.FieldID
        LEFT JOIN  PBT.QuizNestedQuestionMaster QNQM (NOLOCK) ON QNQM.NestedQuestionId = QQM.QuestionID 
        where QuizID = @QuizID
        and QNQM.NestedQuestionId IS NULL
         ORDER BY QQM.SerialNo ASC`


        let sqlparam = [];
        sqlparam.push({ key: "QuizID", value: QuizID });
        let data = await sqlHelper.sqlquery("R", query, sqlparam);
        if (data && data.recordsets && data.recordsets.length > 0)
            result = data.recordsets[0]

        for (let index = 0; index < result.length; index++) {
            const element = result[index];

            element["Options"] = await GetQuizOptionMaster(element.QuestionID);

        }

    }
    catch (e) {

    }
    finally {
        return result;
    }

}


async function GetQuizQuestionMaster(QuestionID) {
    let result = {};
    try {
        let query = `
        Select 
        QQM.*,
        FM.FieldType
        FROM PBT.QuizQuestionMaster QQM (NOLOCK) 
        INNER JOIN PBT.FieldMaster FM (NOLOCK) ON FM.FieldId = QQM.FieldID
        where QuestionID = @QuestionID
         ORDER BY QQM.SerialNo ASC`


        let sqlparam = [];
        sqlparam.push({ key: "QuestionID", value: QuestionID });
        let data = await sqlHelper.sqlquery("R", query, sqlparam);
        if (data && data.recordsets && data.recordsets.length > 0)
            result = data.recordsets[0][0] || []

        result["Options"] = await GetQuizOptionMaster(result.QuestionID);


    }
    catch (e) {

    }
    finally {
        return result;
    }

}

async function GetQuizOptionMaster(QuestionID) {
    let result = [];
    try {

        let query = `
        Select QOM.OptionId, QOM.OptionText, QOM.IsCorrect, QNQM.NestedQuestionId,SerialNo from PBT.QuizOptionMaster QOM (NOLOCK)
        LEFT JOIN PBT.QuizNestedQuestionMaster QNQM (NOLOCK) ON QOM.OptionId = QNQM.SelectedOptionId 
        where QuestionID = @QuestionID order by SerialNo ASC`


        let sqlparam = [];
        sqlparam.push({ key: "QuestionID", value: QuestionID });
        let data = await sqlHelper.sqlquery("R", query, sqlparam);
        if (data && data.recordsets && data.recordsets.length > 0)
            result = data.recordsets[0];

        for (let index = 0; index < result.length; index++) {
            const element = result[index];
            if (element.NestedQuestionId != null) {
                element["NestedQuestion"] = await GetQuizQuestionMaster(element.NestedQuestionId);
            }
        }

    }
    catch (e) {

    }
    finally {
        return result;
    }
}


async function GetQuizDetails(req, res) {
    let data = {};
    try {
        data = await GetQuizMaster(req.query.QuizID);
        if (data) {
            data["Questions"] = await GetQuizParentQuestionMaster(req.query.QuizID);
        }
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {
        res.send({
            status: 200,
            data: data
        });
    }
}


//exports.GetAnswerQuizDetails = async function (req, res) {
async function GetAnswerQuizDetails(req, res) {
    let data = {};
    try {
        data = await GetQuizMaster(req.query.QuizId);
        if (data) {
            data["Questions"] = await GetAnswerQuizParentQuestionMaster(req, req.query.QuizId);
        }
    }
    catch (e) {
        console.log(e);
        res.send({
            status: 500,
            message: e
        });
    }
    finally {
        res.send({
            status: 200,
            data: data
        });
    }
}

async function GetAnswerQuizParentQuestionMaster(req, QuizID) {
    let result = [];
    try {
        let query = `
        Select 
        QQM.*,
        FM.FieldType
        FROM PBT.QuizQuestionMaster QQM (NOLOCK) 
        INNER JOIN PBT.QuizFieldMaster FM (NOLOCK) ON FM.FieldId = QQM.FieldID
        LEFT JOIN  PBT.QuizNestedQuestionMaster QNQM (NOLOCK) ON QNQM.NestedQuestionId = QQM.QuestionID 
        where QuizID = @QuizID
        and QNQM.NestedQuestionId IS NULL
         ORDER BY QQM.SerialNo ASC`


        let sqlparam = [];
        sqlparam.push({ key: "QuizID", value: QuizID });
        let data = await sqlHelper.sqlquery("R", query, sqlparam);
        if (data && data.recordsets && data.recordsets.length > 0)
            result = data.recordsets[0]

        for (let index = 0; index < result.length; index++) {
            const element = result[index];

            element["Options"] = await GetAnswerQuizOptionMaster(req.query.QuizMappingId, element.QuestionID);

        }

    }
    catch (e) {

    }
    finally {
        return result;
    }
    
    
}


async function GetAnswerQuizOptionMaster(QuizMappingId,QuestionId) {
    // let result = [];
    try {
        let sqlparam = [];
        sqlparam.push({ key: "QuizMappingId", value: QuizMappingId });
        sqlparam.push({ key: "QuestionId", value: QuestionId });
        let result = await sqlHelper.sqlProcedure("L", "[PBT].[GetQuizAnswers]", sqlparam);
        
        if (result && result.recordsets && result.recordsets.length > 0){
            console.log(result.recordsets[0])
            result = result.recordsets[0]
        }

        return result;
        
    }
    catch (e) {

    }
}

exports.DownloadSurveyData = async function (req, res) {
// async function DownloadSurveyData(SurveyId) {
    let result = [];
    try {
        let sqlparam = [];
        sqlparam.push({ key: "SurveyId", value: req.query.surveyid });
        let resultSurvey = await sqlHelper.sqlProcedure("R", "[PBT].[GetSurveyAgentData]", sqlparam);
        
        if (resultSurvey && resultSurvey.recordsets && resultSurvey.recordsets.length > 0){
            // console.log(resultSurvey.recordsets[0])
            result = resultSurvey.recordsets;
        }

        res.send({
            status: 200,
            data: result
        });
        
    }
    catch (e) {

    }
}

exports.DownloadQuizData = async function (req, res) {
    // async function DownloadSurveyData(SurveyId) {
    let result = [];
    try {
        let sqlparam = [];
        sqlparam.push({ key: "QuizId", value: req.query.QuizId });
        let resultQuiz = await sqlHelper.sqlProcedure("R", "[PBT].[GetQuizAgentData]", sqlparam);
        
        if (resultQuiz && resultQuiz.recordsets && resultQuiz.recordsets.length > 0){
            // console.log(resultQuiz.recordsets[0])
            result = resultQuiz.recordsets;
        }

        res.send({
            status: 200,
            data: result
        });
        
    }
    catch (e) {

    }
}

exports.QuizInsertResponsesData = async function (req, res) {
    let InsertCount = 0;
    let _QuizID;
    let result = [];

    try {
        let UserId = req.body.UserId;
        const QuizMappingid = req.body.quizMappingId

        for(let index=0; index < req.body.Responses.length ; index++){
            const element = { ...req.body.Responses[index], IsActive: 1, CreatedOn: moment().format("YYYY-MM-DD HH:mm:ss") };
            // const element = { ...req.body.Responses[index], IsActive: 1, CreatedOn: moment().format("YYYY-MM-DD HH:mm:ss") };
            const { QuizID, QuestionID, OptionID, OptionText, AnswerText, CreatedOn, IsActive} = element;
            _QuizID = QuizID
            
            let query = `INSERT INTO PBT.QuizResponse (QuizID, QuestionID, OptionID, OptionText, AnswerText,
                UserId, CreatedOn, IsActive, QuizMappingid)
            VALUES ( @QuizID, @QuestionID, @OptionID, @OptionText, @AnswerText, @UserId, 
                @CreatedOn, @IsActive, @QuizMappingid);`

            let sqlparam = [];
            sqlparam.push({ key: "QuizID", value: QuizID });
            sqlparam.push({ key: "QuestionID", value: QuestionID });
            sqlparam.push({ key: "OptionID", value: OptionID });
            sqlparam.push({ key: "OptionText", value: OptionText });
            sqlparam.push({ key: "AnswerText", value: AnswerText });
            sqlparam.push({ key: "UserId", value: UserId });
            sqlparam.push({ key: "CreatedOn", value: CreatedOn });
            sqlparam.push({ key: "IsActive", value: IsActive });
            sqlparam.push({ key: "QuizMappingid", value: QuizMappingid });

            let data = await sqlHelper.sqlquery("L", query, sqlparam);
            console.log(data);
            if (data && data.rowsAffected && data.rowsAffected.length > 0)
                InsertCount= InsertCount + 1;

        }
        
        if(req.body.Responses.length == InsertCount){
            let query = `UPDATE PBT.QuizAgentMapping SET IsCompleted = 1
            Where ID = @ID;`

            let sqlparam = [];
            // sqlparam.push({ key: "QuizID", value: QuizStatus });
            // sqlparam.push({ key: "UserId", value: UserId });
            sqlparam.push({ key: "ID", value: QuizMappingid });
            
            let data = await sqlHelper.sqlquery("L", query, sqlparam);

            let sqlparams = [];
            sqlparams.push({ key: "QuizId", value: _QuizID });
            sqlparams.push({ key: "UserId", value: UserId });
            sqlparams.push({ key: "QuizAgentMappingId", value: QuizMappingid });
            let resultQuiz = await sqlHelper.sqlProcedure("R", "[PBT].[GetQuizReview]", sqlparams);
            
            // if (resultQuiz && resultQuiz.recordsets && resultQuiz.recordsets.length > 0){
            //     // console.log(resultQuiz.recordsets[0])
            //     result = resultQuiz.recordsets;
            // }
            
            res.send({
                status: 200,
                //data: result
            });

            return;
        }
        res.send({
            status: 500,
            // data: result
        });
    }
    catch (e) {
        res.send({
            status: 500,
            message: e
        });
    }
}


async function AddUpdateCourseContentSql(req, awsFilePath, pageId) {
    try {
        let sqlparam=[];
        const { userId } = req.user || {}
        const body = req.body || {};
        const IsActive = body.Action === 'Add' ? 1 : body.IsActive;
        sqlparam.push({key:"ContentId", value:body.Id});
        sqlparam.push({key:"ContentName", value:body.ContentName});
        sqlparam.push({ key: "ChapterId", value: body.ChapterId });
        sqlparam.push({ key: "ContentTypeId", value: body.ContentTypeId });
        sqlparam.push({key:"ContentUrl", value:awsFilePath});
        sqlparam.push({key:"Description", value: body.Description});
        sqlparam.push({key:"SerialNo", value:body.SerialNo});
        sqlparam.push({key:"CreatedBy",value: body.userId });
        sqlparam.push({key:"UpdatedBy", value: body.userId});
        sqlparam.push({ key: "IsActive", value: IsActive });
        sqlparam.push({key:"Action", value: body.Action});
        sqlparam.push({key:"QuizId", value:body.QuizId});

        let result = await sqlHelper.sqlProcedure("L", "[PBT].[InsertUpdateCourseContent]", sqlparam);
        return { errorStatus: 0, data: result};
    }
    catch (e) {
        console.log(e);
        return { errorStatus: 1, data: e};
    }
    finally {

    }
}

exports.PostCourseContentData = async function (req, res) {
    try {
        let awsFilePath = '';
        let pageId = '';
        if (req.body?.pageId) {
            pageId = req.body.pageId;
        }
        if(req.body.Edit)
        {
         let {errorStatus, data}= await AddUpdateCourseContentSql(req,req.body.ContentUrl,pageId); 
         if(errorStatus === 0) {
            return res.status(200).json({
                status: 200,
                data: data?.recordset || []
            });
        } else {
            return res.status(500).json({
                status: 500,
                data: 'error'
            });
        }

        }
        if (req.files) {
            const CourseContentFile = req.files?.CourseContentFile;
            const today = moment(Date.now()).format('YYYY-MM-DD');
            let myFileName = CourseContentFile.name.split('.').join('-' + Date.now() + '.');
            console.log('myFileName', myFileName);
            let filename = "PBSchool/" + today + "/" + myFileName;
            let awsUploadResponse = uploadAWSfiles(`${CourseContentFile.tempFilePath}`, filename, 'policystatic.policybazaar.com');

            awsUploadResponse.then(async function (resultaws) {
                console.log('aws', resultaws);
                let awsFilePath = 'https://' + resultaws.Bucket + '/' + resultaws.Key;

                let { errorStatus, data} = await AddUpdateCourseContentSql(req, awsFilePath, pageId);
                
                if(errorStatus === 0) {
                    return res.status(200).json({
                        status: 200,
                        data: data?.recordset || []
                    });
                } else {
                    return res.status(500).json({
                        status: 500,
                        data: 'error'
                    });
                }

            }).catch(err =>
                { console.log('awsUploadResponse', err)
                return res.status(500).json({
                    status: 500,
                    data: null
                });
            })
        } else {
            let result = await AddUpdateCourseContentSql(req, awsFilePath, pageId);
            return res.send({
                status: 200,
                data: result?.recordset
            });
        }
    }
    catch (err) {
        console.log('Inside PostCourseContentData',err);
        res.send({
            status: 500,
            message: e
        });
    }
}


exports.PBSchoolQuizInsertResponsesData = async function (req, res) {
    let InsertCount = 0;
    let _QuizID;
    let result = [];

    try {
        let UserId = req.body.UserId;
        // const QuizMappingid = req.body.quizMappingId
        // let qid= req.body.Responses[0].QuizID;

        for(let index=0; index < req.body.Responses.length ; index++){
            const element = { ...req.body.Responses[index], IsActive: 1, CreatedOn: moment().format("YYYY-MM-DD HH:mm:ss") };
            // const element = { ...req.body.Responses[index], IsActive: 1, CreatedOn: moment().format("YYYY-MM-DD HH:mm:ss") };
            const { QuizID, QuestionID, OptionID, OptionText, CreatedOn, IsActive} = element;
            _QuizID = QuizID
            
            let query = `INSERT INTO PBT.CourseQuizResponse (QuizID, QuestionID, OptionID, OptionText,
                UserId, CreatedOn, IsActive)
            VALUES ( @QuizID, @QuestionID, @OptionID, @OptionText, @UserId, 
                @CreatedOn, @IsActive);`

            let sqlparam = [];
            sqlparam.push({ key: "QuizID", value: QuizID });
            sqlparam.push({ key: "QuestionID", value: QuestionID });
            sqlparam.push({ key: "OptionID", value: OptionID });
            sqlparam.push({ key: "OptionText", value: OptionText });
            // sqlparam.push({ key: "AnswerText", value: AnswerText });
            sqlparam.push({ key: "UserId", value: UserId });
            sqlparam.push({ key: "CreatedOn", value: CreatedOn });
            sqlparam.push({ key: "IsActive", value: IsActive });
            // sqlparam.push({ key: "QuizMappingid", value: QuizMappingid });

            let data = await sqlHelper.sqlquery("L", query, sqlparam);
            console.log(data);
            if (data && data.rowsAffected && data.rowsAffected.length > 0)
                InsertCount= InsertCount + 1;

        }
        
        if(req.body.Responses.length == InsertCount){
            let query = `Insert into PBT.CourseQuizAgentMapping (UserId,QuizId,CompletedOn) values 
            ( @UserId, @QuizId, @CompletedOn);`

            let sqlparam = [];
            // sqlparam.push({ key: "QuizID", value: QuizStatus });
            // sqlparam.push({ key: "UserId", value: UserId });
            sqlparam.push({ key: "UserId", value: UserId });
            sqlparam.push({key:"QuizId",value:_QuizID});
            sqlparam.push({key:"CompletedOn",value:moment().format("YYYY-MM-DD HH:mm:ss")});
            let data = await sqlHelper.sqlquery("L", query, sqlparam);

            let sqlparams = [];
            sqlparams.push({ key: "QuizId", value: _QuizID });
            sqlparams.push({ key: "UserId", value: UserId });
            // sqlparams.push({ key: "QuizAgentMappingId", value: QuizMappingid });
            let resultQuiz = await sqlHelper.sqlProcedure("R", "[PBT].[GetCourseQuizReview]", sqlparams);
            
            // if (resultQuiz && resultQuiz.recordsets && resultQuiz.recordsets.length > 0){
            //     // console.log(resultQuiz.recordsets[0])
            //     result = resultQuiz.recordsets;
            // }
            
            res.send({
                status: 200,
                //data: result
            });

            return;
        }
        res.send({
            status: 500,
            // data: result
        });
    }
    catch (e) {
        res.send({
            status: 500,
            message: e
        });
    }
}


async function getRosterData (req, res) {
    try {

        let sqlparam = [];

        let query = `
        Declare @RosterId BIGINT

        SELECT TOP(1) @RosterId = Id from Rms.RosterMaster RM ORDER BY RM.CREATEDON desc
    
        Select RM.Id, RM.StartDate, RM.EndDate,PM.ProductId, PM.ProductName, PM.ProcessId, PM.ProcessName,
                RUH.UserId, UM.EmployeeId,UM.EmployeeName, UM1.EmployeeName AS TLName
                from Rms.RosterUserHistory RUH
                INNER JOIN Rms.RosterMaster RM (NOLOCK) ON RM.Id = RUH.RosterId
                INNER JOIN Rms.UserMaster UM (NOLOCK) ON  UM.UserId = RUH.UserId
                INNER JOIN Rms.UserMaster UM1 (NOLOCK) ON UM1.UserId = RUH.ManagerId
                INNER JOIN Rms.ProcessMaster PM (NOLOCK) ON RUH.ProcessId = PM.Processid
                LEFT JOIN Rms.TransferRequest TR (NOLOCK) ON TR.UserId = RUH.UserId
                where  RUH.ManagerId = 19412 AND RUH.RosterId = @RosterId 
                AND (TR.Status = 2 OR TR.Status IS NULL) 
                ORDER BY RM.CREATEDON 
                --AND RM.FreezedForAdmin = 0`

        let result = await sqlHelper.sqlquery("R", query, sqlparam);

      
        return res.send({
            status: 200,
            data: result?.recordsets || []
        });
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}


async function getEmployeeData (req, res) {
    try {

        let EmployeeId = req.query.EmployeeeId;
        let query = `
        Select EmployeeId, EmployeeName from RMs.UserMaster Where Employeeid Like '@EmployeeeId%'
        `
        let sqlparams = [];
        sqlparams.push({ key: "QuizId", value: EmployeeId });

        let result = await sqlHelper.sqlquery("R", query, sqlparams);

      
        return res.send({
            status: 200,
            data: result?.recordsets || []
        });
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}


async function GetHolidays (req, res) {
    try {

        let sqlparam=[];
        const { userId } = req.user || {}
        
        sqlparam.push({key:"UserId", value: userId});
        sqlparam.push({key:"RosterId", value: req.query.params[0].RosterId});

        let cachedGetUserHolidays = cache.get('GetUserHolidays_'+userId);
        if (cachedGetUserHolidays) {
            return res.send({
                status: 200,
                data: cachedGetUserHolidays[0] || []
            });
        }

        let result = await sqlHelper.sqlProcedure("L", "[Rms].[GetHolidays_v2]", sqlparam);
      
        cache.put('GetUserHolidays_'+userId, result?.recordsets, (10 * 60 * 1000));

        return res.send({
            status: 200,
            data: result?.recordsets[0] || []
        });
    }
    catch (e) {
        console.log(e);
        return res.send({
            status: 500,
            message: e
        });
    }
}