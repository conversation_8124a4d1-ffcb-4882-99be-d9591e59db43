
// import React from "react";
// // import { Form } from 'react-bootstrap';
// import Datetime from 'react-datetime';
// import 'react-datetime/css/react-datetime.css'
// import Moment from 'react-moment';
// import moment from 'moment';
// import {

//     Col
// } from "reactstrap";

// import DropDownList from "../views/Common/DropDownList.jsx"
// import MultiSelectList from "../views/Common/MultiSelectList.jsx";
// import config from "../config.jsx";
// import MultiSelectListMongo from "../views/Common/MultiSelectListMongo.jsx";

// import DropDownAutoComplete from "../views/Common/DropDownAutoComplete.jsx";
import * as XLSX from 'xlsx';

// export function fnBindRootData(col, props) {
//     if (col.type === "dropdown") {
//         props.GetCommonData({
//             limit: 10,
//             skip: 0,
//             root: col.config.root,
//             cols: col.config.cols,
//             con: col.config.con,
//             data: col.config.data,
//             state: true,
//             statename: col.config.statename
//         });
//     }
//     if(col.type === "autodropdown" && col.config.sp) {
        
//         props.GetCommonspData({
//             root: col.config.root,
//             params: col.config.params,
//             state: true,
//             statename: col.config.statename
//         });   
//     }
// }

// export function fnRenderfrmControl(col, formvalue, handleChange, event, errors, handleEditorChange = 0) {
//     errors = errors || 0; 
//     if (col) {
//         // if (col.editable === false && (event === "Edit" || event === "Copy")) {
//         //     return <Form.Group as={Col} md={3} controlId={col.name}>
//         //         <Form.Label>{col.label}</Form.Label>
//         //         <Form.Control disabled="disabled" type="text" placeholder={"Enter " + col.label} value={formvalue[col.name]} />
//         //     </Form.Group>
//         // }
//         var name = col.alias ? col.alias : col.name;
//         if(col.hideonmodal === true ){
//             return <></>;
//         }
//         if(col.addable === false && event == 'Add'){
//             return <></>;
//         }
//         if (col.type === "text") {
//             // return <Form.Group as={Col} md={6} controlId={name} key={name}>
//             //     <Form.Label>{col.label}</Form.Label>
//             //     <Form.Control required
//             //         disabled={(col.editable === false && (event === "Edit")) ? "disabled" : ""}
//             //         type="text" onChange={handleChange}  placeholder={"Enter " + col.label} 
//             //         value={formvalue[name]} />
//             // </Form.Group>
//         }
//         if (col.type === "bool") {
//             // return <Form.Group as={Col} md={3} controlId={name} key={name}>
//             //     <Form.Label>{col.label} &nbsp;</Form.Label>
//             //     <input type="checkbox"
//             //         label=""
//             //         disabled={(col.editable === false && event === "Edit") ? "disabled" : ""}
//             //         onChange={handleChange}
//             //         checked={formvalue[name]}
//             //         name={name} id={name} />
//             // </Form.Group>
//         }
//         if (col.type === "dropdown") {
//             let filterkey = null;
//             let filtervalue = null;
//             if (col && col.config && col.config.cols && col.config.cols.length > 2) {
//                 filterkey = col.config.cols[2];
//                 filtervalue = formvalue[filterkey];
//             }
//             // return <Form.Group as={Col} md={3} controlId={name} key={name}>
//             //     <Form.Label>{col.label}</Form.Label>
//             //     <DropDownList
//             //         disabled={(col.editable === false && (event === "Edit")) ? "disabled" : ""}
//             //         visible={true} col={col}
//             //         value={formvalue[name]}
//             //         filterkey={filterkey}
//             //         filtervalue={filtervalue}
//             //         onChange={handleChange}
//             //         required= {(col.required == true)? true : false} />
//             // </Form.Group>

//         }
//         if (col.type === "autodropdown") {
//             let filterkey = null;
//             let filtervalue = null;
//             if (col && col.config && col.config.cols && col.config.cols.length > 2) {
//                 filterkey = col.config.cols[2];
//                 filtervalue = formvalue[filterkey];
//             }
//             // return <Form.Group as={Col} md={3} controlId={name} key={name}>
//             //     <Form.Label>{col.label}</Form.Label>
//             //     <DropDownAutoComplete
//             //         disabled={(col.editable === false && (event === "Edit")) ? "disabled" : ""}
//             //         visible={true} col={col}
//             //         value={formvalue[name]}
//             //         filterkey={filterkey}
//             //         filtervalue={filtervalue}
//             //         onChange={handleChange}
//             //         required= {(col.required == true)? true : false} />
//             // </Form.Group>

//         }
//         if (col.type === "textarea") {
//             // return <Form.Group as={Col} md={6} controlId={name} key={name}>
//             //     <Form.Label>{col.label}</Form.Label>
//             //     <Form.Control required
//             //         disabled={(col.editable === false && (event === "Edit")) ? "disabled" : ""}
//             //         type="text" onChange={handleChange} as="textarea" placeholder={"Enter " + col.label} 
//             //         value={formvalue[name]} />
//             // </Form.Group>
//         }
//         if (col.type === "multiselect") {
//             let filterkeymulti = null;
//             let filtervaluemulti = null;
//             if (col && col.config && col.config.cols && col.config.cols.length > 2) {
//                // console.log(col.config.cols[0],col.config.cols[1],col.config.cols[2]);
//                 filterkeymulti = col.config.cols[2];
//                 filtervaluemulti = formvalue[filterkeymulti];
//             }
//             // return <Form.Group as={Col} md={4} controlId={name} key={name}>
//             // <Form.Label id={"label-"+name}>{col.label}</Form.Label>
//             // <MultiSelectList
//             // disabled={(col.editable === false && (event === "Edit")) ? "disabled" : ""}
//             // col={col}
//             // id={name}
//             // onChange={handleChange}
//             // filterkey={filterkeymulti}
//             // filtervalue={filtervaluemulti}
//             // value={formvalue[name]}
//             // className={name}
//             // />
//             //  </Form.Group>
//         }
//         if (col.type === "multiselectMongo") {
//             let filterkeymulti = null;
//             let filtervaluemulti = null;
//             if (col && col.config && col.config.cols && col.config.cols.length > 2) {debugger;
//                 filterkeymulti = col.config.cols[2];
//                 filtervaluemulti = formvalue[filterkeymulti];
//             }
//             // return <Form.Group as={Col} md={3} controlId={name} key={name}>
//             // <Form.Label>{col.label}</Form.Label>
//             // <MultiSelectListMongo
//             // disabled={(col.editable === false && (event === "Edit")) ? "disabled" : ""}
//             // col={col}
//             // id={name}
//             // onChange={handleChange}
//             // filterkey={filterkeymulti}
//             // filtervalue={filtervaluemulti}
//             // value={formvalue[name]}
//             // />
//             //     </Form.Group>
//         }
//         if (col.type === "video") {
//             // return <Form.Group as={Col} md={2} controlId={name} key={name}>
//             // <Form.Label>{col.label}</Form.Label>
//             // <input type="file" style={{width: "250px", height: "40px"}} id = {name} onChange={handleChange} disabled={(col.editable === false && (event === "Edit")) ? "disabled" : ""} 
//             // required={(col.editable === true && (event === "Edit")) ? "" : "required"}/>  
//             // <input type="hidden" id="MAX_FILE_SIZE" value="8388608" name="MAX_FILE_SIZE"/>
//             // <span style={{color: "red"}}>{errors.video}</span>
            
//             // </Form.Group>
//         }
       
//         if (col.type === "int" || col.type === "number" || col.type === "decimal") {
//             // return <Form.Group as={Col} md={2} controlId={name} key={name}>
//             //     <Form.Label>{col.label}</Form.Label>
//             //     <Form.Control required
//             //         disabled={(col.editable === false && (event === "Edit")) ? "disabled" : ""}
//             //         type="number" onChange={handleChange} placeholder={"Enter " + col.label} value={formvalue[name]} />
//             // </Form.Group>
//         }
//         if (col.type === "hidden" || col.hide) {
//             return null;
//         }
//         if (col.type === "datetime") {
//             let datestr = formvalue[name];
//             if (datestr === "") {
//                 datestr = new Date();
//             } else {
//                 datestr = new Date(datestr);
//             }

//             // return <Form.Group as={Col} md={3} controlId={name} key={name}>
//             //     <Form.Label>{col.label}</Form.Label>
//             //     <Datetime value={datestr}

//             //         id={name} dateFormat="YYYY-MM-DD"
//             //         timeFormat={(col.timeFormat === false)? col.timeFormat: true}
//             //         onChange={moment => handleChange(moment, name)}
//             //         utc={(col.utc && col.utc == 'no' && event === "Add")?false : true}
//             //         inputProps={{
//             //             id: name,
//             //             name: name,
//             //             required: true,
//             //             disabled: (col.editable === false && (event === "Edit")) ? "disabled" : ""
//             //         }} />

//             // </Form.Group>
//         }
//         if (col.type === "time") {
//             let datestr = formvalue[name]
//             if (datestr === "") {
//                 datestr = new Date();
//             } else {
//                 datestr = new Date(datestr);
//             }

//             // return <Form.Group as={Col} md={3} controlId={name} key={name}>
//             //     <Form.Label>{col.label}</Form.Label>
//             //     <Datetime

//             //         value={datestr} id={name} dateFormat={false} utc={true}
//             //         onChange={moment => handleChange(moment, name)} timeFormat={true}
//             //         inputProps={{
//             //             id: name,
//             //             name: name,
//             //             required: true,
//             //             disabled: (col.editable === false && (event === "Edit")) ? "disabled" : ""
//             //         }} />

//             // </Form.Group>
//         }


//         // return <Form.Group as={Col} md={3} key={name} controlId={name}>
//         //     <Form.Label>{col.label}</Form.Label>
//         //     <Form.Control
//         //         required={col.required} maxLength={(col.maxLength)?col.maxLength:''}
//         //         disabled={(col.editable === false && (event === "Edit")) ? "disabled" : ""}
//         //         type="text" onChange={handleChange} placeholder={"Enter " + col.label} value={formvalue[name]} />
//         //         <span style={{color: "red"}}>{(errors.Link && col.name=='Link')?errors.Link:''}</span>
//         // </Form.Group>
//     }
//     else return null;
// }
// export function getMax(arr, prop) {
//     var max;
//     for (var i = 0; i < arr.length; i++) {
//         if (max === null || parseInt(arr[i][prop]) > parseInt(max[prop]))
//             max = arr[i];
//     }
//     return max;
// }




// export function fnDatatableCol(columnlist) {

//     var columns = []
//     columnlist.forEach(col => {
//         if (!col.hide) {
//             if (col.type === "datetime") {
//                 columns.push({
//                     name: col.label,
//                     selector: col.alias ? col.alias : col.name,
//                     sortable: col.sortable === undefined ? true : col.sortable,
//                     searchable: col.searchable === undefined ? false : col.searchable,
//                     type: col.type? col.type : "",
//                     format: col.format? col.format : "",
//                     width: col.width ? col.width : "150px",
//                     cell: row => row[col.name] ? <Moment format="DD/MM/YYYY HH:mm:ss" utc={true}>{row[col.name]}</Moment> : "N.A",
//                 })
//             } else if (col.type === "time") {
//                 columns.push({
//                     name: col.label,
//                     selector: col.alias ? col.alias : col.name,
//                     sortable: col.sortable === undefined ? true : col.sortable,
//                     searchable: col.searchable === undefined ? false : col.searchable,
//                     cell: row => <Moment format="HH:mm:ss">{row[col.name]}</Moment>,
//                 })
//             } else if (col.type === "bool") {
//                 columns.push({
//                     name: col.label,
//                     selector: col.alias ? col.alias : col.name,
//                     sortable: col.sortable === undefined ? true : col.sortable,
//                     searchable: col.searchable === undefined ? false : col.searchable,
//                     cell: row => <span>{row[col.alias ? col.alias : col.name] ? 'Yes' : 'No'}</span>,
//                     width: "150px",
//                 });
//             } else if (col.type === "dropdown") {
//                 columns.push({
//                     name: col.label,
//                     selector: col.alias ? col.alias + "_display" : col.name + "_display",
//                     sortable: col.sortable === undefined ? true : col.sortable,
//                     searchable: col.searchable === undefined ? false : col.searchable,
//                     type: col.type === undefined ? false : col.type,
//                     config: col.config === undefined ? false : col.config,
//                 })
//             }
//             else if (col.type === "autodropdown") {
//                 columns.push({
//                     name: col.label,
//                     selector: col.alias ? col.alias + "_display" : col.name + "_display",
//                     sortable: col.sortable === undefined ? true : col.sortable,
//                     searchable: col.searchable === undefined ? false : col.searchable,
//                     type: col.type === undefined ? false : col.type,
//                     config: col.config === undefined ? false : col.config,
//                 })
//             }
//             else {
//                 columns.push({
//                     name: col.label,
//                     selector: col.alias ? col.alias : col.name,
//                     sortable: col.sortable === undefined ? true : col.sortable,
//                     searchable: col.searchable === undefined ? false : col.searchable,
//                     cell: col.cell === undefined ? null : col.cell,
//                     width: col.width ? col.width : "150px",
//                 })
//             }
//         }
//     });

//     return columns;
// }

// export function fnCleanData(columnlist, formvalue, IsUpdate) {

//     columnlist.forEach(col => {
//         if (col.type === "datetime") {
//             let datestr = formvalue[col.name] //? moment(formvalue[col.name]).format("YYYY-DD-MM HH:mm:ss") : "";
//             if (datestr === "Invalid date") {
//                 delete formvalue[col.alias ? col.alias : col.name];
//             }
//             else {
//                 formvalue[col.alias ? col.alias : col.name] = datestr;
//             }
//         }
//         if (col.type === "dropdown") {
//             delete formvalue[col.alias ? col.alias + "_display" : col.name + "_display"];
//         }
//         if (col.type === "autodropdown") {
//             delete formvalue[col.alias ? col.alias + "_display" : col.name + "_display"];
//         }
//         if (formvalue[col.alias ? col.alias : col.name] === "" ||
//             formvalue[col.alias ? col.alias : col.name] === null ||
//             formvalue[col.alias ? col.alias : col.name] === "null" ||
//             formvalue[col.alias ? col.alias : col.name] === undefined ||
//             formvalue[col.alias ? col.alias : col.name] === "undefined") {
//             delete formvalue[col.alias ? col.alias : col.name];
//         }
//         if (IsUpdate && col.editable === false) {
//             delete formvalue[col.alias ? col.alias : col.name];
//         }
//     });
//     return formvalue;
// }


// export function GetJsonToArray(columnlist, name) {
//     let cols = [];
//     columnlist.forEach(col => {
//         if (col.type === "number" || col.type === "decimal") {
//             if (col.alias) {
//                 cols.push("ISNULL(" + col[name] + ",0) AS " + col.alias);
//             }
//             else {
//                 cols.push("ISNULL(" + col[name] + ",0) AS " + col[name]);
//             }
//         }

//         else {
//             if (col.alias) {
//                 cols.push("ISNULL(" + col[name] + ",'') AS " + col.alias);
//             } else {
//                 cols.push("ISNULL(" + col[name] + ",'') AS " + col[name]);
//             }
//             //cols.push("ISNULL(NULLIF(" + col[name] + ", ''), " + col[name] + ") AS " + col[name] + "");
//         }

//     });
//     return cols;
// }

// export function joinObject(json1, json2, jcolname) {

//     json2.forEach(j => {
//         json1.forEach(k => {
//             if (j.Id === k[jcolname]) {
//                 k[jcolname + "_display"] = j.Display;
//             }
//             else {
//                 if (!k[jcolname + "_display"]) {
//                     if (k[jcolname] != "") {

//                         k[jcolname + "_display"] = k[jcolname];
//                     }
//                     else {
//                         k[jcolname + "_display"] = "";
//                     }
//                 }
//             }
//         });
//     });

//     return json1;
// }

// export function CompareJson(obj1, obj2) {
//     var flag = true;

//     obj1.forEach(k => {
//         if (obj1[k] !== obj2[k]) {
//             flag = false;
//         }
//     });


//     return flag;
// }



export function getUrlParameter(name, decode) {

    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    var results = regex.exec(window.location.search);
    if (decode) {
        return results === null ? '' : results[1];
    }
    else
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
};

// export function OpenSalesView(custid, leadid, productid) {
//     let baseurl = config.api.SalesviewUrl;
//     try {
//         //baseurl = window.parent.location.href   
//     }
//     catch (e) {

//     }

//     return baseurl + window.btoa(custid + "/" + productid + "/" + leadid);
// }


// export function OpenNewSalesView(custid, leadid, productid) {
//     //let baseurl = config.api.NewSalesviewUrl;
//     let res;
//     try {
//         //baseurl = window.parent.location.href 
//         let baseurl = document.referrer;
//         if(baseurl != "") {
//             res = baseurl + "PGV/newSV/SalesView/"+ window.btoa(custid + "/" + productid + "/" + leadid);
//         }  
//     }
//     catch (e) {
//         console.error("Unable to open salesview", e)
//     }
//     return res;
// }
// export function LeadView(custid, leadid, productid,userId) {
//     //let baseurl = config.api.ServiceAPI;
//     let res;
//     try {
//         //baseurl = window.parent.location.href 
//         let baseurl = document.referrer;
//         if(baseurl != "") {
//             res = baseurl + "pgv/newsv/Leadview/"+ window.btoa(custid + "/" + productid + "/" + leadid + "//"+userId);
         
//         }  
//     }
//     catch (e) {
//         console.error("Unable to open salesview", e)
//     }
    
//     return res;
// }

export function onexport (data, name) { // On Click Excel download button
    
    // export json to Worksheet of Excel
    // only array possible
    let sheet_Data= []
    // for(let i=0; i<data ; i++){
        sheet_Data = XLSX.utils.json_to_sheet(data) 
    // }
    
    // var pokemonWS = XLSX.utils.json_to_sheet(pokemons) 

    // A workbook is the name given to an Excel file
    var wb = XLSX.utils.book_new() // make Workbook of Excel

    // add Worksheet to Workbook
    // Workbook contains one or more worksheets
 
    // for(let i=0; i<data.length ; i++){
        XLSX.utils.book_append_sheet(wb, sheet_Data, 'Sheet')
    // }

    // export Excel file
    if(name){
        XLSX.writeFile(wb, `${name}.xlsx`) // name of the file is 'book.xlsx'
    }
    else {
        XLSX.writeFile(wb, "Download.xlsx") // name of the file is 'book.xlsx'
    }
   
}

// export function hhmmss(secs) {
//     var minutes = Math.floor(secs / 60);
//     secs = secs % 60;
//     var hours = Math.floor(minutes / 60)
//     minutes = minutes % 60;
//     return `${("0" + hours).slice(-2)} h, ${("0" + minutes).slice(-2)} m, ${("0" + secs).slice(-2)} s`;
// }

export function getuser() {

    try {

        let userid = getUrlParameter("u") === "" ? getCookie("AgentId") : getUrlParameter("u");

        let user = JSON.parse(localStorage.getItem("Users-" + userid))
        if (user && user.length > 0) {
            user[0].EmployeeId = user[0].EmployeeId.trim();
            return user[0];
        }
        else
            return {};
    } catch (error) {
        console.error(error);
        return {};
    }
}

export function getRMSUser() {

    try {
        let Info = localStorage.getItem("PBRMSToken") || null;
        if(Info) {
            let user = JSON.parse(window.atob(Info)) || {}
            return user;
        }
    } catch (error) {
        console.error(error);
        return {};
    }
}

// export function setCoo;kie(cname, cvalue, exdays) {
//     var d = new Date();
//     d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
//     var expires = "domain=.policybazaar.com;expires=" + d.toUTCString();
//     document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
//     var expiresae = "domain=.policybazaar.ae;expires=" + d.toUTCString();
//     document.cookie = cname + "=" + cvalue + ";" + expiresae + ";path=/";
// }

export function getCookie(cname) {
    var name = cname + "=";
    var decodedCookie = decodeURIComponent(document.cookie);
    var ca = decodedCookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) === ' ') {
            c = c.substring(1);
        }
        if (c.indexOf(name) === 0) {
            return c.substring(name.length, c.length);
        }
    }
    return "";
}

// export function getDifferenceInMinFromNow(date){
//     var now = moment().format("YYYY-MM-DD HH:mm:ss");
//     var then = moment.utc(new Date(date)).format("YYYY-MM-DD HH:mm:ss");
//     var diff = (moment(now,"YYYY-MM-DD HH:mm:ss").diff(moment(then,"YYYY-MM-DD HH:mm:ss")))/ (1000 * 60);
//     return diff;
// }

// export function getExtension(filename) {
//     var parts = filename.split('.');
//     return parts[parts.length - 1];
// }

// export function IsVideo(filename) {//debugger;
//     var ext = getExtension(filename);
//     switch (ext?.toLowerCase()) {
//       case 'm4v':
//       case 'avi':
//       case 'mpg':
//       case 'mp4':
//       case 'webm':
//       case 'mov':
//         // etc
//         return true;
//     }
//     return false;
//   }

// export function IsImage(filename) {
//     var ext = getExtension(filename);
//     switch (ext?.toLowerCase()) {
//         case 'gif':
//         case 'jpeg':
//         case 'png':
//         case 'jpg':
//         // etc
//         return true;
//     }
//     return false;
// }

// export function IsValidPdf(filename) {
//     var ext = getExtension(filename);
//     switch (ext?.toLowerCase()) {
//         case 'pdf':
      
//         // etc
//         return true;
//     }
//     return false;
// }

// export function checkFileSize (file, max_limit){
//     let size2= 1024*1024;
//     let size= Math.floor(file.size/size2);
//     if(size>=max_limit)
//     {
//         return true
//     }
//     else{
//         return false
//     }
// }

  