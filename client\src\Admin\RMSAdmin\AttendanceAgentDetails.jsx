import React, { useState, useEffect} from 'react';

import moment from 'moment';
import "../app/css/AttendanceDashboard.scss"

import EmployeeDetails from './EmployeeDetails'

const AttendanceAgentDetails = (props) => {
    const {refAgents, rosterDates, agentLeavesData, attendanceAgentWiseData, eligibleDates, toggleView} = props
    const currentDate = moment(new Date()).format('YYYY-MM-DD');

    return (Array.isArray(refAgents) && refAgents?.map((item, ind1) => {
        let agentInfo = item;
        // const { EmployeeId, EmployeeName, UserType, TLName, TLEcode } = agentInfo;
        return <EmployeeDetails agentInfo={agentInfo} rosterDates={rosterDates} agentLeavesData = {agentLeavesData}
        attendanceAgentWiseData = {attendanceAgentWiseData} eligibleDates = {eligibleDates} 
        toggleView = {toggleView} />
        // return <tr key={EmployeeId}>
        //   <td>{EmployeeId || '-'}</td>
        //   {/* <td>{EmployeeName?.substring(0, 20) + '...' || '-'}</td> */}
        //   <td>{EmployeeName || '-'}{UserType == 'GOLD' || UserType == 'SILVER' ? '*' : ''}</td>
        //   <td className="HideColumn">{TLName || '-'}</td>
        //   <td className="HideColumn">{TLEcode || '-'}</td>
    
        //   {Array.isArray(rosterDates) && rosterDates.map((date, ind2) => {
        //     let data = agentLeavesData[EmployeeId] || [];
    
        //     let isPresent = GetAgentAttendanceOnDate(attendanceAgentWiseData[EmployeeId], date);
        //     let isEliglible = toggleView && GetAgentEligibleOnDate(eligibleDates[EmployeeId], date);
    
        //     let isLeave = false, leaveType = '';
        //     let isDatePast = moment(date).isSameOrBefore(currentDate)
    
        //     for (let index = 0; index < data.length; index++) {
        //       const element = data[index];
        //       const applicationDate = moment(element?.ApplicationDate).format('YYYY-MM-DD');
        //       if (date === applicationDate) {
        //         isLeave = true;
        //         leaveType = element.LeaveTypeId;
        //       }
        //     }
        //     let Rank = 0;
        //     if (data[0]?.Week1) {
        //       Rank = data[0]?.Week1;
        //     } else {
        //       Rank = data[0]?.Week2;
        //     }
    
        //     if (isLeave) {
        //       switch (leaveType) {
        //         case 1:
        //           return <td className={isPresent ? "weeklyOf AgentUnplanned" : "weeklyOf"}>{isPresent ? `W/O / P ${toggleView && Rank>0 ? `(${Rank})` : ''}` : `W/O ${toggleView ? `(${Rank})` : ''}`}</td>
        //         case 2:
        //           return <td className={isPresent ? "earnedLeave AgentUnplanned" : "earnedLeave"}>{isPresent ? "EL / P" : "EL"}</td>
        //         case 3:
        //           return <td>E. W/O</td>
        //         case 4:
        //           return <td>SL</td>
        //         case 5:
        //           return <td className={isPresent ? "causalLeave AgentUnplanned" : "causalLeave"} >{isPresent ? "CL / P" : "CL"}</td>
        //         // default:
        //         //   return <td className='AgentPresent'>P</td>
        //       }
    
        //     } else {
        //       if (toggleView && isEliglible) {
        //         // return <td className={isEliglible && "eligibleDates" } >{isPresent ? "P" : "AB"}</td>
        //         if (isDatePast && isPresent !== null) {
        //           return <td className='eligibleDates' >{isPresent ? "P*" : "AB*"}</td>
        //         } else {
        //           // return <td className={isPresent ? "AgentPresent" : "AgentAbsent" } >{isPresent ? "P" : "AB" }</td>
        //           return <td className='eligibleDates'>P*</td>
        //         }
    
        //       }
        //       else {
        //         if (isDatePast && isPresent !== null) {
        //           return <td className={isPresent ? "AgentPresent" : "AgentAbsent"} >{isPresent ? "P" : "AB"}</td>
        //         } else {
        //           // return <td className={isPresent ? "AgentPresent" : "AgentAbsent" } >{isPresent ? "P" : "AB" }</td>
        //           return <td className='AgentPresent'>P</td>
        //         }
        //       }
    
        //     }
        //   })
        //   }
        // </tr>
      }))
}

export default AttendanceAgentDetails;
