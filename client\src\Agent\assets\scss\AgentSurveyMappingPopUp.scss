/*Start CSAT rating css */
.AgentSurveyMappingPopUp {
  // .MuiDialog-paperWidthSm {
  //     padding: 0px;
  //     width: 348px;
  //     height: 530px;
  //     border-radius: 16px;

  // .MuiTypography-h6 {
  //     text-align: left;
  //     font: normal normal bold 16px/21px Roboto;
  //     letter-spacing: 0.22px;
  //     color: #303030;
  //     opacity: 1;
  // }
  padding: 15px;
  .nav{
      justify-content: space-evenly;
  }
  .nav-tabs{
      border-bottom: none;
  }
  .nav-tabs .nav-link{
      text-align: left;
      font: normal normal 600 12px/16px Roboto;
      letter-spacing: 0px;
      color: #A5B6C7;
      opacity: 1;
  }
  .active{
      color: #0065FF !important;
      border: none !important;
      border-bottom: 2px solid #0065ff !important;
  }
  .tab-content{
      padding: 35px 0px;
      .active{
          background-color: transparent !important;
          border-bottom: none !important;
      }
  }
  // .MuiTabs-flexContainer {
  //     align-content: center;
  //     justify-content: center;
  // }

      // .MuiTableContainer-root {
      //     box-shadow: none;
      // }
      .ratingdata {
          thead {
              background: #e5efff 0% 0% no-repeat padding-box;
              border-radius: 26px;
              opacity: 1;

              th {
                  text-align: center;
                  font: normal normal 600 12px/16px Roboto;
                  letter-spacing: 0px;
                  color: #253858;
                  opacity: 1;
                  border: none;
                  padding: 10px;
                  &:first-child {
                      border-top-left-radius: 20px;
                      border-bottom-left-radius: 20px;
                  }
                  &:last-child {
                      border-top-right-radius: 20px;
                      border-bottom-right-radius: 20px;
                  }
              }
          }
          tr {
              td {
                  border: none;
                  background: #fafafa 0% 0% no-repeat padding-box;
                  text-align: center;
                  padding: 10px 20px 10px 20px;
                  font: normal normal normal 12px/16px Roboto;
                  letter-spacing: 0px;
                  color: #253858;
                  opacity: 1;
                  &:first-child {
                      border-top-left-radius: 20px;
                      border-bottom-left-radius: 20px;
                      font: normal normal 600 12px/16px Roboto;
                      text-align: left;
                  }
                  &:last-child {
                      border-top-right-radius: 20px;
                      border-bottom-right-radius: 20px;
                      text-align: right;
                  }
              }
              &:nth-child(odd) {
                  td {
                      background: #fff 0% 0% no-repeat padding-box;
                      padding: 15px 20px 15px 20px;
                  }
              }
          }
      }
  



  .RatingStatus {
      font: normal normal 600 16px/21px Roboto;
      letter-spacing: 0px;
      color: #253858;
      opacity: 1;
      width: 80px;
      text-align: center;
  }
  .criteriaPoint {
      h4 {
          text-align: left;
          font: normal normal bold 14px/19px Roboto;
          letter-spacing: 0px;
          color: #253858;
          margin-bottom: 20px;
      }
      ul {
          list-style-type: decimal;
          li {
              text-align: left;
              font: normal normal normal 14px/19px Roboto;
              letter-spacing: 0px;
              color: #253858;
              margin-bottom: 20px;
          }
      }
  }
  .ratingMsg {
      text-align: justify;
      font: normal normal normal 12px/16px Roboto;
      letter-spacing: 0px;
      color: #253858;
      padding-left: 12px;
  }
  hr {
      background-color: #dddddd7d;
      width: 1px;
      height: 105px;
      border: none;
      float: left;
      position: relative;
      top:-20px;
  }
  h2 {
      text-align: left;
      font: normal normal normal 32px/19px Roboto;
      letter-spacing: 0px;
      color: #253858;
      margin-bottom: 5px;
      margin-right: 0px;
      padding-left: 12px;
      span {
          font: normal normal normal 14px/19px Roboto;
      }
  }
  // }

  .agentSurveyBox{
      .select-panel{
          ul{
              li{
                  margin-bottom: 0px;

                  label{
                      margin-bottom: 0px;
                  }
              }
          }
      }
  }
}