const tblList = require("../constants");
const methods = require("./DialerApiMethods");
let fs = require('fs');
const path = require("path");
const moment = require("moment");



async function setAgentStatus(req, res) {
    try {
       await methods.setAgentStatus(req, res);
    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}
async function multi_conference(req, res) {
    try {
        await methods.multi_conference(req, res);
    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}
async function mob2mobcall(req, res) {
    try {
      await  methods.mob2mobcall(req, res);
    } catch (err) {
        console.log(err);
        res.send({
            status: 500,
            error: err
        });
    }
    finally {
        // res.send({
        //     status: 200,
        //     message: 'Records are in process.'
        // });
        //await sql.close();
    }
}


module.exports = {
    setAgentStatus: setAgentStatus,
    multi_conference: multi_conference,
    mob2mobcall
};