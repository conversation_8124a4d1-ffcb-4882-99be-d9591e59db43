* {
    margin: 0px;
    padding: 0px;
}
*::-webkit-scrollbar {
    width: 8px;
    height: 10px;
}

*::-webkit-scrollbar-thumb {
    margin-right: 10px;
    border-radius: 10px;
    box-shadow: inset 0 0 0 10px;
    color: #d6d4d4;

    :hover {
        color: rgba(0, 0, 0, 0.3);
    }
}
.theme-light {
    background-color: #fff;
}
.mt-20{
margin: 20px;
}
.approvalDashboard {
    padding: 0px 20px 0px 0px;

    .Approvalmenu {
        width: 100%;
        border-right: 1px solid #ddd;
        padding: 15px;
        height: 100vh;

        p {
            color: rgba(37, 56, 88, 0.60);
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;

        }

        img {
            margin-bottom: 40px;
        }

        ul {
            list-style-type: none;

            li {
                color: #253858;
                font-family: Roboto;
                font-size: 14px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
                padding: 13px 18px;
                margin-bottom: 5px;
            }

            .active {
                border-radius: 8px;
                background: rgba(0, 101, 255, 0.05);
                color: #0065FF;

            }
        }
    }

    .ApprovalDetails {
        margin-top: 1.2em;

        table {
            th {
                color: rgba(37, 56, 88, 0.60);
                font-family: Roboto;
                font-size: 13px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
                border-color: #7070701f;
                padding:2px 8px 2px 0px;
                background-color: transparent;
            }

            td {
                color: rgba(37, 56, 88, 0.89);
                font-family: Roboto;
                font-size: 14px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
                border-color: #7070701f;
                padding:2px 8px 2px 0px;

            }

            button {
                font-family: Roboto;
                font-size: 14px;              
                font-style: normal;
                font-weight: 600;
                line-height: 15px;
                border: none;
                border-radius: 24px;
                background-color: transparent;
            }

            .Pending{
                
                color: #FEC617;
            }
            .Approved{
                color: #397444;
             
            }
            .Rejected{
                color: #CA2626;
               
            }
        }

        .BottomBtn {
            position: absolute;
            bottom: 20px;
            right: 30px;

            button {
                color: #FFF;
                font-family: Roboto;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                border-radius: 8px;
                border: none;
                cursor: pointer;
            }

            .GetDetailsBtn {
                background: #0065FF;
                padding: 13px 34px;
            }

            .FreezeBtn {
                background-color: #FF2C2C;
                padding: 13px 40px;
                margin-left: 10px;
            }

            .disable {
                opacity: 0.3;
            }
        }
    }
}