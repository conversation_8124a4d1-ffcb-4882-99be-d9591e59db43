import {
  ADD_SURVEY,
  ADD_RESPONSE,
  ADD_CURRENT_NESTED_QUESTION
} from './SurveyActionTypes';

export const AddSurvey = (data) => {
  return {
    type: ADD_SURVEY,
    payload: data
  }
}

export const AddResponse = (data, Type) => {
  return {
    type: ADD_RESPONSE,
    payload: { ...data, Type }
  }
}

export const ChangeCurrentNestedQuestion = (data) => {
  return {
    type: ADD_CURRENT_NESTED_QUESTION,
    payload: data
  }
}

 