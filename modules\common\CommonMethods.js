const CryptoJS = require("crypto-js");
const crypto = require('crypto');
const config = require('../../env_config');
const AWS = require('aws-sdk');
const Algorithm = 'aes-256-cbc';
const cache = require('memory-cache');

const IsJsonString = (str) => {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
}

const GetDataFromCache = ({ key }) => {
  try {
    let data = cache.get(key);
    return data;
  } catch (err) {
    console.log('Inside GetDataFromCache', err);
    return null
  }
}

const AesEncryption = ({ data, key, iv }) => {
  try {
    let secret_key = key;
    secret_key = CryptoJS.enc.Base64.parse(secret_key);
    secret_key = CryptoJS.enc.Utf8.stringify(secret_key);

    let secret_iv = iv;
    secret_iv = CryptoJS.enc.Base64.parse(secret_iv);
    secret_iv = CryptoJS.enc.Utf8.stringify(secret_iv);

    let cipher = crypto.createCipheriv(Algorithm, secret_key, secret_iv);
    let encrypted = cipher.update(data.toString());
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    return encrypted.toString('base64');

  } catch (err) {
    console.log('Inside AesEncryption: ', err);
  }
}


const GetAWSSecretData = async ({ SecretId }) => {
  try {
    const SecretManager = new AWS.SecretsManager({
      region: config.AWS_REGION || 'ap-south-1'
    });

    const SecretData = await SecretManager.getSecretValue({ SecretId: SecretId }).promise();
    const Result = JSON.parse(SecretData.SecretString);
    return Result;
  } catch (err) {
    console.log('Inside GetAWSSecretData', err);
    return null;
  }
}

const GetParsedConfigFromCache = async ({ key }) => {
  try {
    let cachedData = cache.get(key);

    if (cachedData) {
      return cachedData;
    }
    else {
      const AWSSecretConfig = await GetAWSSecretData({ SecretId: config.AWS_ENV_CONFIG_SECRET })
      if (AWSSecretConfig) {
        let ParsedConfig = {};

        for (let key in AWSSecretConfig) {
          const IsJson = IsJsonString(AWSSecretConfig[key]);
          if (IsJson) {
            ParsedConfig[key] = JSON.parse(AWSSecretConfig[key]);
          } else {
            ParsedConfig[key] = AWSSecretConfig[key]
          }
        }
        cache.put(key, ParsedConfig, (24 * 60 * 60 * 1000));
        return ParsedConfig;
      } else {
        return null
      }
    }
  } catch (err) {
    console.log("Inside GetParsedConfigFromCache", err)
    return null;
  }
}

const GetParsedConfigLocal = async () => {
  try {
    let data = JSON.parse(process.env.CONNECTION || '{}');
    return data;
  } catch (err) {
    console.log('Inside GetParsedConfigLocal', err);
    return null
  }
}

module.exports = {
  AesEncryption: AesEncryption,
  GetAWSSecretData: GetAWSSecretData,
  GetParsedConfigFromCache: GetParsedConfigFromCache,
  GetDataFromCache: GetDataFromCache,
  GetParsedConfigLocal: GetParsedConfigLocal
}
