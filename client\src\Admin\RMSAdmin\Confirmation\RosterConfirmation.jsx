import React, { useState, useEffect } from 'react';
import "../../../Admin/RMSAdmin/Confirmation/RosterConfirmation.scss"
import { Container, FormControl, Grid, MenuItem, Select, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tooltip, Typography } from '@mui/material';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import TransferPopup from './TransferPopup';
import HistoryIcon from '@mui/icons-material/History';
// import RemovePopup from './RemovePopup';
import RemovePopup from './RemovePopup'
import AddAdvisorPopup from './AddAdvisorPopup';
import moment from 'moment';
import {
    GetCommonData, GetCommonspData, GetCommonspDataV2
} from "../../store/actions/CommonAction";
import { connect } from "react-redux";
import _ from 'underscore';
import { useNavigate } from "react-router-dom";
import {
    getTLRosterData, FreezedTlData, DeleteData
} from "../../store/actions/CommonAction";
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogTitle from '@mui/material/DialogTitle';



const RosterConfirmation = (props) => {

    const navigate = useNavigate();
    const [open, setOpen] = useState(false);
    const [openremove, setRemoveOpen] = useState(false);
    const [openAdvisor, setAdvisorOpen] = useState(false);
    const [freezeOpen, setFreezeOpen] = useState(false);
    const [productName, setProductName] = useState();
    const [rosterDates, setRosterDates] = useState();
    const [processList, setProcessList] = useState([]);
    const [userList, setUserList] = useState([]);
    const [transferData, setTransferData] = useState({});
    const [freezed, setFreezed] = useState();
    const [message, setMessage] = useState(1);
    const [eventDate, setEventDate] = useState();
    const [historyData, setHistoryData] = useState([]);
    const [filterData, setFilterData] = useState([]);


    const handleTransferOpen = (value) => {
        // console.log(e.target.value); 
        setTransferData(value);
        setOpen(true);
    };
    const handleClose = (e) => {
        setOpen(false);
    };
    const handleRemoveClickOpen = (value) => {
        setTransferData(value);
        setRemoveOpen(true);
    };
    const handleRemoveClose = () => {
        setRemoveOpen(false);
    };
    const handleAdvisorClickOpen = () => {
        setAdvisorOpen(true);
    };
    const handleAdvisorClose = () => {
        setAdvisorOpen(false);
    };

    useEffect(() => {
        getRosterData();
        TLRosterStatusData();
    }, [])

    const TLRosterStatusData = () => {
        props.GetCommonspDataV2({
            root: 'GetTLRosterStatusData',
            c: "R",
        }, function (errorStatus, response) {
            if (!errorStatus) {
                if (response?.data?.[0] && response?.data?.[0]) {
                    // setUserData(response?.data?.[0]);
                    setHistoryData(response?.data?.[0]);
                    let userData = response?.data?.[0];
                    // setActiveClass('Pending');
                    let PendingData = [];
                    for (let i = 0; i < userData.length; i++) {
                        if (userData[i].Status == 3) {
                            PendingData.push(userData[i]);
                        }
                    }
                    setFilterData(PendingData);
                }
            }
        })
    }

    const getRosterData = () => {
        try {
            getTLRosterData(function (result) {
                debugger;
                if (result && result.data && result.data.status == 200) {
                    // console.log(result);
                    let date = moment(result.data.results[0]?.StartDate).format('DD MMM YYYY') + ' - ' + moment(result.data.results[0]?.EndDate).format('DD MMM YYYY');
                    // let Processes = 
                    let userData = result.data.results;
                    let ProcessesList = _.uniq(userData, (item) => {
                        return item.ProcessId;
                    });

                    // console.log(ProcessesList);
                    let ProcessData = {};
                    for (let i = 0; i < userData.length; i++) {
                        if (!ProcessData[userData[i].ProcessId]) {
                            ProcessData[userData[i].ProcessId] = { ProcessName: userData[i].ProcessName, count: 0 };
                        }
                        else {

                        }
                        ProcessData[userData[i].ProcessId].count++;
                    }
                    console.log(ProcessData);

                    // let Processdata = _.groupBy(result[0], 'ProcessId') || {}

                    let Processdata = _.countBy(userData, 'ProcessName') || {};
                    console.log(Processdata);
                    // console.log(ProcessesList);
                    setProcessList(ProcessData)
                    // console.log(result);
                    setProductName(result.data.results[0]?.ProductName);
                    setEventDate(result.data.results[0]?.EventDate);
                    setRosterDates(date);
                    setUserList(result.data?.results);
                    setFreezed(result.data.results[0]?.IsFreezedByAdmin)
                }
            })
        }
        catch (ex) {

        }
    }

    const HandleChangeHistory = (e) => {
        navigate("/admin/rms/History", { state: historyData });
    }

    const handleFreeze = () => {
        // console.log();

        try {
            FreezedTlData(function (result) {
                if (result && result.data && result.data.status == 200) {
                    console.log(result.data.results[0].status);
                    let status = result.data.results[0].status
                    getRosterData();
                    TLRosterStatusData();
                    setMessage(status);
                    if (status == 1) {
                        setFreezeOpen(false);
                    }

                }
            })
        }
        catch (ex) {

        }
    }

    const handleDialogClickOpen = () => {
        setMessage(1);
        setFreezeOpen(true);
    };

    const handleDialogClose = () => {
        setFreezeOpen(false);
    };

    const undoAdvisor = (TransferId, RosterId) => {
        // let userId = e.target.value;
        props.DeleteData({
            root: 'TransferRequest',
            query: {"Id": TransferId, "RosterId": RosterId}
            
          }, function (data) {
            if(data.data.status === 200) {
                getRosterData();
                TLRosterStatusData();
            }
          });
    }

    return (
        <div className="RoasterConfirmation">
            <Container maxWidth="xl">
                <header>
                    <Typography variant="h2" className="Heading">Roster Confirmation</Typography>
                    {eventDate && <p>View, or update the confirmation of your roster before <b> {moment(eventDate).format('DD/MM/YY')} </b></p>}
                </header>
                <Grid container spacing={3} mt={2}>
                    <Grid item md={1.8} sm={6} xs={12}>
                        <label>Your BU</label>
                        {productName && <div className="BlueBgColor bgFrame">{productName}</div>}
                    </Grid>
                    <Grid item md={2.5} sm={6} xs={12}>
                        <label>Select Roster</label>
                        <FormControl fullWidth>
                            {rosterDates && <div className="BlueBgColor bgFrame">{rosterDates}</div>}
                            {/* <Select className="BlueBgColor">
                                <MenuItem value={2}>02 Oct 2023 - 15 Oct 2023</MenuItem>
                                <MenuItem value={3}>03 Oct 2023 - 15 Oct 2023</MenuItem>
                                <MenuItem value={4}>04 Oct 2023 - 15 Oct 2023</MenuItem>
                            </Select> */}
                        </FormControl></Grid>
                    <Grid item md={6} sm={6} xs={12}>
                        <label>Processes under you</label>
                        <Grid container spacing={2}>
                            {Object.keys(processList).map((process) =>
                            (
                                <Grid item md={3} sm={3} xs={6}>
                                    <Tooltip title={processList[process].ProcessName} arrow>
                                        <div className="BlueBgColor bgFrame Active">
                                            <p>{processList[process].ProcessName} </p><span className="badge">{processList[process].count}</span>
                                        </div>
                                    </Tooltip>
                                </Grid>

                            )
                            )}
                        </Grid>
                    </Grid>

                    <Grid item md={1.2} sm={6} xs={12}>
                        <div className="BlueBgColor bgFrame HistoryButton" onClick={HandleChangeHistory}> <HistoryIcon /> History</div>
                    </Grid>
                    <Grid item xs={12} md={12} sm={12}>

                        <TableContainer className="Rostertable">
                            <Table stickyHeader aria-label="customized table" >
                                <TableHead>
                                    <TableRow>
                                        <TableCell align="left">Employee name</TableCell>
                                        <TableCell align="left">E-Code</TableCell>
                                        <TableCell align="left">Process</TableCell>
                                        {!freezed && <TableCell align="left">Action</TableCell>}
                                    </TableRow>
                                </TableHead>
                                <TableBody>

                                    {userList.map((user, index) => (
                                        <TableRow>
                                            {/* <TableCell align="left">{index+1}</TableCell> */}
                                            <TableCell align="left">{user.EmployeeName}</TableCell>
                                            <TableCell align="left">{user.EmployeeId}</TableCell>
                                            <TableCell align="left">{user.ProcessName}</TableCell>
                                            {!freezed && <TableCell align="left">
                                                <button className="TransferBtn" onClick={() => { handleTransferOpen(JSON.stringify(user)) }} value={JSON.stringify(user)} >  <img src="/rms/move_down.svg" value={JSON.stringify(user)} /> Transfer</button>
                                                <button className="RemoveBtn" onClick={() => { handleRemoveClickOpen(JSON.stringify(user)) }} value={JSON.stringify(user)}> <img src="/rms/delete.svg" />Remove</button>
                                            </TableCell>}

                                        </TableRow>
                                    )
                                    )}



                                    {/* <TableRow>
                                        <TableCell align="left">Vineet Yadav</TableCell>
                                        <TableCell align="left">PW37633</TableCell>
                                        <TableCell align="left">Family floater</TableCell>
                                        <TableCell align="left">
                                            <button className="TransferBtn" onClick={handleClickOpen}>  <img src="/rms/move_down.svg" /> Transfer</button>
                                            <button className="RemoveBtn" onClick={handleRemoveClickOpen}> <img src="/rms/delete.svg" />Remove</button>
                                        </TableCell>

                                    </TableRow>
                                    <TableRow>
                                        <TableCell align="left">Gunjan Sharma</TableCell>
                                        <TableCell align="left">ET01843</TableCell>
                                        <TableCell align="left">Family floater</TableCell>
                                        <TableCell align="left">
                                            <button className="TransferBtn"><img src="/rms/move_down.svg" /> Transfer</button>
                                            <button className="RemoveBtn"><img src="/rms/delete.svg" />Remove</button>
                                        </TableCell>
                                    </TableRow>
                                    <TableRow>
                                        <TableCell align="left">Gunjan Sharma</TableCell>
                                        <TableCell align="left">PW37633</TableCell>
                                        <TableCell align="left">Family floater</TableCell>
                                        <TableCell align="left">
                                            <button className="TransferBtn"><img src="/rms/move_down.svg" /> Transfer</button>
                                            <button className="RemoveBtn"><img src="/rms/delete.svg" />Remove</button>
                                        </TableCell>
                                    </TableRow>
                                    <TableRow>
                                        <TableCell align="left">Gunjan Sharma</TableCell>
                                        <TableCell align="left">ET01843</TableCell>
                                        <TableCell align="left">Family floater</TableCell>
                                        <TableCell align="left">
                                            <button className="TransferBtn"><img src="/rms/move_down.svg" /> Transfer</button>
                                            <button className="RemoveBtn"><img src="/rms/delete.svg" />Remove</button>
                                        </TableCell>
                                    </TableRow>
                                    <TableRow>
                                        <TableCell align="left">Gunjan Sharma</TableCell>
                                        <TableCell align="left">PW37633</TableCell>
                                        <TableCell align="left">Family floater</TableCell>
                                        <TableCell align="left">
                                            <button className="TransferBtn"><img src="/rms/move_down.svg" /> Transfer</button>
                                            <button className="RemoveBtn"><img src="/rms/delete.svg" />Remove</button>
                                        </TableCell>
                                    </TableRow>
                                    <TableRow>
                                        <TableCell align="left">Gunjan Sharma</TableCell>
                                        <TableCell align="left">PW37633</TableCell>
                                        <TableCell align="left">Family floater</TableCell>
                                        <TableCell align="left">
                                            <button className="TransferBtn"><img src="/rms/move_down.svg" /> Transfer</button>
                                            <button className="RemoveBtn"><img src="/rms/delete.svg" />Remove</button>
                                        </TableCell>
                                    </TableRow>
                                    <TableRow>
                                        <TableCell align="left" colSpan={4}>&nbsp;</TableCell>


                                    </TableRow> */}
                                    {/* <TableRow>
                                        <TableCell align="left" colSpan={4}>&nbsp;</TableCell>
                                    </TableRow> */}
                                </TableBody>
                            </Table>
                        </TableContainer>


                        {filterData && filterData.length > 0 && <><Grid item md={1} sm={2} xs={2}>
                            <button className="Rostertable BlueBgColor bgFrame Active mt-1">Pending </button>
                        </Grid><TableContainer className="Rostertable History" sx={{ maxHeight: 320 }}>
                                {/* <Table stickyHeader aria-label="sticky table"> */}
                                <Table >
                                    <TableHead>
                                        <TableRow>
                                            <TableCell align="left">Employee name</TableCell>
                                            <TableCell align="left">E-Code</TableCell>
                                            <TableCell align="left">Process</TableCell>
                                            <TableCell align="left">Request type</TableCell>
                                            <TableCell align="left">Transfer process to</TableCell>
                                            <TableCell align="left">Comments</TableCell>
                                            <TableCell align="left">Status</TableCell>
                                            <TableCell align="left" className="width130px">Undo</TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {filterData && filterData.map((users) => <TableRow>
                                            <TableCell align="left">{users.EmployeeName}</TableCell>
                                            <TableCell align="left">{users.EmployeeId}</TableCell>
                                            <TableCell align="left">{users.ProcessName}</TableCell>
                                            <TableCell align="left">{users.RequestName}</TableCell>
                                            <TableCell align="left" className="Null">{users.TransferProcessTo}</TableCell>
                                            <TableCell align="left" className="Null" title={users.MisComments}>{users.MisComments}</TableCell>
                                            <TableCell align="left"><button className="StatusButton Approved">{users.StatusName}</button></TableCell>
                                            {users.Status == 3 && <TableCell align="left">
                                                <button className="UndoBtn" onClick={() => undoAdvisor(users.TransferId, users.RosterId)}> Undo</button>
                                            </TableCell>}
                                        </TableRow>
                                        )}
                                    </TableBody>
                                </Table>

                            </TableContainer></>}

                          <div className="heightspace"></div>              
                        {!freezed && <div className="BottomBtn">
                            <button className="AddBtn" onClick={handleAdvisorClickOpen}><AddCircleOutlineIcon /> Add</button>
                            {/* <button className="FreezeBtn" onClick = {handleFreeze}>Freeze</button> */}
                            <Button className="FreezeBtn" variant="outlined" onClick={handleDialogClickOpen}>
                                Freeze
                            </Button>
                            <Dialog
                                open={freezeOpen}
                                onClose={handleClose}
                                aria-labelledby="alert-dialog-title"
                                aria-describedby="alert-dialog-description"
                            >
                                <DialogTitle id="alert-dialog-title">
                                    {message ? "Once data Freeze can't be changed" : "Connect with Mis Team"}
                                </DialogTitle>

                                <DialogActions>
                                    <Button onClick={handleDialogClose}>Close</Button>
                                    {message && <Button onClick={handleFreeze} autoFocus>
                                        Save
                                    </Button>}
                                </DialogActions>
                            </Dialog>
                        </div>}
                    </Grid>
                </Grid>
            </Container>
            <TransferPopup open={open} onClose={handleClose} transferData={transferData} processList={processList} getRosterData={getRosterData} TLRosterStatusData = {TLRosterStatusData} />
            <RemovePopup open={openremove} onClose={handleRemoveClose} transferData={transferData} getRosterData={getRosterData} TLRosterStatusData = {TLRosterStatusData} />
            <AddAdvisorPopup open={openAdvisor} onClose={handleAdvisorClose} processList={processList} getRosterData={getRosterData} TLRosterStatusData = {TLRosterStatusData} />
        </div>
    );
};

// export default RosterConfirmation;
function mapStateToProps(state) {
    return {
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonData,
        GetCommonspData,
        GetCommonspDataV2,
        DeleteData
    }
)(RosterConfirmation);