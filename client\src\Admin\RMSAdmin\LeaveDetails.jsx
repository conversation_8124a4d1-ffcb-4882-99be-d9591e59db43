import React, { useState, useEffect } from 'react';

import moment from 'moment';
import "../app/css/AttendanceDashboard.scss"

import { GetAgentAttendanceOnDate, GetAllManagerIds, GetAgentEligibleOnDate } from './Common/Utilities';

const LeaveDetails = (props) => {
  // console.log(props);

  const { agentInfo, rosterDates, agentLeavesData, attendanceAgentWiseData, eligibleDates, toggleView, date } = props;

  let data = agentLeavesData[agentInfo.EmployeeId] || [];


  let isPresent = GetAgentAttendanceOnDate(attendanceAgentWiseData[agentInfo.EmployeeId], date);
  let isEliglible = toggleView && GetAgentEligibleOnDate(eligibleDates[agentInfo.EmployeeId], date);

  let isLeave = false, leaveType = '', statusName = '', color = '';
  let isDatePast = moment(date).isSameOrBefore(agentInfo.currentDate)

  for (let index = 0; index < data.length; index++) {
    const element = data[index];
    const applicationDate = moment(element?.ApplicationDate).format('YYYY-MM-DD');
    if (date === applicationDate) {
      isLeave = true;
      leaveType = element.LeaveTypeId;
      statusName = element.StatusName;
      color = element.Color;
    }
  }
  let Rank = 0;
  if (data[0]?.Week1) {
    Rank = data[0]?.Week1;
  } else {
    Rank = data[0]?.Week2;
  }

  if (isLeave) {
    switch (leaveType) {
      case 1:
        return <td className={isPresent ? "weeklyOf AgentUnplanned" : "weeklyOf"}>{isPresent ? `W/O / P ${toggleView && Rank > 0 ? `(${Rank})` : ''}` : `W/O ${toggleView ? `(${Rank})` : ''}`}</td>
      case 2:
        return <td style={!isPresent ? { backgroundColor: color } : {}} className={isPresent ? "earnedLeave AgentUnplanned" : ""}> 
          {isPresent ? "EL / P" : `EL - ${statusName}`}</td>
      case 3:
        return <td style={!isPresent ? { backgroundColor: color } : {}} className={isPresent ? "earnedLeave AgentUnplanned" : ""} >{isPresent ? "CL / P" : `CL-${statusName}`}</td>
      case 4:
        // return <td>SL</td>
        return <td style={!isPresent ? { backgroundColor: color } : {}} className={isPresent ? "earnedLeave AgentUnplanned" : ""} >{isPresent ? "SL / P" : `SL - ${statusName}`}</td>
      // case 5:
      //   return <td className={isPresent ? "causalLeave AgentUnplanned" : "causalLeave"} >{isPresent ? "CL / P" : "CL"}</td>
      case 10:
        return <td className={isPresent ? "earnedLeave AgentUnplanned" : "earnedLeave"} >{isPresent ? "LWP / P" : `LWP-${statusName}`}</td>
      case 21:
        return <td className={isPresent ? "earnedLeave AgentUnplanned" : "earnedLeave"} >{isPresent ? "OD / P" : `OD - ${statusName}`}</td>
        // default:
      //   return <td className='AgentPresent'>P</td>
   
    // switch (leaveType) {
    //   case 1:
    //     return <td className={isPresent ? "weeklyOf AgentUnplanned" : "weeklyOf"}>{isPresent ? `W/O / P ${toggleView && Rank > 0 ? `(${Rank})` : ''}` : `W/O ${toggleView ? `(${Rank})` : ''}`}</td>
    //   case 2:
    //     return <td className={isPresent ? `${data[0]?.Color} AgentUnplanned` : `${data[0].Color}`}>{isPresent ? "EL / P" : "EL"}</td>
    //   case 3:
    //     return <td>E. W/O</td>
    //   case 4:
    //     return <td>SL</td>
    //   case 5:
    //     return <td className={isPresent ? "causalLeave AgentUnplanned" : "causalLeave"} >{isPresent ? "CL / P" : "CL"}</td>
    //   // default:
    //   //   return <td className='AgentPresent'>P</td>
    }

  } else {
    if (toggleView && isEliglible) {
      // return <td className={isEliglible && "eligibleDates" } >{isPresent ? "P" : "AB"}</td>
      if (isDatePast && isPresent !== null) {
        return <td className='eligibleDates' >{isPresent ? "P*" : "AB*"}</td>
      } else {
        // return <td className={isPresent ? "AgentPresent" : "AgentAbsent" } >{isPresent ? "P" : "AB" }</td>
        return <td className='eligibleDates'>P*</td>
      }

    }
    else {
      if (isDatePast && isPresent !== null) {
        return <td className={isPresent ? "AgentPresent" : "AgentAbsent"} >{isPresent ? "P" : "AB"}</td>
      } else {
        // return <td className={isPresent ? "AgentPresent" : "AgentAbsent" } >{isPresent ? "P" : "AB" }</td>
        return <td className='AgentPresent'>P</td>
      }
    }

  }

}

export default LeaveDetails;

