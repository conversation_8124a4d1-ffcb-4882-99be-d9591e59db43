import CloseIcon from '@mui/icons-material/Close';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import MuiDialogActions from '@mui/material/DialogActions';
import MuiDialogContent from '@mui/material/DialogContent';
import MuiDialogTitle from '@mui/material/DialogTitle';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { Grid, Switch, TextField, styled } from '@mui/material';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';
import { useState } from 'react';

import HistoryIcon from '@mui/icons-material/History';
const DialogTitleRoot = styled(MuiDialogTitle)(({ theme }) => ({
  margin: 0,
  padding: theme.spacing(2),
  '& .closeButton': {
    position: 'absolute',
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500]
  }
}));
const label = { inputProps: { 'aria-label': 'Switch demo' } };
const DialogTitle = (props) => {
  const { children, onClose } = props;
  return (
    <DialogTitleRoot disableTypography>
      {/* <div className="header">
        History
        <HistoryIcon />
      </div> */}
      <Typography variant="h6">Comments</Typography>
      {onClose ? (
        <IconButton aria-label="Close" className="closeButton" onClick={onClose}>
          <CloseIcon />
        </IconButton>
      ) : null}
    </DialogTitleRoot>
  );
};

const DialogContent = styled(MuiDialogContent)(({ theme }) => ({
  '&.root': { padding: theme.spacing(2) }
}));

const DialogActions = styled(MuiDialogActions)(({ theme }) => ({
  '&.root': { margin: 0, padding: theme.spacing(1) }
}));

const CommentPopup = (props) => {
  // const [open, setOpen] = useState(false);
  // const handleClickOpen = () => setOpen(true);
  // const handleClose = () => setOpen(false);

  // const {open} = props;


  const handleCommentChange = (e) => {
    props.setComments(e.target.value);
  }

  return (
    <div>
      {/* <Button variant="outlined" color="secondary" onClick={handleClickOpen}>
        Open dialog
      </Button> */}

      <Dialog onClose={props.handleClose} className="CommentPopup" open={props.open}>
        <DialogTitle id="customized-dialog-title" onClose={props.handleClose}>

        </DialogTitle>

        <DialogContent>
        {/* <div className="CommonComment"><p><LockOutlinedIcon/>Common Comments for all </p><Switch {...label} defaultChecked /></div> */}
            <Grid item xs={12} md={12}>
            {/* <label>Catherine, 07/05/23, PW37622</label> */}
            <TextField
              id="outlined-multiline-static"
              multiline
              rows={4}
              className="TextArea"
              onChange={handleCommentChange}
              placeholder="Type your comment here…"
            />
          </Grid>
          {/* <Grid item xs={12} md={12}>
            <label>Catherine, 07/05/23, PW37622</label>
            </Grid>
            <Grid item xs={12} md={12}>
            <TextField
              id="outlined-multiline-static"
              multiline
              className="TextArea"
              rows={4}
              placeholder="Type your comment here…"
            />
          </Grid>

         */}
          
        </DialogContent>

        <DialogActions>
          <Button onClick={props.handelApproval} color="primary" className="saveButton">
            Submit
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default CommentPopup;
