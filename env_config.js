const qa = {
    PORT: 8080,
    AGENT_CTC_BASE_URL: "https://hrmsqa.policybazaar.com/HRMS-Emp/api/EmpExt/GetAgentCTCDetails",
    AGENT_CTC_TOKEN: "g4cjZpWJEf1BntHoldTXkA==",
    CTC_INCENTIVE_URL: "https://incentiveapiqa.policybazaar.com/api/Incentive/DecryptData",
    AWS_REGION: "ap-south-1",
    AWS_CREDENTIALS_URL : 'http://169.254.169.254/latest/meta-data/iam/security-credentials/matrixlive-api-role',
    dialerApiUrlV2: "https://dialerqaapi.policybazaar.com/api/v2/",
    SERVICE_URL: 'https://pbserviceapi.policybazaar.com/sendcommunication',
    PB_SERVICE_TOKEN : "B5BED94D-9197-45CF-83D5-2534A1A8FEFD",
    BMSTicket : "https://ticketservice.policybazaar.com/",
    BMSToken : "630496192d0c283e0ed93838",
    B<PERSON>App : "MATRIX",
    BMSContent : "application/json",
    FOSAUTHKEY:"LGsaWLYmF6YWNav",
    FOSCLIENTKEY:"L6YWNav",
    VERIFY_TOKEN_MTX : 'https://internalcustomernotificationqa.policybazaar.com/customer/verifytoken/',
    VERIFY_TOKEN_BMS : 'https://bmsv2testservice.policybazaar.com/api/Utility/CheckUserLogin',
    VERIFY_TOKEN_DIALER : 'https://dialerapi.policybazaar.com/api/v2/dialer/validateToken',
    VERIFY_TOKEN_CLAIM : 'https://claimqaapi.policybazaar.com/Login/IsValidAgent',
    UUID_MTX: 'MatrixToken',
    UUID_BMS: 'bmsv2TokenQA',
    UUID_DIALER: 'd7f080c7-e134-459b-967d-389b71a9396f',
    UUID_CLAIM: 'ValidationKey',
    AGENTID_DIALER: 'c7fc71cf-2f0e-4da7-b0a3-72d285b0c2a6',
    AGENTID_CLAIM: 'ClaimEmployeeId',
    SOURCE_KEY_CLAIM: 'Dialer',
    VALIDATION_KEY_CLAIM: 'db3d8188-b687-49d3-a713-f33493ef4459',
    MATRIXCOREAPI:'https://qainternalmatrixapi.policybazaar.com',
    REGISTER_CUSTOMER: 'https://apiqa.policybazaar.com/cs/customer/registerCustomer',
    REGISTER_CUST_AUTHKEY: 'fw03VZNVHPln4VS94QljBt31il7LbrhmsBCGTkeI',
    REGISTER_CUST_CLIENTKEY: 'rYAhR07qvs',
    LMS_BASE_URL: 'https://lmsqa.policybazaar.com/',
    MATRIX_DASHBOARD_BASEURL: 'https://matrixdashboardqa.policybazaar.com',
    AWS_ENV_CONFIG_SECRET: "arn:aws:secretsmanager:ap-south-1:************:secret:QA_MatrixDashboard-Cf0Tf8",
    UnifyValidationApi : "https://pbqaunifyapps.policybazaar.com",
    UnifyApiToken : "1/677cc6d8ac7d3e1bbeaab8b0",
};

const prod = {
    PORT: 8080,
    AGENT_CTC_BASE_URL: "https://hrmsqa.policybazaar.com/HRMS-Emp/api/EmpExt/GetAgentCTCDetails",
    AGENT_CTC_TOKEN: "g4cjZpWJEf1BntHoldTXkA==",
    CTC_INCENTIVE_URL: "https://incentiveapi.policybazaar.com/api/Incentive/DecryptData",
    AWS_REGION: "ap-south-1",
    AWS_CREDENTIALS_URL : 'http://169.254.169.254/latest/meta-data/iam/security-credentials/matrixlive-api-role',
    dialerApiUrlV1: "http://dialerapi.policybazaar.com/api/dialer/",
    dialerApiUrlV2: "https://dialerapi.policybazaar.com/api/v2/",
    BMSTicket : "https://ticketservice.policybazaar.com/",
    BMSContent : "application/json",
    BMSToken : "cRfUjXn2r5u8xADGKaPdSgVkYp3s",
    BMSApp : "Matrix",
    FOSAUTHKEY:"LGsaWLYmF6YWNav",
    FOSCLIENTKEY:"L6YWNav",
    BMSToken : "YmU1OGZmMjAtMDExOC00NDc0LTk4NTgtNGIyZTQyODcwMzhlfjI3NDE=",
    BMSApp : "MATRIX",
    VERIFY_TOKEN_MTX : 'https://internalcustomernotification.policybazaar.com/customer/verifytoken/',
    VERIFY_TOKEN_BMS : 'https://bmsappservice.policybazaar.com/api/Utility/CheckUserLogin',
    VERIFY_TOKEN_DIALER : 'https://dialerapi.policybazaar.com/api/v2/dialer/validateToken',
    VERIFY_TOKEN_CLAIM : 'https://claimapi.policybazaar.com/Login/IsValidAgent',
    UUID_MTX: 'MatrixToken',
    UUID_BMS: 'bmsv2Token',
    UUID_DIALER: 'd7f080c7-e134-459b-967d-389b71a9396f',
    UUID_CLAIM: 'ValidationKey',
    SOURCE_KEY_CLAIM: 'Dialer',
    VALIDATION_KEY_CLAIM: 'db3d8188-b687-49d3-a713-f33493ef4459',
    AGENTID_DIALER: 'c7fc71cf-2f0e-4da7-b0a3-72d285b0c2a6',
    AGENTID_CLAIM: 'ClaimEmployeeId',
    MATRIXCOREAPI:'https://internalmatrixapi.policybazaar.com',
    REGISTER_CUSTOMER: 'https://api.policybazaar.com/cs/customer/registerCustomer',
    REGISTER_CUST_AUTHKEY: 'nlGpH1UxWGzIJfRXXFlyYPtf814HETXWX09tahQJ',
    REGISTER_CUST_CLIENTKEY: 'OlWfN8v7wF',
    LMS_BASE_URL: 'https://pblms.policybazaar.com/',
    MATRIX_DASHBOARD_BASEURL: 'https://matrixdashboard.policybazaar.com',
    AWS_ENV_CONFIG_SECRET: "arn:aws:secretsmanager:ap-south-1:************:secret:Prod_MatrixDashboard-DYjpH3",
    UnifyValidationApi : "https://pbcommevents.policybazaar.com/",
    UnifyApiToken : "3/689347e056d13176d938d15c",
};

const config = qa;

module.exports = {
    ...config
};