const sqlHelper = require("../../Libs/sqlHelper");

async function GetCourseQuizOptionMaster(QuestionID) {
  let result = [];
  try {

    let query = `
      Select QOM.OptionId, QOM.OptionText, QOM.IsCorrect, QNQM.NestedQuestionId,SerialNo from PBT.CourseQuizOptionMaster QOM (NOLOCK)
      LEFT JOIN PBT.CourseQuizNestedQuestionMaster QNQM (NOLOCK) ON QOM.OptionId = QNQM.SelectedOptionId 
      where QuestionID = @QuestionID order by SerialNo ASC`


    let sqlparam = [];
    sqlparam.push({ key: "QuestionID", value: QuestionID });
    let data = await sqlHelper.sqlquery("R", query, sqlparam);
    if (data && data.recordsets && data.recordsets.length > 0)
      result = data.recordsets[0];

    for (let index = 0; index < result.length; index++) {
      const element = result[index];
      if (element.NestedQuestionId != null) {
        element["NestedQuestion"] = await GetCourseQuizQuestionMaster(element.NestedQuestionId);
      }
    }
    return result;
  }
  catch (e) {
    console.log('Inside GetCourseQuizOptionMaster', e.toString())
  }
  finally {

  }
}

async function GetCourseQuizParentQuestionMaster(QuizID) {
  let result = [];
  try {
    let query = `
      Select 
      QQM.*,
      FM.FieldType
      FROM PBT.CourseQuizQuestionMaster QQM (NOLOCK) 
      INNER JOIN PBT.CourseQuizFieldMaster FM (NOLOCK) ON FM.FieldId = QQM.FieldID
      LEFT JOIN  PBT.CourseQuizNestedQuestionMaster QNQM (NOLOCK) ON QNQM.NestedQuestionId = QQM.QuestionID 
      where QuizID = @QuizID
      and QNQM.NestedQuestionId IS NULL
       ORDER BY QQM.SerialNo ASC`


    let sqlparam = [];
    sqlparam.push({ key: "QuizID", value: QuizID });
    let data = await sqlHelper.sqlquery("R", query, sqlparam);
    if (data && data.recordsets && data.recordsets.length > 0)
      result = data.recordsets[0]

    for (let index = 0; index < result.length; index++) {
      const element = result[index];

      element["Options"] = await GetCourseQuizOptionMaster(element.QuestionID);

    }
    return result;
  }
  catch (e) {
    console.log('Inside GetCourseQuizParentQuestionMaster', e.toString())
  }
  finally {

  }

}

async function GetCourseQuizMaster(QuizID) {
  let result = {};
  try {
    let query = `Select * from PBT.CourseQuizMaster where QuizID = @QuizID and IsActive = 1`

    let sqlparam = [];
    sqlparam.push({ key: "QuizID", value: QuizID });
    let data = await sqlHelper.sqlquery("R", query, sqlparam);
    if (data && data.recordsets && data.recordsets.length > 0)
      result = data.recordsets[0][0] || [];

    return result;

  }
  catch (e) {
    console.log('inside GetCourseQuizMaster ', e.toString())
  }
  finally {
  }

}

async function GetCourseQuizDetails(req, res) {
  let data = {};
  try {
    data = await GetCourseQuizMaster(req.query.QuizID);
    if (data) {
      data["Questions"] = await GetCourseQuizParentQuestionMaster(req.query.QuizID);
    }
    return res.status(200).json({
      status: 200,
      data: data
    });
  }
  catch (e) {
    console.log(e);
    return res.status(400).json({
      status: 400,
      message: e.toString()
    });
  }
  finally {
  }
}


//exports.GetAnswerQuizDetails = async function (req, res) {
async function GetCourseAnswerQuizDetails(req, res) {
  let data = {};
  try {
    data = await GetCourseQuizMaster(req.query.QuizId);
    if (data) {
      data["Questions"] = await GetCourseAnswerQuizParentQuestionMaster(req, req.query.QuizId);
    }
    return res.status(200).json({
      status: 200,
      data: data
    });
  }
  catch (e) {
    console.log(e);
    return res.status(400).json({
      status: 400,
      message: e
    });
  }
  finally {

  }
}

async function GetCourseAnswerQuizParentQuestionMaster(req, QuizID) {
  let result = [];
  try {
    let query = `
        Select 
        QQM.*,
        FM.FieldType
        FROM PBT.CourseQuizQuestionMaster QQM (NOLOCK) 
        INNER JOIN PBT.CourseQuizFieldMaster FM (NOLOCK) ON FM.FieldId = QQM.FieldID
        LEFT JOIN  PBT.CourseQuizNestedQuestionMaster QNQM (NOLOCK) ON QNQM.NestedQuestionId = QQM.QuestionID 
        where QuizID = @QuizID
        and QNQM.NestedQuestionId IS NULL
         ORDER BY QQM.SerialNo ASC`


    let sqlparam = [];
    sqlparam.push({ key: "QuizID", value: QuizID });
    let data = await sqlHelper.sqlquery("R", query, sqlparam);
    if (data && data.recordsets && data.recordsets.length > 0)
      result = data.recordsets[0]

    for (let index = 0; index < result.length; index++) {
      const element = result[index];
      element["Options"] = await GetCourseAnswerQuizOptionMaster(req.query.QuizMappingId, element.QuestionID);
    }
    return result;
  }
  catch (e) {

  }
  finally {

  }
}


async function GetCourseAnswerQuizOptionMaster(QuizMappingId, QuestionId) {
  // let result = [];
  try {
    let sqlparam = [];
    sqlparam.push({ key: "QuizMappingId", value: QuizMappingId });
    sqlparam.push({ key: "QuestionId", value: QuestionId });
    let result = await sqlHelper.sqlProcedure("R", "[PBT].[CourseGetQuizAnswers]", sqlparam);

    if (result && result.recordsets && result.recordsets.length > 0) {
      console.log(result.recordsets[0])
      result = result.recordsets[0]
    }
    return result;

  }
  catch (e) {
    console.log('Inside GetCourseAnswerQuizOptionMaster', e.toString())
  }
}

module.exports = {
  GetCourseQuizOptionMaster: GetCourseQuizOptionMaster,
  GetCourseQuizParentQuestionMaster: GetCourseQuizParentQuestionMaster,
  GetCourseQuizMaster: GetCourseQuizMaster,
  GetCourseQuizDetails: GetCourseQuizDetails,
  GetCourseAnswerQuizParentQuestionMaster: GetCourseAnswerQuizParentQuestionMaster,
  GetCourseAnswerQuizOptionMaster: GetCourseAnswerQuizOptionMaster
}
