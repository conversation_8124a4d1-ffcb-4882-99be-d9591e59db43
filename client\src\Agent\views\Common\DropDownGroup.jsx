
import React from "react";
import { useEffect } from "react";
import { Form } from 'react-bootstrap';
import { connect } from "react-redux";
import { getuser } from "utility/utility";
// reactstrap components

import {
    GetCommonData,GetCommonspData
} from "../../store/actions/CommonAction";



const DropDownGroup = (props)=>{
    let item;

    let { value, onChange, visible, items } = props;
        

        useEffect(()=>{
            props.GetCommonspData({
                root: props.col.config.root,
                c: "L",
               //8324
               params: [{ UserId: getuser().UserID }],
                //cols: this.props.col.config.cols,
                // con: this.props.col.config.con,
                // data: this.props.col.config.data,
                // statename: this.props.col.config.statename,
                // state: this.props.col.config.state == false ? false : true,
            });
        },[])

        // if (!items) {
        //     items = [];
        // }
        // if (visible == false) {
        //     return null;
        // }


        if(Array.isArray(props.CommonData.GetFosGroups)){
        item = props.CommonData.GetFosGroups;

        // if(item && item.length>0 && item[0] && item[0].length>0){

        //   console.log(item[0]);
        // //   item[0].map((item)=>{
        // //     console.log(item.UserID)
        // //   })
        // }
    }

    const displayoption = (item) => {
        return <option key={item.GroupId} value={item.GroupId}>{item.GroupName}</option>
    }

    return (

        <div>
        
            <Form.Control as="select" disabled={props.disabled} value={value} onChange={onChange}>
                {(props.firstoption !== false) && (
                    <option key={0} value={0}>{props.firstoption ? props.firstoption : "Select"}</option>
                )}
        
            {Array.isArray(item) && item.length>0 && Array.isArray(item[0]) && item[0].length>0 && item[0].map(item => (
                                displayoption(item)
                            ))}
        
            </Form.Control>
        </div>
        
    );
    
    
}


function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}

export default connect(
    mapStateToProps,
    {
        GetCommonspData
    }
)(DropDownGroup);





