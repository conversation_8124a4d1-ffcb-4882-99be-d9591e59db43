import React, { useEffect, FunctionComponent, Fragment } from 'react';
import {
    Route,
    Routes,
} from 'react-router-dom';

import { connect } from "react-redux";
import materialRoutes from './views/material-kit/MaterialRoutes';


const RmsApp = (props) => {

    // let token = localStorage.getItem('PBRMSToken');
    // console.log('token', token);
    let { Userid, ApplicationId, EmployeeId } = props
    console.log("RMS App is running");

    return (
        <>
        
            <Routes>
                {
                    materialRoutes && materialRoutes.map((route) => {
                        return <Route path={route.path} element={route.element}></Route>
                    })
                }

            </Routes>
        </>
    );

};

function mapStateToProps(state) {
    return {
    };
}

// export default connect<{}, {}, QuizRoutesProps>(mapStateToProps,  {
//     GetCommonspData,
//     GetCommonData
// })(QuizRoutes);

export default connect(mapStateToProps, {

})(RmsApp);
    



