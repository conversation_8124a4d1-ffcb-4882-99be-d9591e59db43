// /*!

// =========================================================
// * Paper Dashboard React - v1.1.0
// =========================================================

// * Product Page: https://www.creative-tim.com/product/paper-dashboard-react
// * Copyright 2019 Creative Tim (https://www.creative-tim.com)

// * Licensed under MIT (https://github.com/creativetimofficial/paper-dashboard-react/blob/master/LICENSE.md)

// * Coded by Creative Tim

// =========================================================

// * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

// */
// import React from "react";

// // reactstrap components
// import {
//   Card,
//   CardHeader,
//   CardBody,
//   CardTitle,
//   Row,
//   Col
// } from "reactstrap";

// class UnAuthenticated extends React.Component {
//   render() {
//     return (
//       <>
//         <div className="content">
//           <Row>
//             <Col className="ml-auto mr-auto" md="8">
//               <Card className="card-Unauthencated">
//                 <CardHeader className="text-center">
//                   <CardTitle tag="h4">Un Authenticated</CardTitle>
                  
//                 </CardHeader>
//                 <CardBody className="text-center">
//                   You are not authorized to see this page.
//                 </CardBody>
//               </Card>
//             </Col>
//           </Row>
//         </div>
//       </>
//     );
//   }
// }

// export default UnAuthenticated;
